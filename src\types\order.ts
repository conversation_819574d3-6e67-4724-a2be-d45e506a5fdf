// 订单详情
export interface OrderDetail {
  id: number
  orderId: number
  productId: number
  productName: string
  productImage: string
  productSpec: string
  quantity: number
  unitPrice: number
  discount: number
  subtotal: number
  createTime?: string
  updateTime?: string
}

// 订单视图对象 - 根据API文档完善
export interface OrderVO {
  id: number
  number: string
  buyerId: number
  addressId: number
  shippingMethodId?: number
  status: number
  amount: number
  orderTime: string
  payTime?: string
  checkoutTime?: string
  shipTime?: string
  completeTime?: string
  cancelTime?: string
  payMethod: number
  paymentTransactionId?: string
  orderRemark?: string
  cancelReason?: string
  rejectionReason?: string
  logisticsCompany?: string
  logisticsNumber?: string     // 根据文档修正字段名
  priority?: number
  source?: string
  discountAmount?: number
  shippingFee?: number
  taxAmount?: number
  // 根据文档补充字段
  userName?: string            // 用户名
  phone?: string               // 手机号
  address?: string             // 收货地址
  consignee?: string           // 收货人
  orderDetails: OrderDetail[]
  createTime?: string          // 创建时间
  updateTime?: string          // 更新时间
}

// 订单分页查询参数 - 根据文档修正
export interface OrderPageQueryDTO {
  page?: number
  pageSize?: number
  number?: string              // 订单号
  status?: number              // 订单状态
  beginTime?: string           // 开始时间（LocalDateTime）
  endTime?: string             // 结束时间（LocalDateTime）
  buyerId?: number             // 买家ID
  phone?: string               // 手机号
  // 保留扩展字段
  statusList?: number[]        // 订单状态列表（多状态查询）
  minAmount?: number           // 最小金额
  maxAmount?: number           // 最大金额
  payMethod?: number           // 支付方式
  productName?: string         // 商品名称（模糊查询）
  sortField?: string           // 排序字段
  sortOrder?: string           // 排序方向（asc/desc）
  dateRange?: string[] | null  // 日期范围（前端使用）
  orderNumber?: string         // 兼容旧字段名
}

// ==================== 基础统计类型定义 ====================

// 订单统计视图对象 - 基础统计接口
export interface OrderStatisticsVO {
  // 原有字段（兼容性）
  toBeConfirmed?: number      // 待接单数量
  confirmed?: number          // 待派送数量
  deliveryInProgress?: number // 派送中数量

  // 新增字段 - 各状态订单数量
  pendingPayment?: number     // 待付款
  paid?: number               // 已付款
  processing?: number         // 处理中
  shipped?: number            // 已发货
  completed?: number          // 已完成
  cancelled?: number          // 已取消
  refunded?: number           // 已退款

  // 总订单数和销售额
  totalOrders?: number        // 总订单数
  totalSales?: number         // 总销售额
  avgOrderAmount?: number     // 平均订单金额

  // 状态分布详情
  statusDistribution?: Array<{
    status: number
    count: number
    statusName: string
  }>

  // 时间段统计
  dailyStatistics?: Array<{
    date: string
    amount: number
    orderCount: number
  }>
  monthlyStatistics?: Array<{
    month: string
    amount: number
    orderCount: number
  }>
}

// ==================== 增强统计类型定义 ====================

// 增强版订单统计数据
export interface EnhancedOrderStatisticsVO {
  // 基础统计字段
  totalOrders: number
  totalSales: number
  avgOrderAmount: number

  // 时间维度统计
  todayOrders: number
  todaySales: number
  yesterdayOrders: number
  yesterdaySales: number
  weekOrders: number
  weekSales: number
  monthOrders: number
  monthSales: number

  // 转化率统计
  paymentRate: number        // 支付转化率（%）
  completionRate: number     // 完成率（%）
  cancellationRate: number   // 取消率（%）

  // 支付方式统计
  paymentMethodStats: PaymentMethodStatVO[]

  // 商品维度统计
  topProducts: ProductStatVO[]

  // 用户维度统计
  newUserOrders: number
  oldUserOrders: number
  userOrderFrequency: UserFrequencyStatVO[]

  // 趋势统计
  hourlyStatistics: HourlyStatVO[]
  dailyStatistics: DailyStatVO[]
  weeklyStatistics: WeeklyStatVO[]
  monthlyStatistics: MonthlyStatVO[]

  // 同比环比数据
  comparisonData: ComparisonDataVO

  // 实时统计
  realtimeStats: RealtimeStatVO
}

// ==================== 专项统计类型定义 ====================

// 时间维度统计
export interface TimeDimensionStatVO {
  today: PeriodStatVO
  yesterday: PeriodStatVO
  thisWeek: PeriodStatVO
  lastWeek: PeriodStatVO
  thisMonth: PeriodStatVO
  lastMonth: PeriodStatVO
}

// 时间段统计基础结构
export interface PeriodStatVO {
  totalOrders: number
  validOrders: number
  totalSales: number
  avgOrderAmount: number
}

// 支付方式统计
export interface PaymentMethodStatVO {
  pay_method: number
  methodName: string
  orderCount: number
  totalAmount: number
  percentage?: number
}

// 商品维度统计
export interface ProductStatVO {
  product_name: string
  totalQuantity: number
  totalAmount: number
  orderCount: number
  rank?: number
}

// 用户维度统计
export interface UserStatVO {
  newOldUserStats: Array<{
    userType: 'new' | 'old'
    orderCount: number
    totalAmount: number
  }>
  userOrderFrequency: UserFrequencyStatVO[]
}

// 用户购买频次统计
export interface UserFrequencyStatVO {
  frequency_range: string
  userCount: number
}

// 转化率统计
export interface ConversionRateStatVO {
  totalOrders: number
  paidOrders: number
  completedOrders: number
  cancelledOrders: number
  paymentRate: number
  completionRate: number
  cancellationRate: number
}

// ==================== 趋势统计类型定义 ====================

// 小时统计
export interface HourlyStatVO {
  hour: number
  amount: number
  orderCount: number
}

// 日统计
export interface DailyStatVO {
  date: string
  amount: number
  orderCount: number
}

// 周统计
export interface WeeklyStatVO {
  week: string
  amount: number
  orderCount: number
}

// 月统计
export interface MonthlyStatVO {
  month: string
  amount: number
  orderCount: number
}

// ==================== 对比统计类型定义 ====================

// 同比环比数据
export interface ComparisonDataVO {
  currentPeriod: ComparisonPeriodVO
  lastYearPeriod: ComparisonPeriodVO
  lastPeriod: ComparisonPeriodVO
  yearOverYearGrowth: GrowthRateVO
  periodOverPeriodGrowth: GrowthRateVO
}

// 对比期间数据
export interface ComparisonPeriodVO {
  totalOrders: number
  validOrders: number
  totalSales: number
  avgOrderAmount: number
}

// 增长率数据
export interface GrowthRateVO {
  salesGrowthRate: number
  orderGrowthRate: number
}

// 实时统计
export interface RealtimeStatVO {
  totalOrders: number
  totalSales: number
  pendingOrders: number
  completedOrders: number
  timestamp?: string
}

// 订单状态枚举 - 根据新API文档更新
export enum OrderStatus {
  PENDING_PAYMENT = 1,  // 待支付
  PAID = 2,             // 已支付
  CANCELLED = 3,        // 已取消
  SHIPPED = 4,          // 已发货
  COMPLETED = 5,        // 已完成
  CLOSED = 6            // 已关闭
}

// 支付方式枚举 - 根据新API文档更新
export enum PayMethod {
  WECHAT = 1,          // 微信支付
  ALIPAY = 2,          // 支付宝
  BANK_CARD = 3        // 银行卡
}

// ==================== 新增类型定义 ====================

// 发货请求参数DTO - 根据后端实际字段名
export interface ShipOrderDTO {
  orderId: number           // 订单ID
  trackingNumber: string    // 物流单号
  courierCode: string       // 快递公司代码
  courierName?: string      // 快递公司名称
  shipNote?: string         // 发货备注
}

// 快递公司信息
export interface CourierInfo {
  courier_code: string      // 快递公司代码
  courier_name: string      // 快递公司名称
  courier_phone?: string    // 快递公司电话
  courier_url?: string      // 快递公司网址
  country: string           // 国家
  country_name?: string     // 国家名称
  country_name_en?: string  // 国家英文名称
}

// 快递公司 - 根据实际后端返回数据结构修正
export interface Courier {
  code: string              // 快递公司代码
  courierCode: string       // 快递公司代码（备用）
  name: string              // 快递公司名称
  courierName: string       // 快递公司名称（备用）
  nameEn?: string           // 英文名称
  website?: string          // 官网地址
  phone?: string            // 客服电话
  country: string           // 国家代码
  active: boolean           // 是否启用
}

// 物流信息DTO（保留兼容性）
export interface OrderLogisticsDTO {
  logisticsCompany: string  // 物流公司
  trackingNumber: string    // 物流单号
}

// 订单编辑DTO
export interface OrderEditDTO {
  addressInfo?: string    // 地址信息
  remark?: string         // 备注信息
  editReason: string      // 编辑原因
}

// 订单退款DTO
export interface OrderRefundDTO {
  refundReason: string    // 退款原因
  refundAmount: string    // 退款金额
}

// 退款申请信息
export interface RefundInfo {
  id: number
  orderNumber: string
  buyerId: number
  refundAmount: number
  refundReason: string
  refundStatus: 'pending' | 'approved' | 'rejected' | 'completed'
  applyTime: string
  processTime?: string
  orderAmount?: number
  payMethod?: number
  orderTime?: string
  payTime?: string
  attachments?: Array<{
    name: string
    url: string
  }>
  processHistory?: Array<{
    action: string
    operator: string
    time: string
    remark?: string
  }>
}

// 退款查询参数
export interface RefundQueryDTO {
  page?: number
  pageSize?: number
  orderNumber?: string
  buyerId?: number
  refundStatus?: string
  beginTime?: string
  endTime?: string
}

// 退款统计数据
export interface RefundStatisticsVO {
  pending: number
  todayProcessed: number
  monthlyAmount: number
  successRate: number
}

// 批量操作DTO - 根据新API文档更新
export interface BatchOperationDTO {
  orderIds: number[]           // 订单ID列表
  logisticsCompany?: string    // 物流公司（批量发货用）
  trackingNumber?: string      // 物流单号（批量发货用）
  remark?: string              // 备注
  reason?: string              // 取消原因（批量取消用）
  exportFormat?: string        // 导出格式（批量导出用）
  exportFields?: string[]      // 导出字段（批量导出用）
}

// 批量操作结果VO
export interface BatchOperationResultVO {
  totalCount: number           // 总数量
  successCount: number         // 成功数量
  failCount: number            // 失败数量
  successOrders: string[]      // 成功的订单号列表
  failOrders: string[]         // 失败的订单号列表
  failReasons: string[]        // 失败原因列表
  downloadUrl?: string         // 下载链接
}

// 订单批量操作结果VO - 与后端接口保持一致
export interface OrderBatchOperationResultVO {
  totalCount: number           // 总数量
  successCount: number         // 成功数量
  failCount: number            // 失败数量
  successOrders: string[]      // 成功的订单号列表
  failOrders: string[]         // 失败的订单号列表
  failReasons: string[]        // 失败原因列表
  downloadUrl?: string         // 下载链接（导出时使用）
}

// 订单操作日志
export interface OrderLogVO {
  id: number
  operation: string      // 操作类型
  oldStatus?: number     // 旧状态
  newStatus: number      // 新状态
  operatorName: string   // 操作人姓名
  remark?: string        // 备注
  createTime: string     // 创建时间
}
