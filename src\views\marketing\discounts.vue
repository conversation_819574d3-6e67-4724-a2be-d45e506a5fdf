<template>
  <div class="page-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="page-header">
          <h2 class="page-title">折扣活动</h2>
          <div class="page-actions">
            <el-button type="primary" @click="openDialog('add')">
              <el-icon><Plus /></el-icon>
              <span>创建活动</span>
            </el-button>
          </div>
        </div>
      </template>

      <div class="page-content">
        <!-- 搜索过滤区域 -->
        <div class="filter-container">
          <el-form :inline="true" :model="filterForm" class="filter-form">
            <el-form-item label="活动名称">
              <el-input v-model="filterForm.name" placeholder="请输入活动名称" clearable />
            </el-form-item>
            <el-form-item label="活动状态">
              <el-select v-model="filterForm.status" placeholder="请选择状态" clearable>
                <el-option label="全部状态" value="" />
                <el-option label="未开始" value="pending" />
                <el-option label="进行中" value="active" />
                <el-option label="已结束" value="expired" />
                <el-option label="已停用" value="disabled" />
              </el-select>
            </el-form-item>
            <el-form-item label="创建时间">
              <el-date-picker
                v-model="filterForm.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :shortcuts="dateShortcuts"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">搜索</el-button>
              <el-button @click="resetFilter">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 表格区域 -->
        <el-table
          :data="tableData"
          style="width: 100%"
          v-loading="loading"
          border
          :header-cell-style="{ background: '#f5f7fa' }"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="name" label="活动名称" min-width="150" show-overflow-tooltip />
          <el-table-column label="折扣类型" width="120">
            <template #default="scope">
              <el-tag :type="getDiscountTypeTag(scope.row.type)">
                {{ getDiscountTypeText(scope.row.type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="折扣力度" width="120">
            <template #default="scope">
              <span v-if="scope.row.type === 'percentage'">{{ scope.row.value }}折</span>
              <span v-else-if="scope.row.type === 'amount'"
                >满{{ scope.row.minAmount }}减{{ scope.row.value }}</span
              >
              <span v-else>{{ scope.row.value }}元</span>
            </template>
          </el-table-column>
          <el-table-column label="活动时间" width="220">
            <template #default="scope">
              <div>{{ scope.row.startTime }} 至</div>
              <div>{{ scope.row.endTime }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="90">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="scope">
              <el-button size="small" type="primary" text @click="openDialog('edit', scope.row)">
                编辑
              </el-button>
              <el-button
                v-if="scope.row.status !== 'expired'"
                size="small"
                type="primary"
                text
                @click="handleStatus(scope.row)"
              >
                {{ scope.row.status === 'active' ? '停用' : '启用' }}
              </el-button>
              <el-button size="small" type="danger" text @click="handleDelete(scope.row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页区域 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 编辑/添加对话框 -->
    <el-dialog
      :title="dialogType === 'add' ? '创建活动' : '编辑活动'"
      v-model="dialogVisible"
      width="600px"
      destroy-on-close
    >
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="活动名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入活动名称" />
        </el-form-item>
        <el-form-item label="折扣类型" prop="type">
          <el-radio-group v-model="form.type">
            <el-radio label="percentage">折扣比例</el-radio>
            <el-radio label="amount">满减优惠</el-radio>
            <el-radio label="fixed">一口价</el-radio>
          </el-radio-group>
        </el-form-item>
        <template v-if="form.type === 'percentage'">
          <el-form-item label="折扣比例" prop="value">
            <el-input-number
              v-model="form.value"
              :min="1"
              :max="9.9"
              :precision="1"
              :step="0.1"
              style="width: 180px"
            />
            <span class="form-tip">折 (1-9.9之间)</span>
          </el-form-item>
        </template>
        <template v-else-if="form.type === 'amount'">
          <el-form-item label="满减条件" prop="minAmount">
            <el-input-number
              v-model="form.minAmount"
              :min="0"
              :precision="2"
              :step="10"
              style="width: 180px"
            />
            <span class="form-tip">元</span>
          </el-form-item>
          <el-form-item label="优惠金额" prop="value">
            <el-input-number
              v-model="form.value"
              :min="0"
              :precision="2"
              :step="10"
              style="width: 180px"
            />
            <span class="form-tip">元</span>
          </el-form-item>
        </template>
        <template v-else>
          <el-form-item label="一口价" prop="value">
            <el-input-number
              v-model="form.value"
              :min="0"
              :precision="2"
              :step="10"
              style="width: 180px"
            />
            <span class="form-tip">元</span>
          </el-form-item>
        </template>
        <el-form-item label="活动时间" prop="timeRange">
          <el-date-picker
            v-model="form.timeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="适用范围" prop="applyType">
          <el-radio-group v-model="form.applyType">
            <el-radio label="all">全部商品</el-radio>
            <el-radio label="category">指定分类</el-radio>
            <el-radio label="product">指定商品</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="form.applyType === 'category'" label="选择分类" prop="categoryIds">
          <el-select
            v-model="form.categoryIds"
            multiple
            placeholder="请选择商品分类"
            style="width: 100%"
          >
            <el-option label="电子产品" value="1" />
            <el-option label="服饰" value="2" />
            <el-option label="家居" value="3" />
            <el-option label="食品" value="4" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="form.applyType === 'product'" label="选择商品" prop="productIds">
          <el-select
            v-model="form.productIds"
            multiple
            placeholder="请选择商品"
            style="width: 100%"
          >
            <el-option label="无线蓝牙耳机" value="1" />
            <el-option label="智能手表" value="2" />
            <el-option label="便携式蓝牙音箱" value="3" />
            <el-option label="防水运动服" value="4" />
          </el-select>
        </el-form-item>
        <el-form-item label="活动说明" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入活动说明"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitLoading"> 确定 </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

// 类型定义
interface DiscountForm {
  id: number
  name: string
  type: 'percentage' | 'amount' | 'fixed'
  value: number
  minAmount: number
  timeRange: string[]
  startTime: string
  endTime: string
  applyType: 'all' | 'category' | 'product'
  categoryIds: string[]
  productIds: string[]
  description: string
}

interface Discount {
  id: number
  name: string
  type: 'percentage' | 'amount' | 'fixed'
  value: number
  minAmount: number
  startTime: string
  endTime: string
  status: 'pending' | 'active' | 'expired' | 'disabled'
  applyType: 'all' | 'category' | 'product'
  categoryIds?: string[]
  productIds?: string[]
  description: string
}

// 筛选表单
const filterForm = reactive({
  name: '',
  status: '',
  dateRange: [],
})

// 时间快捷选项
const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    },
  },
]

// 重置筛选条件
const resetFilter = () => {
  filterForm.name = ''
  filterForm.status = ''
  filterForm.dateRange = []
  handleSearch()
}

// 表格数据
const tableData = ref<Discount[]>([
  {
    id: 1,
    name: '新品上市8折',
    type: 'percentage',
    value: 8.0,
    minAmount: 0,
    startTime: '2023-10-01 00:00:00',
    endTime: '2023-12-31 23:59:59',
    status: 'active',
    applyType: 'category',
    categoryIds: ['1'],
    description: '电子产品类别8折优惠',
  },
  {
    id: 2,
    name: '满300减50',
    type: 'amount',
    value: 50,
    minAmount: 300,
    startTime: '2023-10-01 00:00:00',
    endTime: '2023-10-31 23:59:59',
    status: 'expired',
    applyType: 'all',
    description: '全场满300减50',
  },
  {
    id: 3,
    name: '限时特价',
    type: 'fixed',
    value: 99,
    minAmount: 0,
    startTime: '2023-10-15 00:00:00',
    endTime: '2023-11-15 23:59:59',
    status: 'active',
    applyType: 'product',
    productIds: ['1', '2'],
    description: '指定商品特价99元',
  },
])

// 分页相关
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)

// 对话框相关
const dialogVisible = ref(false)
const dialogType = ref<'add' | 'edit'>('add')
const submitLoading = ref(false)
const formRef = ref()

// 表单数据
const form = reactive<DiscountForm>({
  id: 0,
  name: '',
  type: 'percentage',
  value: 0,
  minAmount: 0,
  timeRange: [],
  startTime: '',
  endTime: '',
  applyType: 'all',
  categoryIds: [],
  productIds: [],
  description: '',
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入活动名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' },
  ],
  value: [{ required: true, message: '请输入折扣值', trigger: 'blur' }],
  timeRange: [{ required: true, message: '请选择活动时间', trigger: 'change' }],
  categoryIds: [{ required: true, message: '请选择商品分类', trigger: 'change' }],
  productIds: [{ required: true, message: '请选择商品', trigger: 'change' }],
}

// 获取折扣类型标签
const getDiscountTypeTag = (type: string) => {
  switch (type) {
    case 'percentage':
      return 'success'
    case 'amount':
      return 'warning'
    case 'fixed':
      return 'info'
    default:
      return ''
  }
}

// 获取折扣类型文本
const getDiscountTypeText = (type: string) => {
  switch (type) {
    case 'percentage':
      return '折扣'
    case 'amount':
      return '满减'
    case 'fixed':
      return '一口价'
    default:
      return '未知'
  }
}

// 获取状态标签类型
const getStatusType = (status: string) => {
  switch (status) {
    case 'pending':
      return 'info'
    case 'active':
      return 'success'
    case 'expired':
      return 'danger'
    case 'disabled':
      return 'warning'
    default:
      return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'pending':
      return '未开始'
    case 'active':
      return '进行中'
    case 'expired':
      return '已结束'
    case 'disabled':
      return '已停用'
    default:
      return '未知'
  }
}

// 处理搜索
const handleSearch = () => {
  loading.value = true
  currentPage.value = 1
  // 模拟API请求
  setTimeout(() => {
    // 这里应该是实际的API请求，根据筛选条件获取数据
    loading.value = false
  }, 500)
}

// 打开对话框
const openDialog = (type: 'add' | 'edit', row?: Discount) => {
  dialogType.value = type

  if (type === 'add') {
    // 重置表单
    Object.assign(form, {
      id: 0,
      name: '',
      type: 'percentage',
      value: 0,
      minAmount: 0,
      timeRange: [],
      startTime: '',
      endTime: '',
      applyType: 'all',
      categoryIds: [],
      productIds: [],
      description: '',
    })
  } else if (type === 'edit' && row) {
    // 填充表单数据
    Object.assign(form, {
      id: row.id,
      name: row.name,
      type: row.type,
      value: row.value,
      minAmount: row.minAmount,
      timeRange: [row.startTime, row.endTime],
      startTime: row.startTime,
      endTime: row.endTime,
      applyType: row.applyType,
      categoryIds: row.categoryIds || [],
      productIds: row.productIds || [],
      description: row.description,
    })
  }

  dialogVisible.value = true
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return

  await formRef.value.validate((valid: boolean) => {
    if (valid) {
      submitLoading.value = true

      // 处理时间范围
      if (form.timeRange && form.timeRange.length === 2) {
        form.startTime = form.timeRange[0]
        form.endTime = form.timeRange[1]
      }

      // 模拟API请求
      setTimeout(() => {
        if (dialogType.value === 'add') {
          // 添加新活动
          const newDiscount: Discount = {
            ...form,
            id: Date.now(),
            status: new Date(form.startTime) > new Date() ? 'pending' : 'active',
            categoryIds: form.applyType === 'category' ? form.categoryIds : undefined,
            productIds: form.applyType === 'product' ? form.productIds : undefined,
          }
          tableData.value.unshift(newDiscount)
          ElMessage.success('添加成功')
        } else {
          // 更新活动信息
          const index = tableData.value.findIndex((item) => item.id === form.id)
          if (index !== -1) {
            const originalStatus = tableData.value[index].status

            tableData.value[index] = {
              ...form,
              status: originalStatus,
              categoryIds: form.applyType === 'category' ? form.categoryIds : undefined,
              productIds: form.applyType === 'product' ? form.productIds : undefined,
            }
            ElMessage.success('更新成功')
          }
        }

        submitLoading.value = false
        dialogVisible.value = false
      }, 500)
    }
  })
}

// 处理状态变更
const handleStatus = (row: Discount) => {
  const newStatus = row.status === 'active' ? 'disabled' : 'active'
  const actionText = newStatus === 'active' ? '启用' : '停用'

  ElMessageBox.confirm(`确定要${actionText}活动 "${row.name}" 吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      // 模拟API请求
      loading.value = true
      setTimeout(() => {
        row.status = newStatus
        loading.value = false
        ElMessage.success(`${actionText}成功`)
      }, 500)
    })
    .catch(() => {
      // 用户取消操作
    })
}

// 处理删除
const handleDelete = (row: Discount) => {
  ElMessageBox.confirm(`确定要删除活动 "${row.name}" 吗?`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      // 模拟API请求
      loading.value = true
      setTimeout(() => {
        tableData.value = tableData.value.filter((item) => item.id !== row.id)
        loading.value = false
        ElMessage.success('删除成功')
      }, 500)
    })
    .catch(() => {
      // 用户取消操作
    })
}

// 处理分页大小变更
const handleSizeChange = (val: number) => {
  pageSize.value = val
  handleSearch()
}

// 处理页码变更
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  handleSearch()
}

// 页面加载时获取数据
onMounted(() => {
  handleSearch()
})
</script>

<style scoped lang="scss">
.page-container {
  padding: 16px;

  .page-card {
    :deep(.el-card__header) {
      padding: 16px 20px;
    }

    :deep(.el-card__body) {
      padding: 20px;
    }
  }

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .page-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }

    .page-actions {
      display: flex;
      gap: 12px;
    }
  }

  .filter-container {
    margin-bottom: 20px;
    padding: 16px;
    background-color: #f9f9f9;
    border-radius: 4px;

    .filter-form {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;

      :deep(.el-form-item) {
        margin-bottom: 0;
        margin-right: 16px;
      }

      :deep(.el-date-editor--daterange) {
        width: 260px;
      }
    }
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }

  .form-tip {
    margin-left: 10px;
    color: #909399;
  }
}

// 响应式调整
@media screen and (max-width: 768px) {
  .page-container {
    .filter-container {
      .filter-form {
        flex-direction: column;

        :deep(.el-form-item) {
          margin-right: 0;
          margin-bottom: 10px;
          width: 100%;
        }

        :deep(.el-date-editor--daterange) {
          width: 100%;
        }
      }
    }
  }
}
</style>
