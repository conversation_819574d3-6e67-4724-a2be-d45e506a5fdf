/**
 * 退款管理API接口
 */

import request from '@/utils/request'
import type {
  RefundQueryDTO,
  RefundApprovalDTO,
  RefundApplicationVO,
  RefundStatistics,
  PageResult
} from '@/types/refund'

// ==================== 退款查询相关 ====================

/**
 * 分页查询退款申请
 */
export const getRefundList = (params: RefundQueryDTO): Promise<{ code: number; data: PageResult<RefundApplicationVO>; msg?: string }> => {
  return request.get('/admin/refund/page', { params })
}

/**
 * 查询待审核的退款申请
 */
export const getPendingRefunds = (page: number = 1, pageSize: number = 10): Promise<{ code: number; data: PageResult<RefundApplicationVO>; msg?: string }> => {
  return request.get('/admin/refund/pending', {
    params: { page, pageSize }
  })
}

/**
 * 查询退款申请详情
 */
export const getRefundById = (refundApplicationId: number): Promise<{ code: number; data: RefundApplicationVO; msg?: string }> => {
  return request.get(`/admin/refund/${refundApplicationId}`)
}

/**
 * 根据退款单号查询详情
 */
export const getRefundByNo = (refundNo: string): Promise<{ code: number; data: RefundApplicationVO; msg?: string }> => {
  return request.get(`/admin/refund/no/${refundNo}`)
}

// ==================== 退款处理相关 ====================

/**
 * 审核退款申请
 */
export const approveRefund = (approvalData: RefundApprovalDTO): Promise<{ code: number; data: string; msg?: string }> => {
  return request.post('/admin/refund/approve', approvalData)
}

/**
 * 手动处理退款
 */
export const processRefund = (refundApplicationId: number): Promise<{ code: number; data: string; msg?: string }> => {
  return request.post(`/admin/refund/process/${refundApplicationId}`)
}

// ==================== 退款统计相关 ====================

/**
 * 查询退款统计信息
 */
export const getRefundStatistics = (startTime?: string, endTime?: string): Promise<{ code: number; data: RefundStatistics; msg?: string }> => {
  const params: any = {}
  if (startTime) params.startTime = startTime
  if (endTime) params.endTime = endTime

  return request.get('/admin/refund/statistics', { params })
}

// ==================== 辅助函数 ====================

/**
 * 格式化退款查询参数
 */
export const formatRefundQuery = (query: Partial<RefundQueryDTO>): RefundQueryDTO => {
  const formatted: RefundQueryDTO = {
    page: query.page || 1,
    pageSize: query.pageSize || 20
  }

  // 只添加有值的参数
  if (query.refundNo) formatted.refundNo = query.refundNo
  if (query.orderId) formatted.orderId = query.orderId
  if (query.orderNumber) formatted.orderNumber = query.orderNumber
  if (query.buyerId) formatted.buyerId = query.buyerId
  if (query.applicationStatus !== undefined) formatted.applicationStatus = query.applicationStatus
  if (query.refundType !== undefined) formatted.refundType = query.refundType
  if (query.needApproval !== undefined) formatted.needApproval = query.needApproval
  if (query.approvalStatus !== undefined) formatted.approvalStatus = query.approvalStatus
  if (query.beginTime) formatted.beginTime = query.beginTime
  if (query.endTime) formatted.endTime = query.endTime

  return formatted
}

/**
 * 批量审核退款申请
 */
export const batchApproveRefunds = (refundIds: number[], approvalResult: number, approvalRemark?: string): Promise<{ code: number; data: any; msg?: string }> => {
  const promises = refundIds.map(id =>
    approveRefund({
      refundApplicationId: id,
      approvalResult,
      approvalRemark
    })
  )
  
  return Promise.all(promises).then(results => {
    const successCount = results.filter(r => r.code === 1).length
    const failCount = results.length - successCount
    
    return {
      code: failCount === 0 ? 1 : 0,
      data: {
        total: results.length,
        success: successCount,
        fail: failCount
      },
      msg: failCount === 0 ? '批量审核成功' : `批量审核完成，成功${successCount}个，失败${failCount}个`
    }
  })
}

/**
 * 批量处理退款
 */
export const batchProcessRefunds = (refundIds: number[]): Promise<{ code: number; data: any; msg?: string }> => {
  const promises = refundIds.map(id => processRefund(id))
  
  return Promise.all(promises).then(results => {
    const successCount = results.filter(r => r.code === 1).length
    const failCount = results.length - successCount
    
    return {
      code: failCount === 0 ? 1 : 0,
      data: {
        total: results.length,
        success: successCount,
        fail: failCount
      },
      msg: failCount === 0 ? '批量处理成功' : `批量处理完成，成功${successCount}个，失败${failCount}个`
    }
  })
}

// ==================== 导出功能 ====================

/**
 * 导出退款数据
 */
export const exportRefundData = (query: RefundQueryDTO, format: string = 'excel'): Promise<{ code: number; data: string; msg?: string }> => {
  return request.post('/admin/refund/export', query, {
    params: { format },
    responseType: 'blob'
  }).then(response => {
    // 处理文件下载
    const blob = new Blob([response.data])
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `退款数据_${new Date().toISOString().slice(0, 10)}.${format}`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    return {
      code: 1,
      data: '导出成功',
      msg: '文件已开始下载'
    }
  })
}

// ==================== 实时数据 ====================

/**
 * 获取退款实时统计
 */
export const getRealtimeRefundStats = (): Promise<{ code: number; data: any; msg?: string }> => {
  return request.get('/admin/refund/realtime-stats')
}

/**
 * 获取退款趋势数据
 */
export const getRefundTrend = (days: number = 7): Promise<{ code: number; data: any; msg?: string }> => {
  return request.get('/admin/refund/trend', {
    params: { days }
  })
}
