<template>
  <el-dialog
    v-model="visible"
    title="物流详情"
    width="1000px"
    :before-close="handleClose"
    class="tracking-detail-dialog"
  >
    <div v-loading="loading" class="tracking-detail-content">
      <div v-if="trackingDetail" class="tracking-info">
        <!-- 基本信息 -->
        <div class="info-section">
          <h3>基本信息</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">物流单号：</span>
                <span class="value">{{ trackingDetail.trackingNumber }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">运输商：</span>
                <span class="value">{{ trackingDetail.carrierName }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">订单号：</span>
                <span class="value">{{ trackingDetail.orderNumber || '无' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">物流状态：</span>
                <el-tag :type="getLogisticsStatusType(trackingDetail.status)" size="small">
                  {{ getLogisticsStatusText(trackingDetail.status) }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">跟踪状态：</span>
                <el-tag :type="getTrackingStatusType(trackingDetail.trackingStatus)" size="small">
                  {{ getTrackingStatusText(trackingDetail.trackingStatus) }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">注册时间：</span>
                <span class="value">{{ trackingDetail.registerTime }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 时间信息 -->
        <div class="info-section">
          <h3>时间信息</h3>
          <el-row :gutter="20">
            <el-col :span="8" v-if="trackingDetail.pickupTime">
              <div class="info-item">
                <span class="label">揽收时间：</span>
                <span class="value">{{ trackingDetail.pickupTime }}</span>
              </div>
            </el-col>
            <el-col :span="8" v-if="trackingDetail.deliveryTime">
              <div class="info-item">
                <span class="label">签收时间：</span>
                <span class="value">{{ trackingDetail.deliveryTime }}</span>
              </div>
            </el-col>
            <el-col :span="8" v-if="trackingDetail.trackTime">
              <div class="info-item">
                <span class="label">最后跟踪：</span>
                <span class="value">{{ trackingDetail.trackTime }}</span>
              </div>
            </el-col>
            <el-col :span="8" v-if="trackingDetail.latestEventTime">
              <div class="info-item">
                <span class="label">最新事件时间：</span>
                <span class="value">{{ trackingDetail.latestEventTime }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 最新事件 -->
        <div class="info-section" v-if="trackingDetail.latestEventInfo">
          <h3>最新事件</h3>
          <div class="event-content">
            {{ trackingDetail.latestEventInfo }}
          </div>
        </div>

        <!-- 物流轨迹 -->
        <div class="info-section" v-if="trackingDetail.events && trackingDetail.events.length > 0">
          <h3>物流轨迹</h3>
          <el-timeline>
            <el-timeline-item
              v-for="event in trackingDetail.events"
              :key="event.id"
              :timestamp="formatEventTime(event.eventTime)"
              placement="top"
              :type="getEventType(event)"
            >
              <div class="timeline-content">
                <div class="timeline-header">
                  <span class="event-desc">{{ event.eventDescription }}</span>
                  <el-tag
                    v-if="event.eventStatus"
                    :type="getEventStatusType(event.eventStatus)"
                    size="small"
                  >
                    {{ event.eventStatus }}
                  </el-tag>
                </div>
                <div class="timeline-location" v-if="event.eventLocation">
                  <el-icon><Location /></el-icon>
                  {{ event.eventLocation }}
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>

        <!-- 扩展信息 -->
        <div class="info-section" v-if="hasExtendedInfo">
          <h3>扩展信息</h3>
          <el-row :gutter="20">
            <el-col :span="8" v-if="trackingDetail.originCountry">
              <div class="info-item">
                <span class="label">发货国家：</span>
                <span class="value">{{ trackingDetail.originCountry }}</span>
              </div>
            </el-col>
            <el-col :span="8" v-if="trackingDetail.destinationCountry">
              <div class="info-item">
                <span class="label">目的地国家：</span>
                <span class="value">{{ trackingDetail.destinationCountry }}</span>
              </div>
            </el-col>
            <el-col :span="8" v-if="trackingDetail.subStatus">
              <div class="info-item">
                <span class="label">子状态：</span>
                <span class="value">{{ trackingDetail.subStatusDesc || trackingDetail.subStatus }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          v-if="trackingDetail && trackingDetail.trackingStatus === 'Tracking'"
          type="primary"
          @click="handleSync"
          :loading="syncing"
        >
          同步状态
        </el-button>
        <el-button
          v-if="trackingDetail && trackingDetail.trackingStatus === 'Tracking'"
          type="warning"
          @click="handleStop"
        >
          停止跟踪
        </el-button>
        <el-button
          v-if="trackingDetail && trackingDetail.trackingStatus === 'Stopped'"
          type="success"
          @click="handleRestart"
        >
          重启跟踪
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Location } from '@element-plus/icons-vue'
import { getTrackingDetail, syncTrackingStatus, stopTracking, restartTracking } from '@/api/tracking'
import type { TrackingRecord, TrackingDetailVO, TrackingEventVO } from '@/types/tracking'
import {
  getLogisticsStatusText,
  getLogisticsStatusType,
  getTrackingStatusText,
  getTrackingStatusType,
  formatEventTime
} from '@/types/tracking'

interface Props {
  modelValue: boolean
  tracking: TrackingRecord | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'refresh'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = ref(false)
const loading = ref(false)
const syncing = ref(false)
const trackingDetail = ref<TrackingDetailVO | null>(null)

// 计算属性
const hasExtendedInfo = computed(() => {
  if (!trackingDetail.value) return false
  return trackingDetail.value.originCountry || 
         trackingDetail.value.destinationCountry || 
         trackingDetail.value.subStatus
})

// 监听显示状态
watch(
  () => props.modelValue,
  (newVal) => {
    visible.value = newVal
    if (newVal && props.tracking) {
      fetchTrackingDetail()
    }
  }
)

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 获取物流详情
const fetchTrackingDetail = async () => {
  if (!props.tracking) return

  loading.value = true
  try {
    const response = await getTrackingDetail(props.tracking.trackingNumber, props.tracking.carrierCode)
    if (response.code === 1) {
      trackingDetail.value = response.data
    } else {
      ElMessage.error(response.msg || '获取物流详情失败')
    }
  } catch (error) {
    console.error('获取物流详情失败:', error)
    ElMessage.error('获取物流详情失败')
  } finally {
    loading.value = false
  }
}

// 获取事件类型
const getEventType = (event: TrackingEventVO): string => {
  if (event.eventDescription.includes('签收') || event.eventDescription.includes('delivered')) {
    return 'success'
  } else if (event.eventDescription.includes('异常') || event.eventDescription.includes('exception')) {
    return 'danger'
  } else if (event.eventDescription.includes('派送') || event.eventDescription.includes('delivery')) {
    return 'warning'
  }
  return 'primary'
}

// 获取事件状态类型
const getEventStatusType = (status: string): string => {
  if (status.includes('success') || status.includes('delivered')) {
    return 'success'
  } else if (status.includes('exception') || status.includes('failed')) {
    return 'danger'
  } else if (status.includes('transit') || status.includes('processing')) {
    return 'warning'
  }
  return 'info'
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  trackingDetail.value = null
}

// 同步状态
const handleSync = async () => {
  if (!props.tracking) return

  syncing.value = true
  try {
    const response = await syncTrackingStatus(props.tracking.trackingNumber, props.tracking.carrierCode)
    if (response.code === 1) {
      ElMessage.success('同步成功')
      emit('refresh')
      fetchTrackingDetail() // 重新获取详情
    } else {
      ElMessage.error(response.msg || '同步失败')
    }
  } catch (error) {
    console.error('同步失败:', error)
    ElMessage.error('同步失败')
  } finally {
    syncing.value = false
  }
}

// 停止跟踪
const handleStop = async () => {
  if (!props.tracking) return

  try {
    await ElMessageBox.confirm('确认停止跟踪该物流单号？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const response = await stopTracking(props.tracking.trackingNumber, props.tracking.carrierCode)
    if (response.code === 1) {
      ElMessage.success('停止跟踪成功')
      emit('refresh')
      handleClose()
    } else {
      ElMessage.error(response.msg || '停止跟踪失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('停止跟踪失败:', error)
      ElMessage.error('停止跟踪失败')
    }
  }
}

// 重启跟踪
const handleRestart = async () => {
  if (!props.tracking) return

  try {
    const response = await restartTracking(props.tracking.trackingNumber, props.tracking.carrierCode)
    if (response.code === 1) {
      ElMessage.success('重启跟踪成功')
      emit('refresh')
      handleClose()
    } else {
      ElMessage.error(response.msg || '重启跟踪失败')
    }
  } catch (error) {
    console.error('重启跟踪失败:', error)
    ElMessage.error('重启跟踪失败')
  }
}
</script>

<style scoped lang="scss">
.tracking-detail-dialog {
  .tracking-detail-content {
    max-height: 70vh;
    overflow-y: auto;
  }

  .tracking-info {
    .info-section {
      margin-bottom: 30px;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 8px;

      h3 {
        margin: 0 0 20px 0;
        padding-bottom: 10px;
        border-bottom: 2px solid #409eff;
        color: #303133;
        font-size: 16px;
        font-weight: 600;
      }

      .info-item {
        margin-bottom: 15px;
        display: flex;
        align-items: center;

        .label {
          font-weight: 500;
          color: #606266;
          min-width: 120px;
          margin-right: 10px;
        }

        .value {
          color: #303133;
          flex: 1;
        }
      }

      .event-content {
        background: white;
        padding: 15px;
        border-radius: 4px;
        border: 1px solid #dcdfe6;
        color: #606266;
        line-height: 1.6;
      }
    }

    .timeline-content {
      .timeline-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;

        .event-desc {
          font-weight: 500;
          color: #303133;
          flex: 1;
        }
      }

      .timeline-location {
        color: #909399;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 5px;
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .tracking-detail-dialog {
    .tracking-info {
      .info-section {
        padding: 15px;

        .info-item {
          flex-direction: column;
          align-items: flex-start;

          .label {
            min-width: auto;
            margin-bottom: 5px;
          }
        }
      }

      .timeline-content {
        .timeline-header {
          flex-direction: column;
          align-items: flex-start;
          gap: 8px;
        }
      }
    }
  }
}
</style>
