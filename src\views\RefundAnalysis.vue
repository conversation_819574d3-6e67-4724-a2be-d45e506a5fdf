<template>
  <div class="refund-analysis-container">
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">退货情况分析</h1>
        <div class="page-subtitle">查看退货率、退货原因及商家退货情况</div>
      </div>
      <div class="header-right">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="fetchRefundData"
        />
        <el-button type="primary" @click="exportData">导出数据</el-button>
      </div>
    </div>

    <!-- 退货统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card shadow="hover" class="stats-card">
            <div class="stats-info">
              <div class="stats-label">总退货率</div>
              <div class="stats-value">{{ refundStats.refundRate }}%</div>
              <div
                class="stats-trend"
                :class="refundStats.refundRateTrend <= 0 ? 'trend-up' : 'trend-down'"
              >
                <el-icon v-if="refundStats.refundRateTrend <= 0"><ArrowDown /></el-icon>
                <el-icon v-else><ArrowUp /></el-icon>
                {{ Math.abs(refundStats.refundRateTrend) }}% 较上期
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stats-card">
            <div class="stats-info">
              <div class="stats-label">退货金额</div>
              <div class="stats-value">¥{{ refundStats.refundAmount }}</div>
              <div
                class="stats-trend"
                :class="refundStats.refundAmountTrend <= 0 ? 'trend-up' : 'trend-down'"
              >
                <el-icon v-if="refundStats.refundAmountTrend <= 0"><ArrowDown /></el-icon>
                <el-icon v-else><ArrowUp /></el-icon>
                {{ Math.abs(refundStats.refundAmountTrend) }}% 较上期
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stats-card">
            <div class="stats-info">
              <div class="stats-label">退货订单数</div>
              <div class="stats-value">{{ refundStats.refundOrderCount }}</div>
              <div
                class="stats-trend"
                :class="refundStats.refundOrderTrend <= 0 ? 'trend-up' : 'trend-down'"
              >
                <el-icon v-if="refundStats.refundOrderTrend <= 0"><ArrowDown /></el-icon>
                <el-icon v-else><ArrowUp /></el-icon>
                {{ Math.abs(refundStats.refundOrderTrend) }}% 较上期
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stats-card">
            <div class="stats-info">
              <div class="stats-label">平均处理时间</div>
              <div class="stats-value">{{ refundStats.avgProcessTime }}小时</div>
              <div
                class="stats-trend"
                :class="refundStats.avgProcessTimeTrend <= 0 ? 'trend-up' : 'trend-down'"
              >
                <el-icon v-if="refundStats.avgProcessTimeTrend <= 0"><ArrowDown /></el-icon>
                <el-icon v-else><ArrowUp /></el-icon>
                {{ Math.abs(refundStats.avgProcessTimeTrend) }}% 较上期
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 退货趋势 -->
    <el-card shadow="hover" class="trend-card">
      <template #header>
        <div class="card-header">
          <span>退货趋势</span>
          <div class="header-actions">
            <el-radio-group v-model="trendType" size="small" @change="updateTrendChart">
              <el-radio-button label="daily">日视图</el-radio-button>
              <el-radio-button label="weekly">周视图</el-radio-button>
              <el-radio-button label="monthly">月视图</el-radio-button>
            </el-radio-group>
          </div>
        </div>
      </template>
      <div class="trend-chart-container" style="height: 300px; margin-top: 20px">
        <!-- 图表区域 - 实际项目中会使用ECharts等图表库 -->
        <div class="mock-chart">
          <div class="chart-line-container">
            <div class="chart-line refund-rate-line"></div>
            <div class="chart-line refund-amount-line"></div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 退货原因分析 -->
    <el-card shadow="hover" class="reason-card">
      <template #header>
        <div class="card-header">
          <span>退货原因分析</span>
        </div>
      </template>
      <div class="reason-analysis">
        <div class="reason-chart" style="width: 40%">
          <!-- 饼图区域 -->
          <div class="mock-pie-chart"></div>
        </div>
        <div class="reason-table" style="width: 60%">
          <el-table :data="refundReasons" style="width: 100%">
            <el-table-column prop="reason" label="退货原因" />
            <el-table-column prop="count" label="数量" sortable />
            <el-table-column prop="percentage" label="占比">
              <template #default="scope">
                <el-progress :percentage="scope.row.percentage" :stroke-width="10" />
              </template>
            </el-table-column>
            <el-table-column prop="amount" label="金额" sortable />
          </el-table>
        </div>
      </div>
    </el-card>

    <!-- 商家退货排行 -->
    <el-card shadow="hover" class="merchant-refund-card">
      <template #header>
        <div class="card-header">
          <span>商家退货率排行</span>
          <div class="header-actions">
            <el-select v-model="sortType" placeholder="排序方式" @change="updateMerchantList">
              <el-option label="按退货率排序" value="rate" />
              <el-option label="按退货金额排序" value="amount" />
              <el-option label="按退货订单数排序" value="count" />
            </el-select>
          </div>
        </div>
      </template>
      <div class="merchant-table-container">
        <el-table :data="merchantRefundList" style="width: 100%">
          <el-table-column type="index" label="排名" width="60" />
          <el-table-column prop="name" label="商家名称" min-width="180" />
          <el-table-column prop="refundRate" label="退货率" sortable>
            <template #default="scope">
              <el-progress
                :percentage="scope.row.refundRate"
                :color="getRefundRateColor(scope.row.refundRate)"
                :stroke-width="10"
              />
            </template>
          </el-table-column>
          <el-table-column prop="refundAmount" label="退货金额" sortable />
          <el-table-column prop="refundCount" label="退货订单数" sortable />
          <el-table-column prop="mainReason" label="主要原因" />
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="scope">
              <el-button type="primary" size="small" @click="viewMerchantRefund(scope.row.id)">
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ArrowUp, ArrowDown } from '@element-plus/icons-vue'

const router = useRouter()
const dateRange = ref([])
const trendType = ref('daily')
const sortType = ref('rate')

// 退货统计数据
const refundStats = reactive({
  refundRate: 4.8,
  refundRateTrend: -0.5,
  refundAmount: '28,560',
  refundAmountTrend: -2.3,
  refundOrderCount: 158,
  refundOrderTrend: -3.7,
  avgProcessTime: 8.5,
  avgProcessTimeTrend: -1.2,
})

// 退货原因数据
const refundReasons = ref([
  { reason: '质量问题', count: 65, percentage: 41.1, amount: '¥12,685' },
  { reason: '商品损坏', count: 38, percentage: 24.1, amount: '¥8,456' },
  { reason: '尺寸不合适', count: 22, percentage: 13.9, amount: '¥3,689' },
  { reason: '不符合预期', count: 18, percentage: 11.4, amount: '¥2,875' },
  { reason: '客户不需要', count: 15, percentage: 9.5, amount: '¥855' },
])

// 商家退货数据
const merchantRefundList = ref([
  {
    id: 1,
    name: '电子科技专营店',
    refundRate: 3.8,
    refundAmount: '¥5,856',
    refundCount: 28,
    mainReason: '质量问题',
  },
  {
    id: 2,
    name: '优品家居旗舰店',
    refundRate: 4.2,
    refundAmount: '¥4,235',
    refundCount: 21,
    mainReason: '商品损坏',
  },
  {
    id: 3,
    name: '时尚服饰品牌店',
    refundRate: 5.1,
    refundAmount: '¥3,856',
    refundCount: 19,
    mainReason: '尺寸不合适',
  },
  {
    id: 4,
    name: '全球美妆专卖',
    refundRate: 2.9,
    refundAmount: '¥2,586',
    refundCount: 15,
    mainReason: '不符合预期',
  },
  {
    id: 5,
    name: '健康食品直营店',
    refundRate: 1.8,
    refundAmount: '¥1,856',
    refundCount: 9,
    mainReason: '质量问题',
  },
  {
    id: 6,
    name: '办公用品商城',
    refundRate: 3.2,
    refundAmount: '¥1,735',
    refundCount: 8,
    mainReason: '质量问题',
  },
  {
    id: 7,
    name: '运动户外专营',
    refundRate: 4.5,
    refundAmount: '¥1,586',
    refundCount: 7,
    mainReason: '尺寸不合适',
  },
  {
    id: 8,
    name: '母婴用品旗舰店',
    refundRate: 6.2,
    refundAmount: '¥1,423',
    refundCount: 6,
    mainReason: '质量问题',
  },
])

// 根据退货率获取颜色
const getRefundRateColor = (rate: number) => {
  if (rate <= 3) return '#67C23A'
  if (rate <= 5) return '#E6A23C'
  return '#F56C6C'
}

// 查看商家退货详情
const viewMerchantRefund = (merchantId: number) => {
  router.push(`/merchant-refund/${merchantId}`)
}

// 获取退货数据
const fetchRefundData = () => {
  console.log('Fetching refund data for date range:', dateRange.value)
  // 实际项目中会调用API获取数据
}

// 更新趋势图
const updateTrendChart = () => {
  console.log('Updating trend chart to type:', trendType.value)
  // 实际项目中会更新图表
}

// 更新商家列表
const updateMerchantList = () => {
  console.log('Updating merchant list with sort type:', sortType.value)
  // 实际项目中会根据排序类型更新列表
}

// 导出数据
const exportData = () => {
  console.log('Exporting refund data')
  // 实际项目中会实现导出功能
}

onMounted(() => {
  // 初始化日期范围为最近30天
  const end = new Date()
  const start = new Date()
  start.setDate(start.getDate() - 30)
  dateRange.value = [start, end]

  // 获取初始数据
  fetchRefundData()
})
</script>

<style scoped lang="scss">
.refund-analysis-container {
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #eef2f5 100%);
  border-radius: 12px;
  min-height: calc(100vh - 40px);

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    .header-left {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        margin: 0 0 8px 0;
        background: linear-gradient(120deg, #3a7bd5, #2c5499);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .page-subtitle {
        font-size: 14px;
        color: #606266;
      }
    }

    .header-right {
      display: flex;
      gap: 16px;
    }
  }

  .stats-cards {
    margin-bottom: 24px;

    .stats-card {
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;
      padding: 20px;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
      }

      .stats-info {
        text-align: center;

        .stats-label {
          font-size: 16px;
          color: #606266;
          margin-bottom: 10px;
        }

        .stats-value {
          font-size: 28px;
          font-weight: 600;
          color: #303133;
          margin-bottom: 10px;
        }

        .stats-trend {
          display: inline-flex;
          align-items: center;
          font-size: 14px;

          &.trend-up {
            color: #67c23a;
          }

          &.trend-down {
            color: #f56c6c;
          }
        }
      }
    }
  }

  .trend-card,
  .reason-card,
  .merchant-refund-card {
    margin-bottom: 24px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 16px;
      font-weight: 600;
      padding: 16px 20px;

      .header-actions {
        display: flex;
        gap: 16px;
      }
    }
  }

  .trend-chart-container {
    padding: 0 20px 20px;

    .mock-chart {
      width: 100%;
      height: 100%;
      position: relative;
      border-bottom: 1px solid #ebeef5;

      .chart-line-container {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;

        .chart-line {
          width: 100%;
          height: 2px;
          position: relative;

          &.refund-rate-line {
            background: linear-gradient(90deg, #67c23a, #e6a23c, #f56c6c);
            margin-bottom: 50px;

            &::before {
              content: '';
              position: absolute;
              top: -2px;
              width: 100%;
              height: 40px;
              background: linear-gradient(to top, rgba(103, 194, 58, 0.1), transparent);
            }
          }

          &.refund-amount-line {
            background: linear-gradient(90deg, #409eff, #64b5f6);

            &::before {
              content: '';
              position: absolute;
              top: -2px;
              width: 100%;
              height: 40px;
              background: linear-gradient(to top, rgba(64, 158, 255, 0.1), transparent);
            }
          }
        }
      }
    }
  }

  .reason-analysis {
    display: flex;
    padding: 20px;
    min-height: 300px;

    .reason-chart {
      display: flex;
      align-items: center;
      justify-content: center;

      .mock-pie-chart {
        width: 200px;
        height: 200px;
        border-radius: 50%;
        background: conic-gradient(
          #f56c6c 0% 41%,
          #e6a23c 41% 65%,
          #409eff 65% 79%,
          #67c23a 79% 90%,
          #909399 90% 100%
        );
        position: relative;

        &::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 80px;
          height: 80px;
          border-radius: 50%;
          background: white;
        }
      }
    }

    .reason-table {
      padding-left: 20px;
    }
  }

  .merchant-table-container {
    padding: 0 20px 20px;
  }
}

@media screen and (max-width: 768px) {
  .refund-analysis-container {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-right {
        width: 100%;
      }
    }

    .stats-cards .el-col {
      margin-bottom: 16px;
    }

    .reason-analysis {
      flex-direction: column;

      .reason-chart,
      .reason-table {
        width: 100% !important;
      }

      .reason-chart {
        margin-bottom: 20px;
      }
    }
  }
}
</style>
