<template>
  <div class="refund-approval-test">
    <el-card>
      <template #header>
        <h3>退款审核功能测试</h3>
      </template>

      <div class="test-section">
        <h4>测试场景</h4>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card class="test-card">
              <template #header>
                <h5>审核通过测试</h5>
              </template>
              <div class="test-content">
                <p>测试审核通过流程，验证：</p>
                <ul>
                  <li>✅ 提交数据格式正确</li>
                  <li>✅ 包含退款方式和金额</li>
                  <li>✅ 审核人姓名自动填充</li>
                  <li>✅ 错误处理机制</li>
                </ul>
                <el-button type="success" @click="testApprovalPass" :loading="testing.pass">
                  <el-icon><CircleCheck /></el-icon>
                  测试审核通过
                </el-button>
              </div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card class="test-card">
              <template #header>
                <h5>审核拒绝测试</h5>
              </template>
              <div class="test-content">
                <p>测试审核拒绝流程，验证：</p>
                <ul>
                  <li>✅ 提交数据格式正确</li>
                  <li>✅ 不包含退款方式和金额</li>
                  <li>✅ 审核备注必填</li>
                  <li>✅ 错误处理机制</li>
                </ul>
                <el-button type="danger" @click="testApprovalReject" :loading="testing.reject">
                  <el-icon><Close /></el-icon>
                  测试审核拒绝
                </el-button>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <div class="test-section">
        <h4>错误处理测试</h4>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-button type="warning" @click="testInvalidData" :loading="testing.invalid">
              <el-icon><Warning /></el-icon>
              测试无效数据
            </el-button>
          </el-col>
          <el-col :span="8">
            <el-button type="info" @click="testNetworkError" :loading="testing.network">
              <el-icon><Connection /></el-icon>
              测试网络错误
            </el-button>
          </el-col>
          <el-col :span="8">
            <el-button type="primary" @click="testResponseFormat" :loading="testing.format">
              <el-icon><Document /></el-icon>
              测试响应格式
            </el-button>
          </el-col>
        </el-row>
      </div>

      <div class="test-section">
        <h4>测试结果</h4>
        <div class="test-results">
          <el-timeline>
            <el-timeline-item
              v-for="(result, index) in testResults"
              :key="index"
              :timestamp="result.timestamp"
              :type="result.type"
            >
              <div class="result-content">
                <h6>{{ result.title }}</h6>
                <p>{{ result.message }}</p>
                <pre v-if="result.data">{{ JSON.stringify(result.data, null, 2) }}</pre>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { CircleCheck, Close, Warning, Connection, Document } from '@element-plus/icons-vue'
import { approveRefund } from '@/api/refund'
import type { RefundApprovalDTO } from '@/types/refund'
import { useUserStore } from '@/stores/user'

// 用户信息
const userStore = useUserStore()

// 测试状态
const testing = reactive({
  pass: false,
  reject: false,
  invalid: false,
  network: false,
  format: false
})

// 测试结果
const testResults = ref<Array<{
  timestamp: string
  type: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  title: string
  message: string
  data?: any
}>>([])

// 添加测试结果
const addTestResult = (type: 'primary' | 'success' | 'warning' | 'danger' | 'info', title: string, message: string, data?: any) => {
  testResults.value.unshift({
    timestamp: new Date().toLocaleString(),
    type,
    title,
    message,
    data
  })
}

// 测试审核通过
const testApprovalPass = async () => {
  testing.pass = true
  try {
    const submitData: RefundApprovalDTO = {
      refundApplicationId: 1,
      approvalResult: 1, // 通过
      approvalRemark: '商品质量问题属实，同意退款申请',
      approverName: userStore.userInfo?.accountName || '测试管理员',
      refundMethod: 1, // 原路退回
      actualRefundAmount: 299.99
    }

    console.log('测试审核通过 - 提交数据:', submitData)
    addTestResult('info', '审核通过测试开始', '正在提交审核通过请求...', submitData)

    const response = await approveRefund(submitData)
    console.log('测试审核通过 - 响应:', response)

    if (response && response.code === 1) {
      ElMessage.success('审核通过测试成功')
      addTestResult('success', '审核通过测试成功', response.data || '审核通过', response)
    } else {
      ElMessage.error(response?.msg || '审核通过测试失败')
      addTestResult('warning', '审核通过测试失败', response?.msg || '未知错误', response)
    }
  } catch (error: any) {
    console.error('测试审核通过失败:', error)
    ElMessage.error('审核通过测试异常')
    addTestResult('danger', '审核通过测试异常', error.message || '网络错误', error)
  } finally {
    testing.pass = false
  }
}

// 测试审核拒绝
const testApprovalReject = async () => {
  testing.reject = true
  try {
    const submitData: RefundApprovalDTO = {
      refundApplicationId: 2,
      approvalResult: 2, // 拒绝
      approvalRemark: '申请理由不充分，不符合退款条件',
      approverName: userStore.userInfo?.accountName || '测试管理员'
      // 注意：拒绝时不包含退款方式和金额
    }

    console.log('测试审核拒绝 - 提交数据:', submitData)
    addTestResult('info', '审核拒绝测试开始', '正在提交审核拒绝请求...', submitData)

    const response = await approveRefund(submitData)
    console.log('测试审核拒绝 - 响应:', response)

    if (response && response.code === 1) {
      ElMessage.success('审核拒绝测试成功')
      addTestResult('success', '审核拒绝测试成功', response.data || '审核拒绝', response)
    } else {
      ElMessage.error(response?.msg || '审核拒绝测试失败')
      addTestResult('warning', '审核拒绝测试失败', response?.msg || '未知错误', response)
    }
  } catch (error: any) {
    console.error('测试审核拒绝失败:', error)
    ElMessage.error('审核拒绝测试异常')
    addTestResult('danger', '审核拒绝测试异常', error.message || '网络错误', error)
  } finally {
    testing.reject = false
  }
}

// 测试无效数据
const testInvalidData = async () => {
  testing.invalid = true
  try {
    const submitData = {
      refundApplicationId: null, // 无效ID
      approvalResult: 999, // 无效状态
      approvalRemark: '',
      approverName: ''
    }

    console.log('测试无效数据 - 提交数据:', submitData)
    addTestResult('info', '无效数据测试开始', '正在提交无效数据...', submitData)

    const response = await approveRefund(submitData as any)
    console.log('测试无效数据 - 响应:', response)

    addTestResult('warning', '无效数据测试完成', '服务器响应', response)
  } catch (error: any) {
    console.error('测试无效数据失败:', error)
    addTestResult('danger', '无效数据测试异常', error.message || '网络错误', error)
  } finally {
    testing.invalid = false
  }
}

// 测试网络错误
const testNetworkError = async () => {
  testing.network = true
  try {
    // 模拟网络错误
    addTestResult('info', '网络错误测试开始', '模拟网络连接失败...')
    
    // 这里可以通过修改API地址来模拟网络错误
    ElMessage.warning('网络错误测试：请检查网络连接')
    addTestResult('warning', '网络错误测试', '模拟网络连接失败场景')
  } catch (error: any) {
    addTestResult('danger', '网络错误测试异常', error.message || '未知错误', error)
  } finally {
    testing.network = false
  }
}

// 测试响应格式
const testResponseFormat = async () => {
  testing.format = true
  try {
    addTestResult('info', '响应格式测试开始', '验证API响应格式...')
    
    const submitData: RefundApprovalDTO = {
      refundApplicationId: 999,
      approvalResult: 1,
      approvalRemark: '格式测试',
      approverName: '测试用户'
    }

    const response = await approveRefund(submitData)
    
    // 验证响应格式
    const hasCode = typeof response?.code === 'number'
    const hasData = response?.data !== undefined
    const hasMsg = typeof response?.msg === 'string' || response?.msg === undefined

    const formatCheck = {
      hasCode,
      hasData,
      hasMsg,
      isValid: hasCode && (hasData || hasMsg)
    }

    addTestResult('info', '响应格式验证', '检查API响应格式', formatCheck)
    
    if (formatCheck.isValid) {
      ElMessage.success('响应格式验证通过')
    } else {
      ElMessage.warning('响应格式可能存在问题')
    }
  } catch (error: any) {
    addTestResult('danger', '响应格式测试异常', error.message || '网络错误', error)
  } finally {
    testing.format = false
  }
}
</script>

<style scoped lang="scss">
.refund-approval-test {
  padding: 20px;

  .test-section {
    margin-bottom: 30px;

    h4 {
      margin: 0 0 20px 0;
      color: #303133;
      border-bottom: 2px solid #409eff;
      padding-bottom: 8px;
    }
  }

  .test-card {
    height: 100%;

    h5 {
      margin: 0;
      color: #303133;
    }

    .test-content {
      p {
        margin-bottom: 10px;
        color: #606266;
      }

      ul {
        margin: 10px 0;
        padding-left: 20px;

        li {
          margin-bottom: 5px;
          color: #909399;
        }
      }

      .el-button {
        width: 100%;
        margin-top: 15px;
      }
    }
  }

  .test-results {
    max-height: 500px;
    overflow-y: auto;
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;

    .result-content {
      h6 {
        margin: 0 0 8px 0;
        color: #303133;
      }

      p {
        margin: 0 0 8px 0;
        color: #606266;
      }

      pre {
        background: #fff;
        padding: 10px;
        border-radius: 4px;
        font-size: 12px;
        overflow-x: auto;
        margin: 0;
      }
    }
  }
}
</style>
