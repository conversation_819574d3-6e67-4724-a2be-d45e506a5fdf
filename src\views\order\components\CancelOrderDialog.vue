<template>
  <el-dialog
    v-model="visible"
    title="取消订单"
    width="500px"
    :before-close="handleClose"
  >
    <div class="cancel-order-content">
      <div v-if="order" class="order-info">
        <div class="info-item">
          <span class="label">订单号：</span>
          <span class="value">{{ order.number }}</span>
        </div>
        <div class="info-item">
          <span class="label">订单金额：</span>
          <span class="value amount">¥{{ order.amount }}</span>
        </div>
        <div class="info-item">
          <span class="label">订单状态：</span>
          <el-tag :type="getStatusType(order.status)">
            {{ getStatusText(order.status) }}
          </el-tag>
        </div>
      </div>

      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        class="cancel-form"
      >
        <el-form-item label="取消原因" prop="cancelReason" required>
          <el-select
            v-model="form.cancelReason"
            placeholder="请选择取消原因"
            style="width: 100%"
          >
            <el-option label="买家要求取消" value="买家要求取消" />
            <el-option label="商品缺货" value="商品缺货" />
            <el-option label="价格错误" value="价格错误" />
            <el-option label="系统错误" value="系统错误" />
            <el-option label="其他原因" value="其他原因" />
          </el-select>
        </el-form-item>
        
        <el-form-item 
          v-if="form.cancelReason === '其他原因'" 
          label="详细说明" 
          prop="customReason"
        >
          <el-input
            v-model="form.customReason"
            type="textarea"
            :rows="3"
            placeholder="请输入详细的取消原因"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <div class="warning-notice">
        <el-alert
          title="注意事项"
          type="warning"
          :closable="false"
          show-icon
        >
          <template #default>
            <ul>
              <li>取消订单后将无法恢复</li>
              <li>如果买家已付款，系统将自动退款</li>
              <li>取消原因将记录在订单中</li>
            </ul>
          </template>
        </el-alert>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="danger" 
          @click="handleConfirm"
          :loading="loading"
        >
          确认取消订单
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { cancelOrder } from '@/api/order'
import type { OrderVO } from '@/types/order'

interface Props {
  modelValue: boolean
  order: OrderVO | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'refresh'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = ref(false)
const loading = ref(false)
const formRef = ref<FormInstance>()

// 表单数据
const form = reactive({
  cancelReason: '',
  customReason: ''
})

// 表单验证规则
const rules: FormRules = {
  cancelReason: [
    { required: true, message: '请选择取消原因', trigger: 'change' }
  ],
  customReason: [
    { required: true, message: '请输入详细的取消原因', trigger: 'blur' },
    { min: 5, max: 200, message: '长度在 5 到 200 个字符', trigger: 'blur' }
  ]
}

// 监听显示状态
watch(
  () => props.modelValue,
  (newVal) => {
    visible.value = newVal
    if (newVal) {
      resetForm()
    }
  }
)

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 重置表单
const resetForm = () => {
  form.cancelReason = ''
  form.customReason = ''
  formRef.value?.clearValidate()
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  resetForm()
}

// 确认取消订单
const handleConfirm = async () => {
  if (!props.order) return
  
  try {
    const valid = await formRef.value?.validate()
    if (!valid) return
    
    await ElMessageBox.confirm(
      '确认要取消这个订单吗？此操作不可撤销。',
      '确认取消',
      {
        confirmButtonText: '确定取消',
        cancelButtonText: '再想想',
        type: 'warning'
      }
    )
    
    loading.value = true
    
    // 确定最终的取消原因
    const finalReason = form.cancelReason === '其他原因' 
      ? form.customReason 
      : form.cancelReason
    
    const response = await cancelOrder({ id: props.order.id, cancelReason: finalReason })
    
    if (response.code === 1) {
      ElMessage.success('订单已取消')
      emit('refresh')
      handleClose()
    } else {
      ElMessage.error(response.msg || '取消订单失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消订单失败:', error)
      ElMessage.error('取消订单失败')
    }
  } finally {
    loading.value = false
  }
}

// 获取状态类型
const getStatusType = (status: number) => {
  const statusMap: Record<number, string> = {
    1: 'warning',  // 待付款
    2: 'info',     // 已付款
    3: 'primary',  // 处理中
    4: 'success',  // 已发货
    5: 'success',  // 已完成
    6: 'danger',   // 已取消
    7: 'info'      // 已退款
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    1: '待付款',
    2: '已付款',
    3: '处理中',
    4: '已发货',
    5: '已完成',
    6: '已取消',
    7: '已退款'
  }
  return statusMap[status] || '未知'
}
</script>

<style scoped lang="scss">
.cancel-order-content {
  .order-info {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 6px;
    margin-bottom: 20px;
    
    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .label {
        color: #606266;
        font-size: 14px;
        min-width: 80px;
      }
      
      .value {
        color: #303133;
        font-size: 14px;
        
        &.amount {
          color: #f56c6c;
          font-weight: 600;
        }
      }
    }
  }
  
  .cancel-form {
    margin-bottom: 20px;
  }
  
  .warning-notice {
    :deep(.el-alert__content) {
      ul {
        margin: 0;
        padding-left: 20px;
        
        li {
          margin-bottom: 4px;
          color: #e6a23c;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>

<script lang="ts">
export default {
  name: 'CancelOrderDialog'
}
</script>
