<template>
  <div class="page-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="page-header">
          <h2 class="page-title">商家详情信息</h2>
          <div class="page-actions">
            <el-button @click="debugData" type="primary" size="small">调试数据</el-button>
            <el-button @click="showRawData" type="info" size="small">显示原始数据</el-button>
            <el-button @click="manualFixData" type="warning" size="small" v-if="showDebug"
              >修复结构</el-button
            >
            <el-button @click="goBack" icon="ArrowLeft">返回</el-button>
          </div>
        </div>
      </template>

      <!-- 调试区域 -->
      <div v-if="showDebug" class="debug-area">
        <h3>数据结构调试</h3>
        <el-alert
          v-if="!sellerDetail?.seller?.id || !sellerDetail?.evpi?.id"
          title="检测到数据结构问题"
          type="warning"
          description="系统未检测到完整的嵌套数据结构(seller和evpi)，或其中缺少必要字段。可以点击'修复结构'按钮尝试修复数据结构。"
          show-icon
          :closable="false"
          class="mb-10"
        />
        <el-alert
          v-else
          title="数据结构正常"
          type="success"
          description="系统已检测到正确的嵌套数据结构(seller和evpi)"
          show-icon
          :closable="false"
          class="mb-10"
        />
        <el-descriptions title="数据预览" :column="1" border>
          <el-descriptions-item label="sellerDetail类型">
            {{ typeof sellerDetail }}
          </el-descriptions-item>
          <el-descriptions-item label="seller对象">
            {{ Boolean(sellerDetail?.seller) ? '存在' : '不存在' }}
            <el-tag v-if="sellerDetail?.seller?.id" type="success" size="small" class="ml-10">
              ID: {{ sellerDetail?.seller?.id }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="evpi对象">
            {{ Boolean(sellerDetail?.evpi) ? '存在' : '不存在' }}
            <el-tag v-if="sellerDetail?.evpi?.id" type="success" size="small" class="ml-10">
              ID: {{ sellerDetail?.evpi?.id }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="数据值">
            <pre style="max-height: 200px; overflow: auto; font-size: 12px">{{
              JSON.stringify(sellerDetail, null, 2)
            }}</pre>
          </el-descriptions-item>
        </el-descriptions>
        <el-divider></el-divider>
      </div>

      <!-- 原始数据显示区域 -->
      <div v-if="showRaw" class="debug-area">
        <h3>原始数据结构</h3>
        <el-card shadow="never">
          <pre style="max-height: 400px; overflow: auto; font-size: 12px">{{
            JSON.stringify(rawData, null, 2)
          }}</pre>
        </el-card>
        <el-divider></el-divider>
      </div>

      <div class="page-content" v-loading="loading">
        <pre style="display: none">{{ JSON.stringify(sellerDetail, null, 2) }}</pre>
        <template v-if="sellerDetail">
          <!-- 卖家账户信息 -->
          <el-descriptions title="卖家账户信息" :column="3" border>
            <el-descriptions-item label="商家ID">
              {{ adapt(sellerDetail, 'seller.id') }}
            </el-descriptions-item>
            <el-descriptions-item label="账户名">
              {{ adapt(sellerDetail, 'seller.accountName') }}
            </el-descriptions-item>
            <el-descriptions-item label="性别">
              {{
                adapt(sellerDetail, 'seller.gender') === 1
                  ? '男'
                  : adapt(sellerDetail, 'seller.gender') === 2
                    ? '女'
                    : '未知'
              }}
            </el-descriptions-item>
            <el-descriptions-item label="电子邮箱">
              {{ adapt(sellerDetail, 'seller.email') }}
            </el-descriptions-item>
            <el-descriptions-item label="联系电话">
              {{ adapt(sellerDetail, 'seller.phone') }}
            </el-descriptions-item>
            <el-descriptions-item label="账户状态">
              <el-tag :type="getStatusType(adapt(sellerDetail, 'seller.accountStatus'))">
                {{ getStatusText(adapt(sellerDetail, 'seller.accountStatus')) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="注册时间">
              {{ formatDate(adapt(sellerDetail, 'seller.createTime')) }}
            </el-descriptions-item>
            <el-descriptions-item
              label="最近登录时间"
              v-if="adapt(sellerDetail, 'seller.lastLoginTime')"
            >
              {{ formatDate(adapt(sellerDetail, 'seller.lastLoginTime')) }}
            </el-descriptions-item>
          </el-descriptions>

          <!-- 企业基本信息 -->
          <el-descriptions title="企业基本信息" :column="2" border class="mt-20">
            <el-descriptions-item label="店铺名称">
              {{ adapt(sellerDetail, 'evpi.shopName') }}
            </el-descriptions-item>
            <el-descriptions-item label="公司名称">
              {{ adapt(sellerDetail, 'evpi.companyName') }}
            </el-descriptions-item>
            <el-descriptions-item label="联系人">
              {{ adapt(sellerDetail, 'evpi.contactPerson') }}
            </el-descriptions-item>
            <el-descriptions-item label="联系电话">
              {{ adapt(sellerDetail, 'evpi.contactPhone') }}
            </el-descriptions-item>
            <el-descriptions-item label="营业执照号">
              {{ adapt(sellerDetail, 'evpi.businessLicense') }}
            </el-descriptions-item>
            <el-descriptions-item label="执照有效期">
              {{ adapt(sellerDetail, 'evpi.licenseValidity') }}
            </el-descriptions-item>
            <el-descriptions-item label="公司介绍" :span="2">
              {{ adapt(sellerDetail, 'evpi.companyIntro', '无') }}
            </el-descriptions-item>
          </el-descriptions>

          <!-- 公司地址信息 -->
          <el-descriptions title="公司地址" :column="1" border class="mt-20" v-if="hasAddress">
            <el-descriptions-item label="详细地址">
              <div class="address-detail">
                <span class="address-part">{{ adapt(sellerDetail, 'evpi.province', '') }}</span>
                <span class="address-part">{{ adapt(sellerDetail, 'evpi.city', '') }}</span>
                <span class="address-part">{{ adapt(sellerDetail, 'evpi.district', '') }}</span>
                <span class="address-part highlight">
                  {{ adapt(sellerDetail, 'evpi.addressDetail', '') }}
                </span>
              </div>
            </el-descriptions-item>
          </el-descriptions>

          <!-- 证照资质图片 -->
          <div class="image-section mt-20">
            <h3 class="section-title">证照资质图片</h3>
            <div class="image-grid">
              <!-- 营业执照 -->
              <div class="image-card" v-if="adapt(sellerDetail, 'evpi.businessImgUrl')">
                <div class="image-title">营业执照</div>
                <el-image
                  :src="adapt(sellerDetail, 'evpi.businessImgUrl')"
                  fit="cover"
                  :preview-src-list="[adapt(sellerDetail, 'evpi.businessImgUrl')]"
                  class="certificate-image"
                />
              </div>

              <!-- 身份证正面 -->
              <div class="image-card" v-if="adapt(sellerDetail, 'evpi.idcard1')">
                <div class="image-title">身份证正面</div>
                <el-image
                  :src="adapt(sellerDetail, 'evpi.idcard1')"
                  fit="cover"
                  :preview-src-list="[adapt(sellerDetail, 'evpi.idcard1')]"
                  class="certificate-image"
                />
              </div>

              <!-- 身份证反面 -->
              <div class="image-card" v-if="adapt(sellerDetail, 'evpi.idcard2')">
                <div class="image-title">身份证反面</div>
                <el-image
                  :src="adapt(sellerDetail, 'evpi.idcard2')"
                  fit="cover"
                  :preview-src-list="[adapt(sellerDetail, 'evpi.idcard2')]"
                  class="certificate-image"
                />
              </div>
            </div>
          </div>

          <!-- 仓库照片 -->
          <div class="image-section mt-20">
            <h3 class="section-title">仓库照片</h3>
            <div class="image-grid">
              <!-- 仓库照片1 -->
              <div class="image-card" v-if="adapt(sellerDetail, 'evpi.warehouseImgUrl1')">
                <div class="image-title">仓库照片1</div>
                <el-image
                  :src="adapt(sellerDetail, 'evpi.warehouseImgUrl1')"
                  fit="cover"
                  :preview-src-list="[adapt(sellerDetail, 'evpi.warehouseImgUrl1')]"
                  class="certificate-image"
                />
              </div>

              <!-- 仓库照片2 -->
              <div class="image-card" v-if="adapt(sellerDetail, 'evpi.warehouseImgUrl2')">
                <div class="image-title">仓库照片2</div>
                <el-image
                  :src="adapt(sellerDetail, 'evpi.warehouseImgUrl2')"
                  fit="cover"
                  :preview-src-list="[adapt(sellerDetail, 'evpi.warehouseImgUrl2')]"
                  class="certificate-image"
                />
              </div>

              <!-- 仓库照片3 -->
              <div class="image-card" v-if="adapt(sellerDetail, 'evpi.warehouseImgUrl3')">
                <div class="image-title">仓库照片3</div>
                <el-image
                  :src="adapt(sellerDetail, 'evpi.warehouseImgUrl3')"
                  fit="cover"
                  :preview-src-list="[adapt(sellerDetail, 'evpi.warehouseImgUrl3')]"
                  class="certificate-image"
                />
              </div>
            </div>
          </div>

          <!-- 审核操作区域 -->
          
        </template>
      </div>
    </el-card>

    <!-- 审核通过对话框 -->
    <el-dialog v-model="approveDialogVisible" title="审核通过" width="30%">
      <el-form :model="approveForm" label-width="100px">
        <el-form-item label="审核备注">
          <el-input
            v-model="approveForm.remark"
            type="textarea"
            rows="3"
            placeholder="请输入审核备注(可选)"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="approveDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmApprove" :loading="submitting">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 审核拒绝对话框 -->
    <el-dialog v-model="rejectDialogVisible" title="审核拒绝" width="30%">
      <el-form :model="rejectForm" label-width="100px">
        <el-form-item label="拒绝原因" required>
          <el-input
            v-model="rejectForm.remark"
            type="textarea"
            rows="3"
            placeholder="请输入拒绝原因"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="rejectDialogVisible = false">取消</el-button>
          <el-button type="danger" @click="confirmReject" :loading="submitting">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Check, Close, ArrowLeft } from '@element-plus/icons-vue'
import { getSellerById, updateSellerStatus } from '@/api/sellerAdmin'

// 状态定义
const route = useRoute()
const router = useRouter()
const loading = ref(true)
const submitting = ref(false)
const sellerDetail = ref<any>(null)
const showDebug = ref(false)
const showRaw = ref(false)
const rawData = ref<any>(null)

// 对话框控制
const approveDialogVisible = ref(false)
const rejectDialogVisible = ref(false)

// 审核表单
const approveForm = reactive({
  remark: '',
})

const rejectForm = reactive({
  remark: '',
})

// 格式化日期
const formatDate = (dateStr?: string) => {
  if (!dateStr) return '-'
  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}

// 获取状态文本
const getStatusText = (status?: number) => {
  if (status === undefined) return '未知'
  switch (status) {
    case 0:
      return '待审核'
    case 1:
      return '已通过'
    case 2:
      return '已拒绝'
    default:
      return '未知'
  }
}

// 获取状态类型
const getStatusType = (status?: number) => {
  if (status === undefined) return 'info'
  switch (status) {
    case 0:
      return 'warning'
    case 1:
      return 'success'
    case 2:
      return 'danger'
    default:
      return 'info'
  }
}

// 适配器函数，从任何结构中获取值
const adapt = (data: any, path: string, defaultValue: any = '-') => {
  if (!data) return defaultValue

  // 处理嵌套路径 (例如 "seller.id" 或 "evpi.shopName")
  if (path.includes('.')) {
    const [obj, field] = path.split('.')

    // 直接访问对象属性ttp://localhost:8888/sellers/11
    if (data[obj] && data[obj][field] !== undefined) {
      return data[obj][field]
    }

    // 如果是扁平结构
    if (data[field] !== undefined) {
      return data[field]
    }
  } else {
    // 直接字段
    if (data[path] !== undefined) {
      return data[path]
    }
  }

  // 添加调试信息
  console.log(`未找到路径: ${path}, 返回默认值: ${defaultValue}`)
  return defaultValue
}

// 是否有地址信息
const hasAddress = computed(() => {
  const province = adapt(sellerDetail, 'evpi.province', '')
  const city = adapt(sellerDetail, 'evpi.city', '')
  const district = adapt(sellerDetail, 'evpi.district', '')
  const addressDetail = adapt(sellerDetail, 'evpi.addressDetail', '')

  return Boolean(province || city || district || addressDetail)
})

// 判断数据是否为嵌套结构
const isNestedStructure = computed(() => {
  const data = sellerDetail.value
  return data ? Boolean(data.seller && data.evpi) : false
})

// 根据ID获取商家详情
const fetchSellerDetail = async () => {
  const sellerId = route.params.id
  if (!sellerId) {
    ElMessage.error('商家ID无效')
    goBack()
    return
  }

  loading.value = true
  try {
    // 在详情页中，总是获取详细信息
    console.log(`正在请求商家详情信息，ID: ${sellerId}，请求URL: /sellers/detail/${sellerId}`)
    const response = await getSellerById(Number(sellerId))
    console.log('获取商家详情响应:', response)

    if (response.code === 1 && response.data) {
      sellerDetail.value = response.data
      console.log('设置的详情数据:', sellerDetail.value)

      // 检查数据结构并修复（如果需要）
      if (!sellerDetail.value?.seller || !sellerDetail.value?.evpi) {
        console.log('数据结构不完整，尝试自动修复')
        manualFixData()
      }
    } else {
      ElMessage.error(response.msg || '获取商家详情失败')
      goBack()
    }
  } catch (error) {
    console.error('获取商家详情失败:', error)
    ElMessage.error('获取商家详情失败，请稍后重试')
    goBack()
  } finally {
    loading.value = false
  }
}

// 从扁平的或不完整的数据构建嵌套数据结构
const manualConstructData = (data: any) => {
  // 已有嵌套结构但不完整的情况
  if (data.seller || data.evpi) {
    sellerDetail.value = data
    return
  }

  // 创建完整的嵌套结构
  const constructedData = {
    seller: data.seller || {},
    evpi: data.evpi || {},
  }

  // 如果seller和evpi字段为空，可能是扁平结构
  if (Object.keys(constructedData.seller).length === 0) {
    constructedData.seller = {
      id: data.id || data.sellerId,
      accountName: data.accountName,
      gender: data.gender,
      phone: data.phone,
      email: data.email,
      accountStatus: data.accountStatus,
      photoUrl: data.photoUrl,
      createTime: data.createTime,
      lastLoginTime: data.lastLoginTime,
      password: data.password,
      roles: data.roles || [],
      verificationCode: data.verificationCode,
    }
  }

  if (Object.keys(constructedData.evpi).length === 0) {
    constructedData.evpi = {
      id: data.evpiId || data.id,
      shopName: data.shopName,
      companyName: data.companyName,
      businessLicense: data.businessLicense,
      licenseValidity: data.licenseValidity,
      companyIntro: data.companyIntro,
      contactPerson: data.contactPerson,
      contactPhone: data.contactPhone,
      province: data.province,
      city: data.city,
      district: data.district,
      addressDetail: data.addressDetail,
      createdTime: data.createdTime,
      lastUpdated: data.lastUpdated,
      businessImgUrl: data.businessImgUrl,
      warehouseImgUrl1: data.warehouseImgUrl1,
      warehouseImgUrl2: data.warehouseImgUrl2,
      warehouseImgUrl3: data.warehouseImgUrl3,
      sellerId: data.id || data.sellerId,
      idcard1: data.idcard1,
      idcard2: data.idcard2,
      idcard: data.idcard,
    }
  }

  sellerDetail.value = constructedData
  console.log('构建的嵌套数据结构:', constructedData)
}

// 手动修复数据结构
const manualFixData = () => {
  if (!sellerDetail.value) {
    ElMessage.error('没有可修复的数据')
    return
  }

  try {
    console.log('开始修复数据结构...')
    console.log('原始数据:', sellerDetail.value)

    // 创建副本以避免直接修改原对象
    const originalData = JSON.parse(JSON.stringify(sellerDetail.value))

    // 处理完整响应格式
    if (originalData.code !== undefined && originalData.data) {
      console.log('检测到标准API响应格式，提取data字段')
      sellerDetail.value = originalData.data
      console.log('从响应中提取data字段成功:', sellerDetail.value)

      // 递归调用继续处理
      manualFixData()
      return
    }

    // 特殊情况：处理直接的seller对象（没有嵌套结构的情况）
    if (originalData.id && originalData.accountName && !originalData.seller && !originalData.evpi) {
      console.log('检测到直接的seller对象，构建嵌套结构')

      // 把当前对象作为seller
      const sellerObj = { ...originalData }

      // 构建evpi对象（从其他位置获取或创建空对象）
      const evpiObj = {
        id: sellerObj.id, // 使用相同的ID
        sellerId: sellerObj.id,
        shopName: sellerObj.shopName || '',
        companyName: sellerObj.companyName || '',
        businessLicense: '',
        licenseValidity: '',
        companyIntro: '',
        contactPerson: '',
        contactPhone: sellerObj.phone || '',
        province: '',
        city: '',
        district: '',
        addressDetail: '',
        createdTime: sellerObj.createTime || '',
        lastUpdated: sellerObj.lastLoginTime || '',
        businessImgUrl: '',
        warehouseImgUrl1: '',
        warehouseImgUrl2: '',
        warehouseImgUrl3: '',
        idcard1: '',
        idcard2: '',
        idcard: '',
      }

      // 建立新的嵌套结构
      sellerDetail.value = {
        seller: sellerObj,
        evpi: evpiObj,
      }

      console.log('构建的嵌套结构:', sellerDetail.value)
      ElMessage.success('已自动构建数据结构，但缺少企业信息')
      return
    }

    // 检查已有的数据结构并尝试修复
    if (originalData.data && typeof originalData.data === 'object') {
      // data字段中可能包含完整数据
      if (originalData.data.seller && originalData.data.evpi) {
        sellerDetail.value = originalData.data
        console.log('从data字段中提取数据成功')
        ElMessage.success('成功从data字段中提取数据')
        return
      }
    }

    // 尝试应用手动构建逻辑
    manualConstructData(originalData)
    ElMessage.success('数据结构修复成功')
  } catch (error) {
    console.error('手动修复数据失败:', error)
    ElMessage.error('修复失败，请查看控制台')
  }
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 审核通过
const handleApprove = () => {
  const sellerId = adapt(sellerDetail, 'seller.id') || adapt(sellerDetail, 'id')
  if (!sellerId) {
    ElMessage.error('商家信息不完整')
    return
  }
  approveForm.remark = ''
  approveDialogVisible.value = true
}

// 确认通过
const confirmApprove = async () => {
  const sellerId = adapt(sellerDetail, 'seller.id') || adapt(sellerDetail, 'id')
  if (!sellerId) {
    ElMessage.error('商家信息不完整')
    return
  }

  submitting.value = true
  try {
    const response = await updateSellerStatus(sellerId, 1, approveForm.remark)
    if (response.code === 1) {
      ElMessage.success('审核通过成功')
      approveDialogVisible.value = false
      goBack()
    } else {
      ElMessage.error(response.msg || '操作失败')
    }
  } catch (error) {
    console.error('审核通过出错:', error)
    ElMessage.error('操作失败，请稍后重试')
  } finally {
    submitting.value = false
  }
}

// 审核拒绝
const handleReject = () => {
  const sellerId = adapt(sellerDetail, 'seller.id') || adapt(sellerDetail, 'id')
  if (!sellerId) {
    ElMessage.error('商家信息不完整')
    return
  }
  rejectForm.remark = ''
  rejectDialogVisible.value = true
}

// 确认拒绝
const confirmReject = async () => {
  const sellerId = adapt(sellerDetail, 'seller.id') || adapt(sellerDetail, 'id')
  if (!sellerId || !rejectForm.remark.trim()) {
    ElMessage.error(rejectForm.remark.trim() ? '商家信息不完整' : '请输入拒绝原因')
    return
  }

  submitting.value = true
  try {
    await ElMessageBox.confirm('确定要拒绝该商家的审核申请吗？', '拒绝确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    const response = await updateSellerStatus(sellerId, 2, rejectForm.remark)
    if (response.code === 1) {
      ElMessage.success('已拒绝商家审核')
      rejectDialogVisible.value = false
      goBack()
    } else {
      ElMessage.error(response.msg || '操作失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('拒绝操作失败:', error)
      ElMessage.error('拒绝操作失败，请稍后重试')
    }
  } finally {
    submitting.value = false
  }
}

// 调试函数
const debugData = () => {
  showDebug.value = !showDebug.value
  console.log('当前数据:', sellerDetail.value)
  console.log('seller数据:', sellerDetail.value?.seller)
  console.log('evpi数据:', sellerDetail.value?.evpi)
}

// 显示原始数据
const showRawData = () => {
  showRaw.value = !showRaw.value
  rawData.value = sellerDetail.value
  console.log('显示原始数据:', rawData.value)
}

onMounted(() => {
  fetchSellerDetail()
})
</script>

<style scoped lang="scss">
.page-container {
  padding: 16px;

  .page-card {
    :deep(.el-card__header) {
      padding: 16px 20px;
    }

    :deep(.el-card__body) {
      padding: 20px;
    }
  }

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .page-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
    }

    .page-actions {
      display: flex;
      gap: 12px;
    }
  }

  .mt-20 {
    margin-top: 20px;
  }

  .address-detail {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;

    .address-part {
      &.highlight {
        color: #409eff;
        font-weight: bold;
      }
    }
  }

  .image-section {
    border: 1px solid #ebeef5;
    border-radius: 4px;
    padding: 20px;
    background-color: #ffffff;

    .section-title {
      margin-top: 0;
      margin-bottom: 20px;
      font-size: 16px;
      font-weight: 600;
      padding-bottom: 10px;
      border-bottom: 1px solid #ebeef5;
    }

    .image-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 20px;

      .image-card {
        border: 1px solid #ebeef5;
        border-radius: 4px;
        overflow: hidden;

        .image-title {
          padding: 8px 12px;
          background-color: #f5f7fa;
          font-size: 14px;
          font-weight: 500;
        }

        .certificate-image {
          width: 100%;
          height: 200px;
          object-fit: cover;
        }
      }
    }
  }

  .action-section {
    text-align: center;
    padding: 20px 0;

    .action-buttons {
      display: flex;
      justify-content: center;
      gap: 20px;
    }
  }
}
</style>
