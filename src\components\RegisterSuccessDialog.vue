<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    width="480px"
    center
    class="register-success-dialog"
    top="25vh"
  >
    <div class="success-container">
      <div class="success-icon-wrapper">
        <el-icon class="success-icon-bg">
          <circle-check />
        </el-icon>
      </div>

      <h2 class="success-title">注册申请已提交</h2>

      <div class="success-content">
        <p>您的注册信息已收到，平台会在<span class="highlight">24小时内</span>审核完成</p>
        <p>感谢您耐心等待，审核结果将通过短信通知您</p>
      </div>

      <div class="success-info">
        <div class="info-item">
          <el-icon><timer /></el-icon>
          <span>审核时间：24小时内</span>
        </div>
        <div class="info-item">
          <el-icon><message /></el-icon>
          <span>通知方式：短信/邮件</span>
        </div>
        <div class="info-item">
          <el-icon><question-filled /></el-icon>
          <span>遇到问题？联系客服 400-123-4567</span>
        </div>
      </div>

      <el-button type="primary" class="confirm-button" @click="handleConfirm">
        返回登录页面
      </el-button>

      <div class="countdown">
        <span>{{ countdown }}秒后自动跳转</span>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { CircleCheck, Timer, Message, QuestionFilled } from '@element-plus/icons-vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['confirm', 'update:visible'])

const countdown = ref(5)
let timer: number | null = null

const handleConfirm = () => {
  if (timer) {
    window.clearInterval(timer)
    timer = null
  }
  emit('confirm')
}

onMounted(() => {
  if (props.visible) {
    startCountdown()
  }
})

const startCountdown = () => {
  timer = window.setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      if (timer) {
        window.clearInterval(timer)
        timer = null
      }
      handleConfirm()
    }
  }, 1000)
}

onBeforeUnmount(() => {
  if (timer) {
    window.clearInterval(timer)
    timer = null
  }
})
</script>

<style scoped lang="scss">
.register-success-dialog {
  :deep(.el-dialog) {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  }

  :deep(.el-dialog__header) {
    display: none;
  }

  :deep(.el-dialog__body) {
    padding: 0;
  }
}

.success-container {
  padding: 30px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.success-icon-wrapper {
  width: 90px;
  height: 90px;
  border-radius: 50%;
  background: linear-gradient(135deg, #e1f6ff 0%, #f0faff 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
  animation: pulse 2s infinite;
}

.success-icon-bg {
  font-size: 60px;
  color: #67c23a;
  animation: appear 0.5s ease-out;
}

.success-title {
  font-size: 26px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20px;
  animation: slideDown 0.5s ease-out;
}

.success-content {
  margin-bottom: 30px;

  p {
    font-size: 16px;
    line-height: 1.8;
    color: #606266;
    margin: 5px 0;
  }

  .highlight {
    color: #409eff;
    font-weight: 600;
  }
}

.success-info {
  background-color: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  width: 100%;
  margin-bottom: 25px;
  animation: fadeIn 0.8s ease-out;

  .info-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    text-align: left;

    &:last-child {
      margin-bottom: 0;
    }

    .el-icon {
      font-size: 18px;
      color: #909399;
      margin-right: 10px;
    }

    span {
      font-size: 14px;
      color: #606266;
    }
  }
}

.confirm-button {
  width: 100%;
  height: 46px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 15px;
  transition: all 0.3s;
  background: linear-gradient(90deg, var(--el-color-primary), #79bbff);
  border: none;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(var(--el-color-primary-rgb), 0.3);
  }
}

.countdown {
  font-size: 14px;
  color: #909399;
  animation: fadeIn 1s ease-out;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(103, 194, 58, 0.4);
  }
  70% {
    box-shadow: 0 0 0 15px rgba(103, 194, 58, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(103, 194, 58, 0);
  }
}

@keyframes appear {
  from {
    opacity: 0;
    transform: scale(0.5);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>
