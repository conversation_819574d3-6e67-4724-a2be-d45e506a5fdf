<template>
  <div class="refund-list">
    <el-card class="search-card">
      <template #header>
        <div class="card-header">
          <span>退款管理</span>
          <div class="header-actions">
            <el-button type="primary" @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button @click="handleExport" :loading="exporting">
              <el-icon><Download /></el-icon>
              导出
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="退款单号">
          <el-input
            v-model="searchForm.refundNo"
            placeholder="请输入退款单号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="订单号">
          <el-input
            v-model="searchForm.orderNumber"
            placeholder="请输入订单号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="申请状态">
          <el-select
            v-model="searchForm.applicationStatus"
            placeholder="请选择申请状态"
            clearable
            style="width: 150px"
          >
            <el-option label="待处理" :value="1" />
            <el-option label="已同意" :value="2" />
            <el-option label="已拒绝" :value="3" />
            <el-option label="已取消" :value="4" />
            <el-option label="退款中" :value="5" />
            <el-option label="退款成功" :value="6" />
            <el-option label="退款失败" :value="7" />
          </el-select>
        </el-form-item>
        <el-form-item label="退款类型">
          <el-select
            v-model="searchForm.refundType"
            placeholder="请选择退款类型"
            clearable
            style="width: 150px"
          >
            <el-option label="仅退款" :value="1" />
            <el-option label="退货退款" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="审核状态">
          <el-select
            v-model="searchForm.approvalStatus"
            placeholder="请选择审核状态"
            clearable
            style="width: 150px"
          >
            <el-option label="待审核" :value="1" />
            <el-option label="审核通过" :value="2" />
            <el-option label="审核拒绝" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="申请时间">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 350px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.totalApplications }}</div>
              <div class="stat-label">总申请数</div>
            </div>
            <el-icon class="stat-icon"><Document /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card pending">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.pendingApprovals }}</div>
              <div class="stat-label">待审核</div>
            </div>
            <el-icon class="stat-icon"><Clock /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card success">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.approvedApplications }}</div>
              <div class="stat-label">已通过</div>
            </div>
            <el-icon class="stat-icon"><CircleCheck /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card completed">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.completedRefunds }}</div>
              <div class="stat-label">已完成</div>
            </div>
            <el-icon class="stat-icon"><Money /></el-icon>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <template #header>
        <div class="table-header">
          <span>退款列表</span>
          <div class="batch-actions" v-if="selectedRefunds.length > 0">
            <el-button type="success" size="small" @click="handleBatchApprove(2)">
              批量通过
            </el-button>
            <el-button type="danger" size="small" @click="handleBatchApprove(3)">
              批量拒绝
            </el-button>
            <el-button type="primary" size="small" @click="handleBatchProcess">
              批量处理
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="refundList"
        @selection-change="handleSelectionChange"
        stripe
        border
        style="width: 100%"
        :header-cell-style="{ background: '#f8f9fa', color: '#606266', fontWeight: '600' }"
      >
        <el-table-column type="selection" width="55" align="center" />

        <el-table-column prop="refundNo" label="退款单号" min-width="160" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="refund-no">
              <el-icon class="refund-icon"><Document /></el-icon>
              <span>{{ row.refundNo }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="orderNumber" label="订单号" min-width="160" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="order-no">
              <el-icon class="order-icon"><ShoppingBag /></el-icon>
              <span>{{ row.orderNumber }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="退款金额" width="130" align="right">
          <template #default="{ row }">
            <div class="amount-cell">
              <span class="amount">¥{{ row.refundAmount.toFixed(2) }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="申请人信息" min-width="140">
          <template #default="{ row }">
            <div class="buyer-info">
              <div class="buyer-name">{{ row.buyerName || '未知用户' }}</div>
              <div class="buyer-phone" v-if="row.buyerPhone">{{ row.buyerPhone }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="退款类型" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.refundType === 1 ? 'primary' : 'warning'" size="small">
              {{ getRefundTypeText(row.refundType) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="状态信息" min-width="180">
          <template #default="{ row }">
            <div class="status-info">
              <div class="status-row">
                <span class="status-label">申请：</span>
                <el-tag :type="getRefundStatusType(row.applicationStatus)" size="small">
                  {{ getRefundStatusText(row.applicationStatus) }}
                </el-tag>
              </div>
              <div class="status-row" v-if="row.needApproval === 1">
                <span class="status-label">审核：</span>
                <el-tag
                  :type="row.approvalStatus === 1 ? 'warning' : row.approvalStatus === 2 ? 'success' : 'danger'"
                  size="small"
                >
                  {{ getApprovalStatusText(row.approvalStatus) }}
                </el-tag>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="申请时间" width="160" align="center">
          <template #default="{ row }">
            <div class="time-info">
              <div class="date">{{ formatDate(row.createTime) }}</div>
              <div class="time">{{ formatTime(row.createTime) }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right" align="center">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button type="primary" size="small" @click="handleViewDetail(row)" plain>
                <el-icon><View /></el-icon>
                详情
              </el-button>
              <el-button
                v-if="canApprove(row)"
                type="success"
                size="small"
                @click="handleApprove(row)"
                plain
              >
                <el-icon><CircleCheck /></el-icon>
                审核
              </el-button>
              <el-button
                v-if="canProcessRefund(row)"
                type="warning"
                size="small"
                @click="handleProcess(row)"
                plain
              >
                <el-icon><Money /></el-icon>
                处理
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 退款详情对话框 -->
    <RefundDetailDialog
      v-model="detailDialogVisible"
      :refund-id="selectedRefundId"
      @refresh="handleRefresh"
    />

    <!-- 退款审核对话框 -->
    <RefundApprovalDialog
      v-model="approvalDialogVisible"
      :refund="selectedRefund"
      @refresh="handleRefresh"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Download,
  Document,
  Clock,
  CircleCheck,
  Money,
  View,
  ShoppingBag
} from '@element-plus/icons-vue'
import {
  getRefundList,
  getRefundStatistics,
  processRefund,
  batchApproveRefunds,
  batchProcessRefunds,
  exportRefundData
} from '@/api/refund'
import type {
  RefundQueryDTO,
  RefundApplicationVO,
  RefundStatistics
} from '@/types/refund'
import {
  getRefundStatusText,
  getRefundStatusType,
  getRefundTypeText,
  getApprovalStatusText,
  canApprove,
  canProcessRefund
} from '@/types/refund'
import RefundDetailDialog from './components/RefundDetailDialog.vue'
import RefundApprovalDialog from './components/RefundApprovalDialog.vue'

// 搜索表单
const searchForm = reactive<RefundQueryDTO>({
  refundNo: '',
  orderNumber: '',
  applicationStatus: undefined,
  refundType: undefined,
  approvalStatus: undefined,
  page: 1,
  pageSize: 20
})

// 日期范围
const dateRange = ref<string[]>([])

// 数据状态
const loading = ref(false)
const exporting = ref(false)
const refundList = ref<RefundApplicationVO[]>([])
const selectedRefunds = ref<RefundApplicationVO[]>([])

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 统计信息
const statistics = ref<RefundStatistics>({
  totalApplications: 0,
  pendingApprovals: 0,
  approvedApplications: 0,
  rejectedApplications: 0,
  completedRefunds: 0
})

// 对话框状态
const detailDialogVisible = ref(false)
const approvalDialogVisible = ref(false)
const selectedRefundId = ref<number | null>(null)
const selectedRefund = ref<RefundApplicationVO | null>(null)

// 监听日期范围变化
watch(dateRange, (newVal) => {
  if (newVal && newVal.length === 2) {
    searchForm.beginTime = newVal[0]
    searchForm.endTime = newVal[1]
  } else {
    searchForm.beginTime = undefined
    searchForm.endTime = undefined
  }
})

// 获取退款列表
const fetchRefundList = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      page: pagination.page,
      pageSize: pagination.pageSize
    }
    
    const response = await getRefundList(params)
    if (response.code === 1) {
      refundList.value = response.data.list || []
      pagination.total = response.data.total || 0
    } else {
      ElMessage.error(response.msg || '获取退款列表失败')
    }
  } catch (error) {
    console.error('获取退款列表失败:', error)
    ElMessage.error('获取退款列表失败')
  } finally {
    loading.value = false
  }
}

// 获取统计信息
const fetchStatistics = async () => {
  try {
    const response = await getRefundStatistics()
    if (response.code === 1) {
      statistics.value = response.data
    }
  } catch (error) {
    console.error('获取统计信息失败:', error)
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchRefundList()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    refundNo: '',
    orderNumber: '',
    applicationStatus: undefined,
    refundType: undefined,
    approvalStatus: undefined,
    page: 1,
    pageSize: 20
  })
  dateRange.value = []
  pagination.page = 1
  fetchRefundList()
}

// 刷新
const handleRefresh = () => {
  fetchRefundList()
  fetchStatistics()
}

// 分页变化
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
  fetchRefundList()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchRefundList()
}

// 选择变化
const handleSelectionChange = (selection: RefundApplicationVO[]) => {
  selectedRefunds.value = selection
}

// 查看详情
const handleViewDetail = (refund: RefundApplicationVO) => {
  selectedRefundId.value = refund.id
  detailDialogVisible.value = true
}

// 审核
const handleApprove = (refund: RefundApplicationVO) => {
  selectedRefund.value = refund
  approvalDialogVisible.value = true
}

// 处理退款
const handleProcess = async (refund: RefundApplicationVO) => {
  try {
    await ElMessageBox.confirm('确认处理该退款申请？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const response = await processRefund(refund.id)
    if (response.code === 1) {
      ElMessage.success('退款处理成功')
      handleRefresh()
    } else {
      ElMessage.error(response.msg || '退款处理失败')
    }
  } catch (error) {
    console.error('退款处理失败:', error)
  }
}

// 批量审核
const handleBatchApprove = async (approvalStatus: number) => {
  if (selectedRefunds.value.length === 0) {
    ElMessage.warning('请选择要审核的退款申请')
    return
  }

  const statusText = approvalStatus === 2 ? '通过' : '拒绝'
  try {
    await ElMessageBox.confirm(`确认批量${statusText}选中的 ${selectedRefunds.value.length} 个退款申请？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const refundIds = selectedRefunds.value.map(r => r.id)
    const response = await batchApproveRefunds(refundIds, approvalStatus)
    
    if (response.code === 1) {
      ElMessage.success(`批量${statusText}成功`)
      handleRefresh()
    } else {
      ElMessage.error(response.msg || `批量${statusText}失败`)
    }
  } catch (error) {
    console.error(`批量${statusText}失败:`, error)
  }
}

// 批量处理
const handleBatchProcess = async () => {
  if (selectedRefunds.value.length === 0) {
    ElMessage.warning('请选择要处理的退款申请')
    return
  }

  try {
    await ElMessageBox.confirm(`确认批量处理选中的 ${selectedRefunds.value.length} 个退款申请？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const refundIds = selectedRefunds.value.map(r => r.id)
    const response = await batchProcessRefunds(refundIds)
    
    if (response.code === 1) {
      ElMessage.success('批量处理成功')
      handleRefresh()
    } else {
      ElMessage.error(response.msg || '批量处理失败')
    }
  } catch (error) {
    console.error('批量处理失败:', error)
  }
}

// 导出
const handleExport = async () => {
  exporting.value = true
  try {
    await exportRefundData(searchForm)
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exporting.value = false
  }
}

// 时间格式化函数
const formatDate = (dateTime: string) => {
  if (!dateTime) return ''
  return dateTime.split(' ')[0]
}

const formatTime = (dateTime: string) => {
  if (!dateTime) return ''
  return dateTime.split(' ')[1] || ''
}

// 组件挂载
onMounted(() => {
  fetchRefundList()
  fetchStatistics()
})
</script>

<style scoped lang="scss">
.refund-list {
  padding: 20px;

  .search-card {
    margin-bottom: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-actions {
        display: flex;
        gap: 10px;
      }
    }

    .search-form {
      .el-form-item {
        margin-bottom: 15px;
      }
    }
  }

  .stats-cards {
    margin-bottom: 20px;

    .stat-card {
      position: relative;
      overflow: hidden;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .stat-content {
        .stat-number {
          font-size: 28px;
          font-weight: bold;
          color: #303133;
          margin-bottom: 5px;
        }

        .stat-label {
          font-size: 14px;
          color: #909399;
        }
      }

      .stat-icon {
        position: absolute;
        right: 20px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 40px;
        opacity: 0.3;
      }

      &.pending {
        border-left: 4px solid #e6a23c;
        .stat-number { color: #e6a23c; }
        .stat-icon { color: #e6a23c; }
      }

      &.success {
        border-left: 4px solid #67c23a;
        .stat-number { color: #67c23a; }
        .stat-icon { color: #67c23a; }
      }

      &.completed {
        border-left: 4px solid #409eff;
        .stat-number { color: #409eff; }
        .stat-icon { color: #409eff; }
      }
    }
  }

  .table-card {
    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .batch-actions {
        display: flex;
        gap: 10px;
      }
    }

    .amount {
      font-weight: bold;
      color: #f56c6c;
    }

    .text-muted {
      color: #909399;
      font-size: 12px;
    }

    .pagination-container {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }

    // 表格样式优化
    .refund-no, .order-no {
      display: flex;
      align-items: center;
      gap: 8px;

      .refund-icon, .order-icon {
        color: #409eff;
        font-size: 14px;
      }
    }

    .amount-cell {
      .amount {
        font-weight: 700;
        color: #e6a23c;
        font-size: 15px;
        background: linear-gradient(135deg, #ff9a56, #ff6b35);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
    }

    .buyer-info {
      .buyer-name {
        font-weight: 500;
        color: #303133;
        margin-bottom: 2px;
      }

      .buyer-phone {
        font-size: 12px;
        color: #909399;
      }
    }

    .status-info {
      .status-row {
        display: flex;
        align-items: center;
        margin-bottom: 4px;

        &:last-child {
          margin-bottom: 0;
        }

        .status-label {
          font-size: 12px;
          color: #606266;
          margin-right: 6px;
          min-width: 32px;
        }
      }
    }

    .time-info {
      .date {
        font-weight: 500;
        color: #303133;
        margin-bottom: 2px;
      }

      .time {
        font-size: 12px;
        color: #909399;
      }
    }

    .action-buttons {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .el-button {
        margin: 0;
        width: 100%;

        &.el-button--small {
          padding: 4px 8px;
          font-size: 12px;
        }
      }
    }

    // 表格行悬停效果
    :deep(.el-table__row) {
      transition: all 0.3s ease;

      &:hover {
        background-color: #f8f9fa !important;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }

    // 表格头部样式
    :deep(.el-table__header-wrapper) {
      .el-table__header {
        th {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
          color: white !important;
          font-weight: 600 !important;
          border-bottom: none !important;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .refund-list {
    padding: 10px;

    .search-form {
      .el-form-item {
        width: 100%;
        margin-bottom: 10px;
      }
    }

    .stats-cards {
      .el-col {
        margin-bottom: 15px;
      }
    }

    .table-card {
      .el-table {
        font-size: 12px;
      }
    }
  }
}
</style>
