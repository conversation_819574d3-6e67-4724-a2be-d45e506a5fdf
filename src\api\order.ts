import request from '@/utils/request'
import type {
  OrderPageQueryDTO,
  OrderEditDTO,
  BatchOperationDTO,
  OrderBatchOperationResultVO,
  ShipOrderDTO,
  Courier
} from '@/types/order'

// ==================== 订单查询相关 ====================

// 订单分页查询
export const getOrderList = (params: OrderPageQueryDTO) => {
  return request.get('/admin/order/conditionSearch', { params })
}

// 获取订单详情
export const getOrderById = (id: number) => {
  return request.get(`/admin/order/details/${id}`)
}

// 根据订单号查询订单
export const getOrderByNumber = (number: string) => {
  return request.get(`/admin/order/number/${number}`)
}

// 根据买家ID查询订单列表
export const getOrdersByBuyerId = (buyerId: number) => {
  return request.get(`/admin/order/buyer/${buyerId}`)
}

// 查询历史订单
export const getOrderHistory = (buyerId: number) => {
  return request.get('/admin/order/history', {
    params: { buyerId }
  })
}

// ==================== 订单状态管理 ====================

// 发货 - 根据文档修正接口路径
export const shipOrder = (data: ShipOrderDTO) => {
  return request.put('/admin/order/delivery', data)
}

// 获取快递公司列表
export const getCourierList = (): Promise<{ code: number; data: Courier[]; msg?: string }> => {
  return request.get('/admin/order/couriers')
}

// 更新物流信息（新API文档中没有单独的物流更新接口，发货时一起设置）

// 完成订单
export const completeOrder = (id: number) => {
  return request.put(`/admin/order/complete/${id}`)
}

// 取消订单
export const cancelOrder = (data: { id: number; cancelReason: string }) => {
  return request.put('/admin/order/cancel', data)
}

// ==================== 批量操作 ====================

// 批量发货
export const batchShip = (data: BatchOperationDTO): Promise<{ code: number; data: OrderBatchOperationResultVO; msg?: string }> => {
  return request.put('/admin/order/batch/ship', data)
}

// 批量取消
export const batchCancel = (data: BatchOperationDTO): Promise<{ code: number; data: OrderBatchOperationResultVO; msg?: string }> => {
  return request.put('/admin/order/batch/cancel', data)
}

// 批量完成
export const batchComplete = (data: BatchOperationDTO): Promise<{ code: number; data: OrderBatchOperationResultVO; msg?: string }> => {
  return request.put('/admin/order/batch/complete', data)
}

// 批量导出
export const batchExport = (data: BatchOperationDTO): Promise<{ code: number; data: OrderBatchOperationResultVO; msg?: string }> => {
  return request.post('/admin/order/batch/export', data)
}

// ==================== 订单编辑和退款 ====================

// 编辑订单
export const editOrder = (id: number, data: OrderEditDTO) => {
  return request.put(`/admin/order/edit/${id}`, null, {
    params: {
      addressInfo: data.addressInfo,
      remark: data.remark,
      editReason: data.editReason
    }
  })
}

// 注意：根据文档，订单退款功能已移至退款管理模块
// 订单管理模块不再包含退款处理接口
// 退款相关功能请使用 /api/refund.ts 中的接口

// ==================== 订单日志和操作记录 ====================

// 获取订单操作日志
export const getOrderLogs = (id: number) => {
  return request.get(`/admin/order/logs/${id}`)
}

// 获取订单可执行操作
export const getOrderActions = (id: number): Promise<{ code: number; data: string[]; msg?: string }> => {
  return request.get(`/admin/order/actions/${id}`)
}

// ==================== 统计和报表 ====================

// 获取订单统计
export const getOrderStatistics = () => {
  return request.get('/admin/order/statistics')
}

// 获取订单概览
export const getOrderOverview = () => {
  return request.get('/admin/order/overview')
}

// 导出订单
export const exportOrders = (orderIds: number[], format: string = 'excel'): Promise<{ code: number; data: string; msg?: string }> => {
  return request.post('/admin/order/export', null, {
    params: {
      orderIds,
      format
    }
  })
}

// ==================== 统计报表接口 ====================

// ==================== 基础统计接口 ====================

// 获取订单统计数据
export const getOrderStatisticsData = () => {
  return request.get('/admin/orders/statistics')
}

// 获取指定日期范围的订单统计
export const getOrderStatisticsByRange = (beginDate: string, endDate: string) => {
  return request.get('/admin/orders/statistics/range', {
    params: { beginDate, endDate }
  })
}

// ==================== 增强统计接口 ====================

// 获取增强版订单统计数据
export const getEnhancedStatistics = (beginDate: string, endDate: string) => {
  return request.get('/admin/orders/statistics/enhanced', {
    params: { beginDate, endDate }
  })
}

// ==================== 专项统计接口 ====================

// 获取时间维度统计
export const getTimeDimensionStatistics = () => {
  return request.get('/admin/orders/statistics/time-dimension')
}

// 获取支付方式统计
export const getPaymentMethodStatistics = (beginDate: string, endDate: string) => {
  return request.get('/admin/orders/statistics/payment-method', {
    params: { beginDate, endDate }
  })
}

// 获取商品维度统计
export const getProductStatistics = (beginDate: string, endDate: string, topLimit: number = 10) => {
  return request.get('/admin/orders/statistics/product', {
    params: { beginDate, endDate, topLimit }
  })
}

// 获取用户维度统计
export const getUserStatistics = (beginDate: string, endDate: string) => {
  return request.get('/admin/orders/statistics/user', {
    params: { beginDate, endDate }
  })
}

// 获取转化率统计
export const getConversionRateStatistics = (beginDate: string, endDate: string) => {
  return request.get('/admin/orders/statistics/conversion-rate', {
    params: { beginDate, endDate }
  })
}

// ==================== 实时和对比统计接口 ====================

// 获取实时统计数据
export const getRealtimeStatistics = () => {
  return request.get('/admin/orders/statistics/realtime')
}

// 获取同比环比数据
export const getComparisonStatistics = (beginDate: string, endDate: string) => {
  return request.get('/admin/orders/statistics/comparison', {
    params: { beginDate, endDate }
  })
}

// 获取趋势统计数据
export const getTrendStatistics = (beginDate: string, endDate: string, granularity: string = 'day') => {
  return request.get('/admin/orders/statistics/trend', {
    params: { beginDate, endDate, granularity }
  })
}

// ==================== 传统兼容接口 ====================

// 获取今日订单统计
export const getTodayStatistics = () => {
  return request.get('/admin/orders/statistics/today')
}

// 获取本月订单统计
export const getMonthStatistics = () => {
  return request.get('/admin/orders/statistics/month')
}

// 获取订单状态分布
export const getStatusDistribution = () => {
  return request.get('/admin/orders/statistics/status-distribution')
}
