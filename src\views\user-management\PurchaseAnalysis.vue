<template>
  <div class="purchase-analysis-container">
    <div class="page-header">
      <h2>购买分析</h2>
      <p>分析用户的购买行为和消费模式</p>
    </div>

    <el-card class="filter-card">
      <div class="filter-container">
        <div class="date-range-box">
          <el-radio-group v-model="dateRange" @change="loadAnalysisData">
            <el-radio-button label="last7days">最近7天</el-radio-button>
            <el-radio-button label="last14days">最近14天</el-radio-button>
            <el-radio-button label="last30days">最近30天</el-radio-button>
            <el-radio-button label="thisMonth">本月</el-radio-button>
            <el-radio-button label="lastMonth">上月</el-radio-button>
            <el-radio-button label="thisYear">今年</el-radio-button>
            <el-radio-button label="custom">自定义</el-radio-button>
          </el-radio-group>

          <el-date-picker
            v-if="dateRange === 'custom'"
            v-model="customDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            @change="loadAnalysisData"
          ></el-date-picker>
        </div>
      </div>
    </el-card>

    <div class="analysis-grid">
      <el-card class="stats-card">
        <template #header>
          <div class="card-header">
            <span>购买概览</span>
            <el-tooltip content="基于所选时间范围内的用户购买数据" placement="top">
              <el-icon class="header-icon"><InfoFilled /></el-icon>
            </el-tooltip>
          </div>
        </template>
        <div class="stats-grid">
          <div class="stat-item">
            <h3>{{ stats.totalOrders }}</h3>
            <p>总订单数</p>
          </div>
          <div class="stat-item">
            <h3>{{ stats.totalRevenue }}</h3>
            <p>总收入</p>
          </div>
          <div class="stat-item">
            <h3>{{ stats.averageOrderValue }}</h3>
            <p>平均订单价值</p>
          </div>
          <div class="stat-item">
            <h3>{{ stats.repurchaseRate }}%</h3>
            <p>复购率</p>
          </div>
        </div>
      </el-card>

      <el-card class="chart-card">
        <template #header>
          <div class="card-header">
            <span>订单趋势</span>
            <el-tooltip content="展示所选时间范围内每日订单数量" placement="top">
              <el-icon class="header-icon"><InfoFilled /></el-icon>
            </el-tooltip>
          </div>
        </template>
        <div class="chart-container" ref="orderChartRef"></div>
      </el-card>

      <el-card class="chart-card">
        <template #header>
          <div class="card-header">
            <span>收入趋势</span>
            <el-tooltip content="展示所选时间范围内每日收入金额" placement="top">
              <el-icon class="header-icon"><InfoFilled /></el-icon>
            </el-tooltip>
          </div>
        </template>
        <div class="chart-container" ref="revenueChartRef"></div>
      </el-card>

      <el-card class="chart-card">
        <template #header>
          <div class="card-header">
            <span>购买时段分布</span>
            <el-tooltip content="用户下单时段的分布情况" placement="top">
              <el-icon class="header-icon"><InfoFilled /></el-icon>
            </el-tooltip>
          </div>
        </template>
        <div class="chart-container" ref="hourlyChartRef"></div>
      </el-card>

      <el-card class="chart-card chart-wide">
        <template #header>
          <div class="card-header">
            <span>复购周期分析</span>
            <el-tooltip content="用户再次购买所需的平均天数" placement="top">
              <el-icon class="header-icon"><InfoFilled /></el-icon>
            </el-tooltip>
          </div>
        </template>
        <div class="chart-container" ref="repurchaseChartRef"></div>
      </el-card>

      <el-card class="merchant-card chart-wide">
        <template #header>
          <div class="card-header">
            <span>商家购买分布</span>
            <el-tooltip content="各商家的订单和收入占比" placement="top">
              <el-icon class="header-icon"><InfoFilled /></el-icon>
            </el-tooltip>
          </div>
        </template>
        <el-table
          :data="merchantData"
          border
          style="width: 100%"
          :max-height="400"
          v-loading="loading"
        >
          <el-table-column prop="merchantName" label="商家名称" min-width="200"></el-table-column>
          <el-table-column prop="orderCount" label="订单数" width="120"></el-table-column>
          <el-table-column prop="orderPercent" label="订单占比" width="120">
            <template #default="scope">
              <el-progress
                :percentage="scope.row.orderPercent"
                :color="getRandomColor(scope.row.merchantName)"
              ></el-progress>
            </template>
          </el-table-column>
          <el-table-column prop="revenue" label="收入金额" width="120">
            <template #default="scope">
              <span>¥{{ Number(scope.row.revenue).toFixed(2) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="revenuePercent" label="收入占比" width="120">
            <template #default="scope">
              <el-progress
                :percentage="scope.row.revenuePercent"
                :color="getRandomColor(scope.row.merchantName + '-rev')"
              ></el-progress>
            </template>
          </el-table-column>
          <el-table-column prop="avgOrderValue" label="平均订单价值" width="150">
            <template #default="scope">
              <span>¥{{ Number(scope.row.avgOrderValue).toFixed(2) }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { InfoFilled } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

defineOptions({
  name: 'PurchaseAnalysis',
})

// 日期筛选
const dateRange = ref('last30days')
const customDateRange = ref([])

// 图表实例
let orderChart = null
let revenueChart = null
let hourlyChart = null
let repurchaseChart = null

// DOM引用
const orderChartRef = ref(null)
const revenueChartRef = ref(null)
const hourlyChartRef = ref(null)
const repurchaseChartRef = ref(null)

// 加载状态
const loading = ref(false)

// 统计数据
const stats = reactive({
  totalOrders: '0',
  totalRevenue: '¥0.00',
  averageOrderValue: '¥0.00',
  repurchaseRate: '0.0',
})

// 商家数据
const merchantData = ref([])

// 加载分析数据
const loadAnalysisData = async () => {
  loading.value = true
  try {
    // 在实际项目中会调用API
    // const res = await getPurchaseAnalysis({
    //   dateRange: dateRange.value,
    //   startDate: customDateRange.value?.[0],
    //   endDate: customDateRange.value?.[1],
    // })

    // 模拟数据加载延迟
    await new Promise((resolve) => setTimeout(resolve, 800))

    // 设置统计数据
    stats.totalOrders = '8,562'
    stats.totalRevenue = '¥1,258,432.50'
    stats.averageOrderValue = '¥146.98'
    stats.repurchaseRate = '35.2'

    // 模拟商家数据
    merchantData.value = [
      {
        merchantName: '优品服饰旗舰店',
        orderCount: 1245,
        orderPercent: 14.5,
        revenue: 215689.5,
        revenuePercent: 17.1,
        avgOrderValue: 173.24,
      },
      {
        merchantName: '电子科技专卖店',
        orderCount: 986,
        orderPercent: 11.5,
        revenue: 328450.75,
        revenuePercent: 26.1,
        avgOrderValue: 333.11,
      },
      {
        merchantName: '家居日用生活馆',
        orderCount: 1563,
        orderPercent: 18.3,
        revenue: 187560.25,
        revenuePercent: 14.9,
        avgOrderValue: 120.0,
      },
      {
        merchantName: '美妆护肤旗舰店',
        orderCount: 1876,
        orderPercent: 21.9,
        revenue: 206890.15,
        revenuePercent: 16.4,
        avgOrderValue: 110.28,
      },
      {
        merchantName: '母婴用品专营店',
        orderCount: 874,
        orderPercent: 10.2,
        revenue: 156780.85,
        revenuePercent: 12.5,
        avgOrderValue: 179.38,
      },
      {
        merchantName: '零食小吃旗舰店',
        orderCount: 1218,
        orderPercent: 14.2,
        revenue: 97865.3,
        revenuePercent: 7.8,
        avgOrderValue: 80.35,
      },
      {
        merchantName: '运动户外专营店',
        orderCount: 542,
        orderPercent: 6.3,
        revenue: 45987.5,
        revenuePercent: 3.7,
        avgOrderValue: 84.85,
      },
      {
        merchantName: '图书文具专卖店',
        orderCount: 258,
        orderPercent: 3.0,
        revenue: 19208.2,
        revenuePercent: 1.5,
        avgOrderValue: 74.45,
      },
    ]

    // 渲染图表
    nextTick(() => {
      initCharts()
    })
  } catch (error) {
    console.error('获取购买分析数据失败:', error)
    ElMessage.error('获取购买分析数据失败')
  } finally {
    loading.value = false
  }
}

// 初始化图表
const initCharts = () => {
  // 订单趋势图表
  if (orderChartRef.value) {
    orderChart = echarts.init(orderChartRef.value)
    const orderOption = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: generateDateLabels(30),
        axisLabel: {
          rotate: 45,
        },
      },
      yAxis: {
        type: 'value',
        name: '订单数',
      },
      series: [
        {
          name: '订单数量',
          type: 'bar',
          data: generateRandomData(30, 50, 150),
          itemStyle: {
            color: '#409EFF',
          },
        },
      ],
    }
    orderChart.setOption(orderOption)
  }

  // 收入趋势图表
  if (revenueChartRef.value) {
    revenueChart = echarts.init(revenueChartRef.value)
    const revenueOption = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'line',
        },
        formatter: function (params) {
          return (
            params[0].name + '<br/>' + params[0].seriesName + ': ¥' + params[0].value.toFixed(2)
          )
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: generateDateLabels(30),
        axisLabel: {
          rotate: 45,
        },
      },
      yAxis: {
        type: 'value',
        name: '收入(元)',
      },
      series: [
        {
          name: '收入金额',
          type: 'line',
          smooth: true,
          data: generateRandomData(30, 5000, 20000),
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(103, 194, 58, 0.8)',
                },
                {
                  offset: 1,
                  color: 'rgba(103, 194, 58, 0.1)',
                },
              ],
            },
          },
          itemStyle: {
            color: '#67C23A',
          },
        },
      ],
    }
    revenueChart.setOption(revenueOption)
  }

  // 购买时段分布图表
  if (hourlyChartRef.value) {
    hourlyChart = echarts.init(hourlyChartRef.value)
    const hours = Array.from({ length: 24 }, (_, i) => i + ':00')
    const hourlyOption = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: hours,
        axisLabel: {
          rotate: 45,
          interval: 1,
        },
      },
      yAxis: {
        type: 'value',
        name: '订单数',
      },
      series: [
        {
          name: '订单数量',
          type: 'bar',
          data: [
            // 模拟数据 - 早上和晚上高峰
            12, 5, 3, 2, 8, 25, 45, 78, 120, 100, 85, 95, 105, 90, 80, 95, 110, 135, 165, 185, 145,
            95, 55, 25,
          ],
          itemStyle: {
            color: function (params) {
              // 颜色根据时段变化
              const hourValue = parseInt(params.name)
              if (hourValue >= 0 && hourValue < 6) {
                return '#909399' // 凌晨
              } else if (hourValue >= 6 && hourValue < 12) {
                return '#E6A23C' // 上午
              } else if (hourValue >= 12 && hourValue < 18) {
                return '#F56C6C' // 下午
              } else {
                return '#409EFF' // 晚上
              }
            },
          },
        },
      ],
    }
    hourlyChart.setOption(hourlyOption)
  }

  // 复购周期分析图表
  if (repurchaseChartRef.value) {
    repurchaseChart = echarts.init(repurchaseChartRef.value)
    const repurchaseOption = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: [
          '1-7天',
          '8-14天',
          '15-30天',
          '31-60天',
          '61-90天',
          '91-180天',
          '181-365天',
          '365天以上',
        ],
      },
      yAxis: {
        type: 'value',
        name: '用户数',
      },
      series: [
        {
          name: '用户数量',
          type: 'bar',
          data: [586, 425, 352, 281, 195, 143, 78, 42],
          itemStyle: {
            color: function (params) {
              const colors = [
                '#67C23A',
                '#85CE61',
                '#B3E19D',
                '#E6A23C',
                '#F3D19E',
                '#F56C6C',
                '#FA9E9E',
                '#909399',
              ]
              return colors[params.dataIndex]
            },
          },
        },
      ],
    }
    repurchaseChart.setOption(repurchaseOption)
  }
}

// 生成随机数据
const generateRandomData = (count, min, max) => {
  return Array.from({ length: count }, () => Math.floor(Math.random() * (max - min + 1)) + min)
}

// 生成日期标签
const generateDateLabels = (days) => {
  const dates = []
  const endDate = new Date()

  for (let i = days - 1; i >= 0; i--) {
    const date = new Date()
    date.setDate(endDate.getDate() - i)
    const formattedDate = `${date.getMonth() + 1}/${date.getDate()}`
    dates.push(formattedDate)
  }

  return dates
}

// 根据字符串获取随机颜色
const getRandomColor = (str) => {
  // 从一个固定的颜色数组中选择，以便相同的商家名称总是得到相同的颜色
  const colors = [
    '#409EFF',
    '#67C23A',
    '#E6A23C',
    '#F56C6C',
    '#909399',
    '#336699',
    '#99CC66',
    '#FF9900',
    '#FF6666',
    '#666666',
  ]

  // 使用字符串的哈希值来选择颜色
  let hashCode = 0
  for (let i = 0; i < str.length; i++) {
    hashCode = str.charCodeAt(i) + ((hashCode << 5) - hashCode)
  }

  // 确保hashCode是正数
  hashCode = Math.abs(hashCode)

  // 使用hashCode选择颜色
  return colors[hashCode % colors.length]
}

// 处理窗口大小改变，重新调整图表
const handleResize = () => {
  orderChart?.resize()
  revenueChart?.resize()
  hourlyChart?.resize()
  repurchaseChart?.resize()
}

// 生命周期钩子
onMounted(() => {
  loadAnalysisData()
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  orderChart?.dispose()
  revenueChart?.dispose()
  hourlyChart?.dispose()
  repurchaseChart?.dispose()
})
</script>

<style scoped lang="scss">
.purchase-analysis-container {
  padding: 20px;

  .page-header {
    margin-bottom: 20px;

    h2 {
      font-size: 24px;
      margin-bottom: 8px;
    }

    p {
      color: #606266;
      font-size: 14px;
    }
  }

  .filter-card {
    margin-bottom: 20px;
  }

  .filter-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
  }

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .header-icon {
      color: #909399;
      font-size: 16px;
      cursor: help;
    }
  }

  .analysis-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    margin-bottom: 20px;

    .chart-wide {
      grid-column: 1 / 3;
    }
  }

  .stats-card {
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 20px;
    }

    .stat-item {
      padding: 15px;
      background-color: #f8f9fb;
      border-radius: 8px;
      text-align: center;

      h3 {
        margin: 0;
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 5px;
        color: #409eff;
      }

      p {
        margin: 0;
        color: #606266;
      }

      &:nth-child(2) h3 {
        color: #67c23a;
      }

      &:nth-child(3) h3 {
        color: #e6a23c;
      }

      &:nth-child(4) h3 {
        color: #f56c6c;
      }
    }
  }

  .chart-container {
    height: 350px;
    width: 100%;
  }

  @media screen and (max-width: 1200px) {
    .analysis-grid {
      grid-template-columns: 1fr;

      .chart-wide {
        grid-column: auto;
      }
    }
  }

  @media screen and (max-width: 768px) {
    .stats-card .stats-grid {
      grid-template-columns: 1fr;
    }

    .filter-container {
      flex-direction: column;
      align-items: flex-start;
    }

    .chart-container {
      height: 300px;
    }
  }
}
</style>
