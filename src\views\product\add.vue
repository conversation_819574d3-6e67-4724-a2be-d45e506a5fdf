<template>
  <div class="page-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="page-header">
          <h2 class="page-title">添加商品</h2>
          <div class="page-actions">
            <el-button @click="handleCancel">取消</el-button>
            <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
              添加商品
            </el-button>
          </div>
        </div>
      </template>

      <div class="page-content">
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="120px"
          class="product-form"
          @submit.prevent
        >
          <!-- 基本信息 -->
          <div class="form-section">
            <h3 class="section-title">基本信息</h3>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="商品名称" prop="name">
                  <el-input v-model="form.name" placeholder="请输入商品名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="商品分类" prop="category_id">
                  <el-select
                    v-model="form.category_id"
                    placeholder="请选择商品分类"
                    clearable
                    filterable
                  >
                    <el-option
                      v-for="category in thirdLevelCategories"
                      :key="category.id"
                      :label="category.fullName"
                      :value="category.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="商品编码" prop="out_product_id">
                  <el-input v-model="form.out_product_id" placeholder="创建成功后自动生成" disabled>
                    <template #append>
                      <el-tooltip content="商品编码将在商品创建成功后自动生成" placement="top">
                        <el-icon><InfoFilled /></el-icon>
                      </el-tooltip>
                    </template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="品牌" prop="brand_name">
                  <el-input v-model="form.brand_name" placeholder="请输入品牌名称" clearable />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="商品价格" prop="price">
                  <el-input-number
                    v-model="form.price"
                    :min="0"
                    :precision="2"
                    :step="10"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="商品单位" prop="unit">
                  <el-select v-model="form.unit" placeholder="请选择商品单位">
                    <el-option
                      v-for="unit in unitOptions"
                      :key="unit"
                      :label="unit"
                      :value="unit"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="商品重量(克)" prop="weight">
                  <el-input-number
                    v-model="form.weight"
                    :min="0"
                    :precision="2"
                    :step="0.1"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12" v-if="userStore.userInfo.role==='超级管理员'||userStore.userInfo.role==='管理员'">
                <el-form-item v-if="userStore.userInfo.role==='超级管理员'||userStore.userInfo.role==='管理员'" label="商品排序" prop="sort">
                  <el-input-number v-model="form.sort" :min="0" style="width: 100%" />
                  <div class="field-tip">
                    <el-icon><InfoFilled /></el-icon>
                    <span>排序值越小越靠前，用于控制商品在商城中的展示顺序</span>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="userStore.userInfo.role==='超级管理员'||userStore.userInfo.role==='管理员' ? 12 : 24">
                <el-form-item label="上架状态" prop="publish_status">
                  <el-radio-group v-model="form.publish_status">
                    <el-radio :label="1">上架</el-radio>
                    <el-radio :label="0">下架</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="userStore.userInfo.role==='超级管理员'||userStore.userInfo.role==='管理员' ? 12 : 24">
                <el-form-item label="当前库存" prop="inventory">
                  <el-input-number v-model="form.inventory" :min="0"></el-input-number>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 商品图片 -->
          <div class="form-section">
            <h3 class="section-title">商品图片</h3>
            <el-form-item label="商品主图" prop="pic">
              <el-upload
                class="main-image-uploader"
                action="#"
                :auto-upload="false"
                :show-file-list="false"
                :on-change="handleMainImageChange"
              >
                <img v-if="form.pic" :src="form.pic" class="main-image" />
                <div v-else class="upload-placeholder">
                  <el-icon><Plus /></el-icon>
                  <div class="upload-text">上传主图</div>
                </div>
              </el-upload>
              <div class="upload-tip">建议尺寸：800x800px，支持jpg、png格式</div>
            </el-form-item>

            <el-form-item label="商品相册" prop="albumPics">
              <el-upload
                class="gallery-uploader"
                action="#"
                :auto-upload="false"
                list-type="picture-card"
                :on-change="handleGalleryChange"
                :on-remove="handleGalleryRemove"
                :file-list="galleryList"
                multiple
              >
                <el-icon><Plus /></el-icon>
              </el-upload>
              <div class="upload-tip">最多上传5张商品展示图，建议尺寸：800x800px</div>

              <!-- 添加图片总大小提示 -->
              <div class="upload-size-info">
                <span
                  class="size-text"
                  :class="{
                    warning: totalImagesSize > 7 && totalImagesSize <= 10,
                    danger: totalImagesSize > 10,
                    normal: totalImagesSize <= 7,
                  }"
                >
                  图片总大小：{{ totalImagesSize.toFixed(2) }}MB / 10MB
                </span>
                <el-progress
                  class="size-progress"
                  :percentage="totalImagesSize * 10"
                  :status="
                    totalImagesSize > 10 ? 'exception' : totalImagesSize > 7 ? 'warning' : ''
                  "
                  :stroke-width="8"
                ></el-progress>
              </div>
            </el-form-item>
          </div>

          <!-- 商品属性 -->
          <div class="form-section">
            <h3 class="section-title">商品属性</h3>
            <el-form-item label="销售属性" prop="product_attr">
              <div class="attr-container">
                <!-- 左侧：属性分类和选择 -->
                <div class="attr-selection-panel attr-child">
                  <div class="panel-header">
                    <span class="panel-title">添加商品属性</span>
                    <el-tooltip content="从预设属性中选择适合您商品的属性" placement="top">
                      <el-icon class="help-icon"><QuestionFilled /></el-icon>
                    </el-tooltip>
                  </div>
                  <el-tabs type="border-card" class="attr-tabs">
                    <el-tab-pane
                      v-for="(attrGroup, groupIndex) in predefinedAttrs"
                      :key="groupIndex"
                      :label="attrGroup.groupName"
                    >
                      <div class="attr-options">
                        <el-checkbox-group v-model="selectedAttrs[groupIndex]">
                          <!-- 点击后执行回调handleAttrChange显示属性输入对话框 -->
                          <el-button
                            v-for="(attr, attrIndex) in attrGroup.attrs"
                            :key="attrIndex"
                            :label="attr"
                            @click="(val) => handleAttrChange(val, groupIndex, attr)"
                            class="attr-checkbox"
                          >
                            {{ attr }}
                          </el-button>
                        </el-checkbox-group>
                      </div>
                    </el-tab-pane>
                  </el-tabs>
                </div>

                <!-- 右侧：已选属性展示 -->
                <div class="attr-preview-panel attr-child">
                  <div class="panel-header">
                    <span class="panel-title">已选择的属性</span>
                    <span class="attr-count" v-if="form.attrList.length > 0">
                      共 <b>{{ form.attrList.length }}</b> 个属性
                    </span>
                  </div>

                  <div class="attr-tags-container">
                    <div v-if="form.attrList.length === 0" class="no-attrs">
                      <el-empty description="暂无选择的属性" :image-size="80">
                        <template #description>
                          <p class="empty-tip">从左侧选择商品属性</p>
                          <p class="empty-tip-sub">添加属性将帮助买家更好地了解您的商品</p>
                        </template>
                      </el-empty>
                    </div>
                    <!-- 此处用于展示已添加的商品属性列表 -->
                    <div v-else class="attr-list">
                      <div v-for="(attr, index) in form.attrList" :key="index" class="attr-item">
                        <el-dropdown class="attr-dropDown">
                          <el-button type="primary">
                            {{ attr.key }}
                            <el-icon class="el-icon--right"><arrow-down /></el-icon>
                          </el-button>
                          <template #dropdown>
                            <el-dropdown-menu class="attr-dropDown">
                              <el-dropdown-item
                                v-for="(attrValue, attrIndex) in attr.value"
                                :key="attrIndex"
                              >
                                <div class="attr-item-content">
                                  <div class="attr-item-value">{{ attrValue }}</div>
                                </div>
                                <div class="attr-item-actions">
                                  <el-tooltip content="编辑属性值" placement="top">
                                    <el-button
                                      type="primary"
                                      link
                                      circle
                                      @click="editAttrValue(index, attr.key, attrValue, attrIndex)"
                                    >
                                      <el-icon><Edit /></el-icon>
                                    </el-button>
                                  </el-tooltip>
                                  <el-tooltip content="删除属性" placement="top">
                                    <el-button
                                      type="danger"
                                      link
                                      circle
                                      @click="handleRemoveAttr(index, attrIndex)"
                                    >
                                      <el-icon><Delete /></el-icon>
                                    </el-button>
                                  </el-tooltip>
                                </div>
                              </el-dropdown-item>
                            </el-dropdown-menu>
                          </template>
                        </el-dropdown>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 属性值输入对话框 -->
              <el-dialog
                v-model="inputVisible.value"
                :title="
                  currentAction === 'add' ? `请输入 ${currentKey} 的值` : `编辑 ${currentKey} 的值`
                "
                width="30%"
                :close-on-click-modal="false"
                :show-close="true"
                align-center
              >
                <!--阻止表单回车默认提交事件-->
                <el-form @submit.prevent>
                  <el-form-item :label="currentKey">
                    <el-input
                      @keyup.enter="handleValueConfirm"
                      ref="attrValueInput"
                      v-model="inputValue.value"
                      placeholder="请输入属性值"
                    >
                      <template #prefix v-if="currentAction === 'edit'">
                        <el-icon><Edit /></el-icon>
                      </template>
                    </el-input>
                  </el-form-item>
                  <div class="attr-tips">
                    <el-alert
                      title="请保持属性值简洁、准确，有助于买家理解商品特性"
                      type="info"
                      :closable="false"
                      show-icon
                    />
                  </div>
                </el-form>
                <template #footer>
                  <span class="dialog-footer">
                    <el-button @click="cancelAttributeValue">取消</el-button>
                    <el-button type="primary" @click="handleValueConfirm">确认</el-button>
                  </span>
                </template>
              </el-dialog>
            </el-form-item>
          </div>
          <!-- 商品详情 -->
          <div class="form-section">
            <h3 class="section-title">商品详情</h3>
            <el-form-item label="商品详情" prop="detail_html">
              <el-input
                v-model="form.detail_html"
                type="textarea"
                :rows="6"
                placeholder="请输入商品详情HTML"
              />
            </el-form-item>
            <el-form-item label="图文介绍" prop="graphic_introduction">
              <el-upload
                list-type="picture-card"
                action="#"
                :auto-upload="false"
                :on-change="handleIntroductionChange"
                :on-remove="handleIntroductionRemove"
                multiple
                :file-list="graphicIntroductList"
              >
                <el-icon><Plus /></el-icon>
              </el-upload>
              <div class="upload-tip">建议尺寸：800x800px，支持jpg、png格式</div>
            </el-form-item>
            <el-form-item label="商品文档(PDF)" prop="pdf_document">
              <el-upload
                class="pdf-uploader"
                action="#"
                :auto-upload="false"
                :show-file-list="true"
                :on-change="handlePdfChange"
                :on-remove="handlePdfRemove"
                :limit="1"
                :file-list="pdfFileList"
                :before-upload="beforePdfUpload"
                accept=".pdf,application/pdf"
              >
                <el-button type="primary" plain>
                  <el-icon><Document /></el-icon>
                  <span>上传PDF文档</span>
                </el-button>
                <template #tip>
                  <div class="upload-tip">仅支持PDF格式，最大10MB</div>
                  <!-- 添加总大小提示 -->
                  <div class="upload-size-info" v-if="pdfFile">
                    <span
                      class="size-text"
                      :class="{
                        warning: pdfFile.size / 1024 / 1024 > 7 && pdfFile.size / 1024 / 1024 <= 10,
                        danger: pdfFile.size / 1024 / 1024 > 10,
                        normal: pdfFile.size / 1024 / 1024 <= 7,
                      }"
                    >
                      PDF大小：{{ (pdfFile.size / 1024 / 1024).toFixed(2) }}MB / 10MB
                    </span>
                    <el-progress
                      class="size-progress"
                      :percentage="(pdfFile.size / 1024 / 1024) * 10"
                      :status="
                        pdfFile.size / 1024 / 1024 > 10
                          ? 'exception'
                          : pdfFile.size / 1024 / 1024 > 7
                            ? 'warning'
                            : ''
                      "
                      :stroke-width="8"
                    ></el-progress>
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </div>
        </el-form>
      </div>
    </el-card>

    <!-- 添加商品审核弹窗 -->
    <ProductAuditDialog
      v-model:visible="auditDialogVisible"
      :product-name="submittedProductName"
      @view-products="goToProductList"
      @add-another="closeAuditDialog"
    />
  </div>
</template>

<script setup lang="ts">
import { getCategoryTree } from '@/api/category'
import { addProduct } from '@/api/product'
import ProductAuditDialog from '@/components/ProductAuditDialog.vue'
import { useUserStore } from '@/stores/user'
import { Delete, Edit, InfoFilled, Plus, QuestionFilled } from '@element-plus/icons-vue'
import type { UploadUserFile } from 'element-plus'
import { ElMessage } from 'element-plus'
import { computed, nextTick, onMounted, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const formRef = ref()
const submitLoading = ref(false)
const galleryList = ref<UploadUserFile[]>([])
const graphicIntroductList = ref<UploadUserFile[]>([])
const userStore = useUserStore()



// 分类数据
const categoryOptions = ref<any[]>([])
const thirdLevelCategories = ref<{ id: number; fullName: string }[]>([])

// 品牌选项
const brandOptions = [
  { id: 1, name: '华为' },
  { id: 2, name: '小米' },
  { id: 3, name: '苹果' },
  { id: 4, name: '三星' },
  { id: 5, name: 'OPPO' },
  { id: 6, name: 'vivo' },
]

// 单位选项
const unitOptions = ['件', '个', '套', '盒', '袋', '箱', '瓶', '千克', '克', '米', '厘米']

// 属性输入状态
const inputVisible = reactive({
  key: false,
  value: false,
})
// 商品属性值
const inputValue = reactive({
  key: '',
  value: '',
})

const currentKey = ref('')
const attrValueInput = ref(null)
const currentAction = ref('add') // 'add' 或 'edit'
const currentEditIndex = ref(-1) // 正在编辑的属性索引
interface CurrentAttr {
  index: number
  value: string
}
// 商品属性列表项form.attrList.value项
const currentAttr = reactive<CurrentAttr>({
  index: 0,
  value: '',
})
// 预定义的销售属性
const predefinedAttrs = [
  {
    groupName: '基本属性',
    attrs: ['颜色', '尺寸', '材质', '款式', '重量'],
  },
  {
    groupName: '电子产品',
    attrs: ['内存', '存储容量', '处理器', '屏幕尺寸', '电池容量', '操作系统'],
  },
  {
    groupName: '服装',
    attrs: ['季节', '适用人群', '领型', '袖长', '风格', '版型'],
  },
  {
    groupName: '食品',
    attrs: ['口味', '产地', '保质期', '包装', '净含量'],
  },
]

// 已选择的属性（用于复选框组）
const selectedAttrs = ref(Array(predefinedAttrs.length).fill([]))

// 表单数据
const form = reactive({
  product_snapshot_id: null,
  brand_id: null,
  category_id: null,
  out_product_id: '',
  name: '',
  pic: '',
  album_pics: '',
  publish_status: 1,
  sort: 0,
  price: 0,
  unit: '件',
  weight: 0,
  inventory: 0,
  detail_html: '',
  brand_name: '',
  product_category_name: '',
  product_attr: '',
  attrList: [] as { key: string; value: string[] }[],
})

// 在表单数据中添加存储原始文件的字段
const mainPicFile = ref<File | null>(null)
const albumPicFiles = ref<File[]>([])
const graphicIntroductFiles = ref<File[]>([])
const pdfFile = ref<File | null>(null)
const pdfFileList = ref<UploadUserFile[]>([])
// 计算当前所有图片文件的总大小（单位：MB）
const totalImagesSize = computed(() => {
  let totalSize = 0

  // 主图大小
  if (mainPicFile.value) {
    totalSize += mainPicFile.value.size / 1024 / 1024
  }

  // 相册图片大小
  albumPicFiles.value.forEach((file) => {
    totalSize += file.size / 1024 / 1024
  })

  // 图文介绍图片大小
  graphicIntroductFiles.value.forEach((file) => {
    totalSize += file.size / 1024 / 1024
  })

  // PDF文档大小
  if (pdfFile.value) {
    totalSize += pdfFile.value.size / 1024 / 1024
  }

  return parseFloat(totalSize.toFixed(2))
})

// 检查总文件大小是否超过限制
const checkTotalFileSize = (newFileSize = 0) => {
  const currentTotal = totalImagesSize.value
  const projectedTotal = currentTotal + newFileSize / 1024 / 1024 // 新增文件的大小（MB）

  if (projectedTotal > 10) {
    ElMessage.error(
      `图片总大小不能超过10MB，当前已用${currentTotal.toFixed(2)}MB，添加此图片后将达到${projectedTotal.toFixed(2)}MB`,
    )
    return false
  }

  return true
}

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入商品名称', trigger: 'blur' },
    { min: 2, max: 200, message: '长度在 2 到 200 个字符', trigger: 'blur' },
  ],
  category_id: [{ required: true, message: '请选择商品分类', trigger: 'change' }],
  price: [{ required: true, message: '请输入商品价格', trigger: 'blur' }],
  pic: [{ required: true, message: '请上传商品主图', trigger: 'change' }],
  albumPics: [{ required: true, message: '请至少上传一张商品相册', trigger: 'change' }],
  inventory: [{ required: true, message: '请输入商品库存', trigger: 'blur' }],
  detail_html: [{ min: 0, max: 1000, message: '最多1000个字符', trigger: 'blur' }],
  brand_name: [{ required: true, message: '请输入商品品牌名称,若无品牌则填写无', trigger: 'blur' }],
}

// 临时模拟获取分类数据
const fetchCategories = async () => {
  try {
    // 调用真实接口获取分类数据
    const response = await getCategoryTree()
    // 接口返回的数据结构
    categoryOptions.value = response.data
    processThirdLevelCategories(response.data)
  } catch (error) {
    console.error('获取分类数据失败:', error)
    ElMessage.error('获取分类数据失败，请刷新重试')
  }
}

// 处理三级分类数据
const processThirdLevelCategories = (categories: any[]) => {
  const result: { id: number; fullName: string }[] = []

  const traverse = (categories: any[], parentNames: string[] = []) => {
    categories.forEach((category) => {
      const currentPath = [...parentNames, category.name]

      // 如果是三级分类（level为2），则添加到结果数组
      if (category.level === 2) {
        result.push({
          id: category.id,
          fullName: currentPath.join(' > '),
        })
      }

      // 递归遍历子分类
      if (category.children && category.children.length > 0) {
        traverse(category.children, currentPath)
      }
    })
  }

  traverse(categories)
  thirdLevelCategories.value = result
}

// 处理主图上传
const handleMainImageChange = (file: UploadUserFile) => {
  // 检查文件类型
  const isImage = file.raw?.type === 'image/jpeg' || file.raw?.type === 'image/png'
  if (!isImage) {
    ElMessage.error('商品主图只能是JPG或PNG格式!')
    return false
  }

  // 检查文件对象
  if (!file.raw) {
    ElMessage.error('无法获取文件对象!')
    return false
  }

  // 检查文件大小
  const isLt5M = file.size! / 1024 / 1024 < 5
  if (!isLt5M) {
    ElMessage.error('商品主图大小不能超过5MB!')
    return false
  }

  // 如果已经有主图，先计算减去当前主图后的总大小
  let additionalSize = file.size!
  if (mainPicFile.value) {
    additionalSize = file.size! - mainPicFile.value.size
  }

  // 检查添加此图片后总大小是否超过限制
  if (additionalSize > 0 && !checkTotalFileSize(additionalSize)) {
    return false
  }

  // 保存原始文件对象
  mainPicFile.value = file.raw

  // 设置预览
  const reader = new FileReader()
  reader.onload = (e: any) => {
    form.pic = e.target.result
  }
  reader.readAsDataURL(file.raw)

  console.log('主图文件已保存:', file.raw.name, file.raw.size, file.raw.type)
  console.log('当前图片总大小:', totalImagesSize.value, 'MB')
  return false // 阻止自动上传
}

// 处理相册上传
const handleGalleryChange = (file: UploadUserFile, fileList: UploadUserFile[]) => {
  // 检查文件类型和大小
  const isImage = file.raw?.type === 'image/jpeg' || file.raw?.type === 'image/png'
  if (!isImage) {
    ElMessage.error('商品图片只能是JPG或PNG格式!')
    return false
  }

  // 检查单个文件大小
  const isLt5M = file.size! / 1024 / 1024 < 5
  if (!isLt5M) {
    ElMessage.error('商品图片大小不能超过5MB!')
    return false
  }

  // 限制图片数量
  if (fileList.length > 5) {
    ElMessage.warning('最多只能上传5张商品展示图')
    fileList.pop() // 移除最后一个
    return false
  }

  // 检查文件对象
  if (!file.raw) {
    ElMessage.error('无法获取文件对象!')
    return false
  }

  // 检查添加此图片后总大小是否超过限制
  if (!checkTotalFileSize(file.size!)) {
    // 从fileList中移除这个文件
    const index = fileList.findIndex((item) => item.uid === file.uid)
    if (index !== -1) {
      fileList.splice(index, 1)
      galleryList.value = [...fileList]
    }
    return false
  }

  // 更新相册文件列表
  albumPicFiles.value = []
  fileList.forEach((item) => {
    if (item.raw) {
      albumPicFiles.value.push(item.raw)
    }
  })

  // 更新预览图
  const reader = new FileReader()
  reader.onload = () => {
    // 更新相册预览
    galleryList.value = fileList

    // 更新album_pics字段
    const galleries = fileList
      .map((item) => {
        if (item.url) return item.url
        if (item.raw) {
          const rawFile = item.raw as File
          return URL.createObjectURL(rawFile)
        }
        return ''
      })
      .filter((url) => url !== '')

    form.album_pics = galleries.join(',')
  }
  reader.readAsDataURL(file.raw)

  console.log('相册文件已更新, 当前数量:', albumPicFiles.value.length)
  console.log('当前图片总大小:', totalImagesSize.value, 'MB')
  return false // 阻止自动上传
}

// 处理图文介绍图片上传
const handleIntroductionChange = (file: UploadUserFile, fileList: UploadUserFile[]) => {
  // 检查文件类型和大小
  const isImage = file.raw?.type === 'image/jpeg' || file.raw?.type === 'image/png'
  if (!isImage) {
    ElMessage.error('商品图片只能是JPG或PNG格式!')
    return false
  }

  // 检查单个文件大小
  const isLt5M = file.size! / 1024 / 1024 < 5
  if (!isLt5M) {
    ElMessage.error('商品图片大小不能超过5MB!')
    return false
  }

  // 限制图片数量
  if (fileList.length > 5) {
    ElMessage.warning('最多只能上传5张商品展示图')
    fileList.pop() // 移除最后一个
    return false
  }

  // 检查文件对象
  if (!file.raw) {
    ElMessage.error('无法获取文件对象!')
    return false
  }

  // 检查添加此图片后总大小是否超过限制
  if (!checkTotalFileSize(file.size!)) {
    // 从fileList中移除这个文件
    const index = fileList.findIndex((item) => item.uid === file.uid)
    if (index !== -1) {
      fileList.splice(index, 1)
      graphicIntroductList.value = [...fileList]
    }
    return false
  }

  // 更新文件列表
  graphicIntroductList.value = fileList

  // 更新原始文件列表
  graphicIntroductFiles.value = fileList.map((item) => item.raw).filter(Boolean) as File[]

  return false
}
// 新增beforePdfUpload验证方法
const beforePdfUpload = (file: File) => {
  const isPdf = file.type === 'application/pdf'
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isPdf) {
    ElMessage.error('只能上传PDF格式的文件!')
    return false
  }

  if (!isLt10M) {
    ElMessage.error('PDF文件大小不能超过10MB!')
    return false
  }

  // 检查添加此PDF后总大小是否超过限制
  const newTotalSize = totalImagesSize.value + file.size / 1024 / 1024
  if (newTotalSize > 15) {
    // 设置总大小限制为15MB（图片10MB + PDF 5MB）
    ElMessage.error(`总文件大小不能超过15MB，当前已用${totalImagesSize.value.toFixed(2)}MB`)
    return false
  }

  return true
}

// 修改handlePdfChange方法
const handlePdfChange = (file: UploadUserFile) => {
  if (!file.raw) {
    ElMessage.error('无法获取文件对象!')
    return false
  }

  // 使用beforePdfUpload进行验证
  if (!beforePdfUpload(file.raw)) {
    return false
  }

  // 保存原始文件对象
  pdfFile.value = file.raw
  pdfFileList.value = [file]

  console.log('PDF文件已保存:', file.name, file.size, file.type)
  return false
}

// pdf移除
const handlePdfRemove = () => {
  if (pdfFile.value) {
    console.log('移除PDF文件:', pdfFile.value.name)
  }
  pdfFile.value = null
  pdfFileList.value = []
}
// 处理图文介绍图片移除
const handleIntroductionRemove = (file: UploadUserFile) => {
  const index = graphicIntroductList.value.findIndex((item) => item.uid === file.uid)
  if (index !== -1) {
    graphicIntroductList.value.splice(index, 1)
    graphicIntroductFiles.value = graphicIntroductList.value
      .map((item) => item.raw)
      .filter(Boolean) as File[]
  }
}
// 处理相册图片移除
const handleGalleryRemove = (file: UploadUserFile) => {
  const index = galleryList.value.indexOf(file)
  if (index !== -1) {
    // 更新预览图片
    const galleries = form.album_pics.split(',')
    galleries.splice(index, 1)
    form.album_pics = galleries.join(',')

    // 更新原始文件列表
    albumPicFiles.value.splice(index, 1)
  }
}

// 编辑属性值
const editAttrValue = (attrIndex: number, key: string, value: string, attrValueIndex: number) => {
  console.log('attrValueIndex', attrValueIndex)
  currentKey.value = key
  console.log(form.attrList[attrIndex].value[attrValueIndex])
  currentAction.value = 'edit'
  // 在属性列表中查找匹配的索引位置，用于后续更新操作
  // currentEditIndex.value = form.attrList.findIndex((item) => item.key === key)
  currentEditIndex.value = attrIndex
  form.attrList[currentEditIndex.value].value[attrValueIndex] = inputValue.value
  // 显示属性值输入对话框
  inputVisible.value = true
  currentAttr.index = attrValueIndex
  currentAttr.value = inputValue.value
  nextTick(() => {
    if (inputVisible.value) {
      // 获取底层input元素并触发聚焦
      // ;(attrValueInput.value as any).$el.querySelector('input').focus()
      ;(attrValueInput.value as any).focus()
    }
  })
}

// 处理属性选择变化
const handleAttrChange = (checked: boolean, groupIndex: number, attrName: string) => {
  if (checked) {
    // 添加属性时，需要用户输入属性值
    currentKey.value = attrName
    inputValue.value = ''
    currentAction.value = 'add'
    currentEditIndex.value = -1
    inputVisible.value = true
    nextTick(() => {
      if (attrValueInput.value) {
        ;(attrValueInput.value as any).$el.querySelector('input').focus()
      }
    })
  } else {
    // 取消选择属性时，从已选属性列表中移除
    const index = form.attrList.findIndex((item) => item.key === attrName)
    if (index !== -1) {
      form.attrList.splice(index, 1)
      form.product_attr = JSON.stringify(form.attrList)
    }
  }
}

const cancelAttributeValue = () => {
  inputVisible.value = false
  inputValue.value = ''

  if (currentAction.value === 'add') {
    // 如果是添加新属性时取消，需要更新选择状态
    predefinedAttrs.forEach((group, groupIndex) => {
      const attrIndex = group.attrs.indexOf(currentKey.value)
      if (attrIndex !== -1) {
        const groupAttrs = [...selectedAttrs.value[groupIndex]]
        const attrIdx = groupAttrs.indexOf(currentKey.value)
        if (attrIdx !== -1) {
          groupAttrs.splice(attrIdx, 1)
          selectedAttrs.value[groupIndex] = groupAttrs
        }
      }
    })
  }

  currentKey.value = ''
  currentAction.value = 'add'
  currentEditIndex.value = -1
}

// 确认添加或编辑属性值
const handleValueConfirm = () => {
  if (inputValue.value.trim() && currentKey.value) {
    if (currentAction.value === 'edit' && currentEditIndex.value !== -1) {
      // 编辑现有属性
      console.log('编辑属性')
      form.attrList[currentEditIndex.value].value[currentAttr.index] = inputValue.value.trim()
      ElMessage.success(`属性 ${currentKey.value} 已更新`)
    } else {
      // 检查是否已存在相同key的属性
      const existingIndex = form.attrList.findIndex((item) => item.key === currentKey.value)
      console.log('存在', form.attrList[existingIndex])

      if (existingIndex !== -1) {
        // 检查当前类型的属性列表中是否存在与输入框重复项
        if (form.attrList[existingIndex].value.includes(inputValue.value.trim())) {
          ElMessage.error(`属性${inputValue.value.trim()}已存在，请重新输入`)
          inputValue.value = ''
          return
        }
        // 在已存在的属性值列表中添加一项
        form.attrList[existingIndex].value.push(inputValue.value.trim())
        ElMessage.success(`属性 ${currentKey.value}：${inputValue.value} 已添加`)
      } else {
        // 添加新属性类型
        form.attrList.push({
          key: currentKey.value,
          value: [inputValue.value],
        })
        ElMessage.success(`属性 ${currentKey.value} 已添加`)
      }
    }

    // 更新product_attr字段为JSON字符串
    form.product_attr = JSON.stringify(form.attrList)
  } else {
    ElMessage.warning('属性值不能为空')
    return
  }

  inputVisible.value = false
  currentKey.value = ''
  inputValue.value = ''
  currentAction.value = 'add'
  currentEditIndex.value = -1
  console.log('form.attrList:', form.attrList)
}

// 移除属性
const handleRemoveAttr = (index: number, attrIndex: number) => {
  const removedAttr = form.attrList[index].value[attrIndex]

  ElMessage.success(`属性 ${removedAttr} 已移除`)
  form.attrList[index].value.splice(attrIndex, 1)
  // 如果当前属性类型中属性列表已清空，移除此属性类型项
  if (form.attrList[index].value.length === 0) {
    form.attrList.splice(index, 1)
  }
  console.log(form.product_attr)

  // 更新product_attr字段为JSON字符串
  form.product_attr = JSON.stringify(form.attrList)
  console.log(form.product_attr)

  // 同步更新选择状态
  predefinedAttrs.forEach((group, groupIndex) => {
    const attrIndex = group.attrs.indexOf(removedAttr)
    if (attrIndex !== -1) {
      const groupAttrs = [...selectedAttrs.value[groupIndex]]
      const attrIdx = groupAttrs.indexOf(removedAttr)
      if (attrIdx !== -1) {
        groupAttrs.splice(attrIdx, 1)
        selectedAttrs.value[groupIndex] = groupAttrs
      }
    }
  })
}

// 处理表单提交
const handleSubmit = async () => {
  try {
    submitLoading.value = true
    console.log('===== 开始提交商品表单 =====')

    // 验证表单
    if (!form.name) {
      ElMessage.error('请输入商品名称')
      return
    }

    if (!form.category_id) {
      ElMessage.error('请选择商品分类')
      return
    }

    if (form.price === 0 || form.price === undefined) {
      ElMessage.error('请输入商品价格')
      return
    }
    if (!form.inventory) {
      ElMessage.success('请输入商品库存')
      return
    }
    if (!mainPicFile.value) {
      ElMessage.error('请上传商品主图')
      return
    }
    if (albumPicFiles.value.length === 0) {
      ElMessage.error('请至少上传一张商品相册')
      return
    }
    // 验证图片总大小
    if (totalImagesSize.value > 10) {
      ElMessage.error(
        `图片总大小超过限制，当前为${totalImagesSize.value.toFixed(2)}MB，最大允许10MB`,
      )
      return
    }
    // 新增PDF验证
    if (pdfFile.value && pdfFile.value.size / 1024 / 1024 > 10) {
      ElMessage.error('PDF文件大小不能超过10MB!')
      return
    }

    // 总大小验证（包含PDF）
    if (totalImagesSize.value > 15) {
      ElMessage.error(`总文件大小不能超过15MB，当前为${totalImagesSize.value.toFixed(2)}MB`)
      return
    }
    // 获取当前登录商家ID
    const sellerId = userStore.userInfo?.id

    console.log('商家ID:', sellerId)
    if (!sellerId) {
      ElMessage.error('未获取到商家ID，请重新登录')
      return
    }

    // 准备分类名称和品牌名称
    if (form.category_id) {
      const category = thirdLevelCategories.value.find((item) => item.id === form.category_id)
      if (category) {
        form.product_category_name = category.fullName
      }
    }

    if (form.brand_name) {
      const brand = brandOptions.find((item) => item.name === form.brand_name)
      if (brand) {
        form.brand_id = brand.id
      }
    }

    // 处理表单数据中的属性JSON
    const attrJson = JSON.stringify(form.attrList || [])
    console.log('商品属性JSON:', attrJson)

    // 创建要传递给后端的商品对象，确保字段名称与后端匹配
    // 重要：严格不包含任何图片相关字段
    const product = {
      productSnapshotId: null, // 新增时为空
      brandId: form.brand_id || null,
      brandName: form.brand_name || '',
      categoryId: form.category_id || null,
      productCategoryName: form.product_category_name || '',
      outProductId: form.out_product_id || '',
      name: form.name,
      inventory: form.inventory || 0,
      publishStatus: form.publish_status,
      sort: form.sort || 0,
      price: form.price || 0,
      unit: form.unit || '',
      weight: form.weight || 0,
      detailHtml: form.detail_html || '',
      productAttr: attrJson,
      // 不包含pic和albumPics字段
    }

    // 详细打印将要提交的商品数据
    console.log('准备调用addProduct API，转换后的商品数据:', {
      name: product.name,
      brandId: product.brandId,
      categoryId: product.categoryId,
      price: product.price,
      inventory: form.inventory,
      publishStatus: product.publishStatus,
      productAttr: attrJson.length < 200 ? attrJson : attrJson.substring(0, 100) + '...',
    })

    console.log('图片信息（将单独传递）:')
    console.log(
      '- 主图:',
      mainPicFile.value ? `${mainPicFile.value.name}, ${mainPicFile.value.size} 字节` : '未上传',
    )
    console.log('- 相册图片数量:', albumPicFiles.value.length)
    if (albumPicFiles.value.length > 0) {
      console.log('  相册图片明细:')
      albumPicFiles.value.forEach((file, index) => {
        console.log(`  - 相册图${index + 1}: ${file.name}, ${file.size} 字节, 类型: ${file.type}`)
      })
    }

    // 调用API模块中的方法，传递文件对象
    console.log('开始调用API，传递商品数据和图片文件...')
    console.log(
      '传入的图片：',
      mainPicFile.value,
      albumPicFiles.value,
      graphicIntroductFiles.value,
      pdfFile.value,
    )

    const result = await addProduct(
      sellerId,
      product,
      mainPicFile.value,
      albumPicFiles.value,
      graphicIntroductFiles.value,
      pdfFile.value,
    )
    console.log('addProduct API返回结果:', result)

    if (result.code === 1) {
      // 显示成功消息
      ElMessage.success('商品发布成功')
      console.log('商品添加成功，准备重置表单并跳转')

      console.log('响应消息', result.data)

      // 打开审核弹窗而不是直接跳转
      auditDialogVisible.value = true

      // 保存当前商品名称用于显示在弹窗中
      submittedProductName.value = form.name

      console.log('表单信息：', form)

      // 表单重置
      resetForm()
    } else {
      ElMessage.error(result.msg || '商品发布失败')
      console.error('商品添加失败:', result.msg)
    }
  } catch (error: any) {
    console.error('===== 商品提交失败 =====', error)

    // 分析错误类型并显示更具体的错误信息
    let errorMessage = '商品发布失败'

    if (error.message) {
      errorMessage += `: ${error.message}`
      console.error('错误消息:', error.message)
    }

    if (error.response) {
      console.error('服务器错误响应:', error.response)
      if (error.response.status) {
        errorMessage += ` (状态码: ${error.response.status})`
      }
    }

    ElMessage.error(errorMessage)
  } finally {
    console.log('===== 表单提交处理完成 =====')
    submitLoading.value = false
  }
}

// 重置表单
const resetForm = () => {
  form.name = ''
  form.brand_name = ''
  form.category_id = null
  form.product_category_name = ''
  form.price = 0
  form.detail_html = ''
  form.pic = ''
  form.album_pics = ''
  form.publish_status = 1
  form.sort = 0
  form.weight = 0
  form.product_attr = ''
  form.attrList = []

  // 重置文件
  mainPicFile.value = null
  albumPicFiles.value = []
  galleryList.value = []
  graphicIntroductList.value = []
  // 重置其他状态
  selectedAttrs.value = new Array(predefinedAttrs.length).fill([])

  // 如果表单引用存在，重置验证状态
  formRef.value?.resetFields()

  console.log('表单已重置')
}

// 在分类树中递归查找分类名称
const findCategoryNameById = (categories: any[], categoryId: number): any | null => {
  for (const category of categories) {
    if (category.id === categoryId) {
      return category
    }
    if (category.children && category.children.length > 0) {
      const found = findCategoryNameById(category.children, categoryId)
      if (found) return found
    }
  }
  return null
}

// 取消
const handleCancel = () => {
  ElMessage.warning('正在返回商品列表')
  router.push('/main/product/list')
}

// 商品审核弹窗相关
const auditDialogVisible = ref(false)
const submittedProductName = ref('')

// 关闭审核弹窗，留在添加商品页面
const closeAuditDialog = () => {
  auditDialogVisible.value = false
}

// 前往商品列表页面
const goToProductList = () => {
  router.push('/main/product/list')
}

// 初始化加载分类数据
onMounted(() => {
  console.log("当前登录的用户信息userStore",userStore.userInfo);
  
  fetchCategories()
})
</script>

<style scoped lang="scss">
.page-container {
  padding: 16px;

  .page-card {
    :deep(.el-card__header) {
      padding: 16px 20px;
    }

    :deep(.el-card__body) {
      padding: 20px;
    }
  }

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .page-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }

    .page-actions {
      display: flex;
      gap: 12px;
    }
  }

  .form-section {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 8px;

    .section-title {
      margin: 0 0 20px;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      border-left: 4px solid var(--el-color-primary);
      padding-left: 10px;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  .main-image-uploader {
    .main-image {
      width: 200px;
      height: 200px;
      object-fit: cover;
    }

    .upload-placeholder {
      width: 200px;
      height: 200px;
      border: 1px dashed #d9d9d9;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      cursor: pointer;

      &:hover {
        border-color: var(--el-color-primary);
        color: var(--el-color-primary);
      }

      .el-icon {
        font-size: 28px;
        color: #8c939d;
        margin-bottom: 8px;
      }

      .upload-text {
        font-size: 14px;
        color: #8c939d;
      }
    }
  }

  .gallery-uploader {
    :deep(.el-upload--picture-card) {
      width: 150px;
      height: 150px;
      line-height: 150px;
    }

    :deep(.el-upload-list--picture-card .el-upload-list__item) {
      width: 150px;
      height: 150px;
    }
  }

  .upload-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 8px;
  }

  // 添加图片总大小提示样式
  .upload-size-info {
    font-size: 12px;
    margin-top: 8px;
    display: flex;
    align-items: center;

    .size-text {
      &.warning {
        color: #e6a23c;
      }

      &.danger {
        color: #f56c6c;
      }

      &.normal {
        color: #67c23a;
      }
    }

    .size-progress {
      margin-left: 10px;
      flex: 1;
      max-width: 200px;
    }
  }

  .attr-container {
    display: flex;
    gap: 20px;
    width: 100%;
    min-height: 400px;
    border-radius: 4px;
    overflow: hidden;

    @media screen and (max-width: 768px) {
      flex-direction: column;
    }
    .attr-selection-panel,
    .attr-preview-panel {
      flex: 1;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    }
  }

  .panel-header {
    padding: 12px 15px;
    font-weight: 600;
    color: #303133;
    background-color: #f5f7fa;
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .help-icon {
      color: #909399;
      cursor: pointer;
      font-size: 15px;

      &:hover {
        color: var(--el-color-primary);
      }
    }

    .attr-count {
      font-size: 12px;
      font-weight: normal;
      color: #606266;

      b {
        color: var(--el-color-primary);
      }
    }
  }

  .attr-tabs {
    flex: 1;
    margin: 0;
    display: flex;
    flex-direction: column;

    :deep(.el-tabs__content) {
      padding: 15px;
      flex: 1;
      overflow-y: auto;
    }
  }

  .attr-options {
    max-height: 300px;
    overflow-y: auto;
    padding-right: 5px;

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #e0e0e0;
      border-radius: 4px;
    }
  }

  .attr-checkbox {
    margin-right: 20px;
    margin-bottom: 12px;
    transition: all 0.3s;

    &:hover {
      color: var(--el-color-primary);
    }
  }

  .attr-tags-container {
    padding: 15px;
    flex: 1;
    overflow-y: auto;
    min-height: 250px;

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #e0e0e0;
      border-radius: 4px;
    }
  }

  .attr-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    width: 100%;
  }

  .attr-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid var(--el-color-primary);
    transition: all 0.3s;

    &:hover {
      background: #edf2fc;
      transform: translateY(-2px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .attr-item-content {
      flex: 1;
    }

    .attr-item-key {
      font-weight: 600;
      color: #303133;
      margin-bottom: 4px;
    }

    .attr-item-value {
      color: var(--el-color-primary);
      font-size: 13px;
    }

    .attr-item-actions {
      display: flex;
      gap: 8px;
    }
  }

  .empty-tip {
    margin: 0 0 5px;
    color: #909399;
  }

  .empty-tip-sub {
    margin: 0;
    font-size: 12px;
    color: #c0c4cc;
  }

  .no-attrs {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .attr-tips {
    margin-top: 15px;
  }

  .field-tip {
    display: flex;
    align-items: center;
    margin-top: 5px;
    font-size: 12px;
    color: #909399;

    .el-icon {
      margin-right: 5px;
      font-size: 14px;
      color: #a0a4a9;
    }

    span {
      line-height: 1.2;
    }
  }
}

// 响应式调整
@media screen and (max-width: 768px) {
  .page-container {
    .form-section {
      padding: 15px;
    }

    :deep(.el-form-item) {
      margin-bottom: 15px;
    }
  }
}
</style>
