<svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#2b3a5a" />
      <stop offset="100%" stop-color="#1a2942" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#4C84FF" />
      <stop offset="100%" stop-color="#2B5AE8" />
    </linearGradient>
    <filter id="shadow" x="-8%" y="-8%" width="116%" height="116%">
      <feDropShadow dx="0" dy="2" stdDeviation="3" flood-opacity="0.3" />
    </filter>
    <filter id="glow" x="-15%" y="-15%" width="130%" height="130%">
      <feGaussianBlur stdDeviation="2" result="blur" />
      <feFlood flood-color="#4C84FF" flood-opacity="0.3" result="color" />
      <feComposite in="color" in2="blur" operator="in" result="shadow" />
      <feComposite in="SourceGraphic" in2="shadow" operator="over" />
    </filter>
  </defs>
  <rect width="100" height="100" rx="20" fill="url(#bgGradient)" filter="url(#shadow)" />
  <g filter="url(#glow)">
    <path d="M30 40L40 40L40 68L30 68L30 40Z" fill="white" />
    <path d="M43 40L53 40L53 68L43 68L43 40Z" fill="white" />
    <path d="M56 40L70 40L70 48L56 48L56 40Z" fill="white" />
    <path d="M56 52L70 52L70 60L56 60L56 52Z" fill="white" />
    <path d="M56 64L70 64L70 72L56 72L56 64Z" fill="white" />
  </g>
  <circle cx="75" cy="30" r="10" fill="url(#accentGradient)" stroke="white" stroke-width="2" />
</svg> 