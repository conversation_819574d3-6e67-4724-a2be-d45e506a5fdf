<template>
  <el-dialog
    v-model="visible"
    title="订单导出"
    width="600px"
    :before-close="handleClose"
  >
    <div class="export-content">
      <div class="export-summary">
        <el-alert
          :title="`准备导出 ${orders.length} 个订单`"
          type="info"
          :closable="false"
          show-icon
        />
      </div>

      <el-form
        ref="formRef"
        :model="exportForm"
        :rules="rules"
        label-width="120px"
        class="export-form"
      >
        <el-form-item label="导出格式" prop="format" required>
          <el-radio-group v-model="exportForm.format">
            <el-radio label="excel">Excel (.xlsx)</el-radio>
            <el-radio label="csv">CSV (.csv)</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="导出字段" prop="fields" required>
          <el-checkbox-group v-model="exportForm.fields">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-checkbox label="number">订单号</el-checkbox>
              </el-col>
              <el-col :span="8">
                <el-checkbox label="buyerId">买家ID</el-checkbox>
              </el-col>
              <el-col :span="8">
                <el-checkbox label="amount">订单金额</el-checkbox>
              </el-col>
              <el-col :span="8">
                <el-checkbox label="status">订单状态</el-checkbox>
              </el-col>
              <el-col :span="8">
                <el-checkbox label="orderTime">下单时间</el-checkbox>
              </el-col>
              <el-col :span="8">
                <el-checkbox label="payTime">支付时间</el-checkbox>
              </el-col>
              <el-col :span="8">
                <el-checkbox label="shipTime">发货时间</el-checkbox>
              </el-col>
              <el-col :span="8">
                <el-checkbox label="completeTime">完成时间</el-checkbox>
              </el-col>
              <el-col :span="8">
                <el-checkbox label="payMethod">支付方式</el-checkbox>
              </el-col>
              <el-col :span="8">
                <el-checkbox label="logisticsCompany">物流公司</el-checkbox>
              </el-col>
              <el-col :span="8">
                <el-checkbox label="trackingNumber">快递单号</el-checkbox>
              </el-col>
              <el-col :span="8">
                <el-checkbox label="orderRemark">订单备注</el-checkbox>
              </el-col>
            </el-row>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="文件名称" prop="filename">
          <el-input
            v-model="exportForm.filename"
            placeholder="请输入文件名称（不含扩展名）"
            clearable
          />
        </el-form-item>

        <el-form-item label="导出选项">
          <el-checkbox v-model="exportForm.includeDetails">包含订单详情</el-checkbox>
          <el-checkbox v-model="exportForm.includeAddress">包含收货地址</el-checkbox>
        </el-form-item>
      </el-form>

      <div class="export-preview">
        <h4>导出预览</h4>
        <div class="preview-info">
          <p><strong>格式：</strong>{{ exportForm.format.toUpperCase() }}</p>
          <p><strong>字段数量：</strong>{{ exportForm.fields.length }} 个</p>
          <p><strong>订单数量：</strong>{{ orders.length }} 个</p>
          <p><strong>预计文件大小：</strong>{{ estimatedFileSize }}</p>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleExport"
          :loading="exporting"
          :disabled="exportForm.fields.length === 0"
        >
          开始导出
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { batchExport } from '@/api/order'
import type { OrderVO } from '@/types/order'

interface Props {
  modelValue: boolean
  orders: OrderVO[]
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'exported', downloadUrl: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = ref(false)
const exporting = ref(false)
const formRef = ref<FormInstance>()

// 导出表单
const exportForm = reactive({
  format: 'excel',
  fields: ['number', 'buyerId', 'amount', 'status', 'orderTime'],
  filename: '',
  includeDetails: false,
  includeAddress: false
})

// 表单验证规则
const rules: FormRules = {
  format: [
    { required: true, message: '请选择导出格式', trigger: 'change' }
  ],
  fields: [
    { required: true, message: '请选择导出字段', trigger: 'change' },
    { type: 'array', min: 1, message: '至少选择一个导出字段', trigger: 'change' }
  ],
  filename: [
    { required: true, message: '请输入文件名称', trigger: 'blur' },
    { min: 1, max: 50, message: '文件名称长度在 1 到 50 个字符', trigger: 'blur' }
  ]
}

// 预计文件大小
const estimatedFileSize = computed(() => {
  const recordSize = exportForm.fields.length * 20 // 每个字段约20字节
  const totalSize = props.orders.length * recordSize
  
  if (totalSize < 1024) {
    return `${totalSize} B`
  } else if (totalSize < 1024 * 1024) {
    return `${(totalSize / 1024).toFixed(1)} KB`
  } else {
    return `${(totalSize / (1024 * 1024)).toFixed(1)} MB`
  }
})

// 监听显示状态
watch(
  () => props.modelValue,
  (newVal) => {
    visible.value = newVal
    if (newVal) {
      resetForm()
    }
  }
)

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 重置表单
const resetForm = () => {
  exportForm.format = 'excel'
  exportForm.fields = ['number', 'buyerId', 'amount', 'status', 'orderTime']
  exportForm.filename = `订单导出_${new Date().toISOString().slice(0, 10)}`
  exportForm.includeDetails = false
  exportForm.includeAddress = false
  formRef.value?.clearValidate()
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
}

// 开始导出
const handleExport = async () => {
  try {
    await formRef.value?.validate()
    
    await ElMessageBox.confirm(
      `确认导出 ${props.orders.length} 个订单？`,
      '确认导出',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    exporting.value = true
    
    const orderIds = props.orders.map(order => order.id)
    const exportData = {
      orderIds,
      exportFormat: exportForm.format,
      exportFields: exportForm.fields,
      filename: exportForm.filename,
      includeDetails: exportForm.includeDetails,
      includeAddress: exportForm.includeAddress
    }
    
    const response = await batchExport(exportData)
    
    if (response.code === 1) {
      const result = response.data
      if (result.downloadUrl) {
        ElMessage.success('导出成功，正在下载...')
        emit('exported', result.downloadUrl)
        
        // 自动下载文件
        const link = document.createElement('a')
        link.href = result.downloadUrl
        link.download = `${exportForm.filename}.${exportForm.format}`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      } else {
        ElMessage.success('导出任务已提交，请稍后查看下载中心')
      }
      
      handleClose()
    } else {
      ElMessage.error(response.msg || '导出失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('导出失败:', error)
      ElMessage.error('导出失败')
    }
  } finally {
    exporting.value = false
  }
}
</script>

<style scoped lang="scss">
.export-content {
  .export-summary {
    margin-bottom: 20px;
  }
  
  .export-form {
    .el-checkbox-group {
      .el-col {
        margin-bottom: 10px;
      }
    }
  }
  
  .export-preview {
    margin-top: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;
    
    h4 {
      margin: 0 0 12px 0;
      color: #303133;
      font-size: 14px;
      font-weight: 600;
    }
    
    .preview-info {
      p {
        margin: 8px 0;
        font-size: 14px;
        color: #606266;
        
        strong {
          color: #303133;
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
