﻿<template>
  <div class="login-container">
    <div class="login-content">
      <div class="login-box">
        <div class="login-header">
          <h1 class="login-title">Sharewharf <span>商城</span></h1>
          <p class="login-subtitle">欢迎回来，请登录您的账号</p>
        </div>

        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          label-width="0"
          size="large"
          @keyup.enter="handleLogin"
        >
          <el-form-item prop="accountName">
            <el-input
              v-model="loginForm.accountName"
              placeholder="请输入账号名称"
              :prefix-icon="User"
              clearable
            />
          </el-form-item>
          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              placeholder="请输入密码"
              :prefix-icon="Lock"
              show-password
              clearable
            />
          </el-form-item>

          <div class="login-options">
            <el-checkbox v-model="rememberMe">记住账号</el-checkbox>
            <el-link type="primary" :underline="false" @click="handleForgotPassword"
              >忘记密码</el-link
            >
          </div>

          <el-form-item>
            <el-button type="primary" :loading="loading" class="login-button" @click="handleLogin">
              {{ loading ? '登录中...' : '登录' }}
            </el-button>
          </el-form-item>
        </el-form>

        <!-- 底部链接 -->
        <div class="login-footer">
          <el-link type="primary" :underline="false" @click="router.push('/declaration')"
            >立即注册</el-link
          >
        </div>
      </div>

      <!-- 装饰元素 -->
      <div class="decoration-circles">
        <div class="circle circle-1"></div>
        <div class="circle circle-2"></div>
        <div class="circle circle-3"></div>
      </div>
    </div>
  </div>

  <!-- 邮箱验证弹窗 -->
  <el-dialog
    v-model="emailVerifyDialogVisible"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    width="500px"
    center
    align-center
    class="email-verify-dialog"
  >
    <template #header>
      <div class="email-dialog-header">
        <div class="email-icon-wrapper">
          <div class="email-icon">
            <el-icon><Message /></el-icon>
          </div>
          <div class="email-icon-glow"></div>
        </div>
        <h2 class="email-title">邮箱验证</h2>
        <p class="email-subtitle">请完成验证以确保账户安全</p>
      </div>
    </template>

    <div class="email-dialog-content">
      <div class="security-tip">
        <el-alert type="info" :closable="false" effect="light">
          <template #title>
            <div class="security-tip-content">
              <el-icon><InfoFilled /></el-icon>
              <span>为了保证您的账户安全，请使用与账号绑定的邮箱进行登录验证</span>
            </div>
          </template>
        </el-alert>
      </div>

      <el-form
        ref="emailFormRef"
        :model="emailForm"
        :rules="emailRules"
        label-position="top"
        size="large"
        class="email-form"
      >
        <el-form-item label="邮箱地址" prop="email">
          <el-input
            v-model="emailForm.email"
            placeholder="请输入与账号绑定的邮箱"
            :prefix-icon="Message"
            clearable
            class="custom-input"
          />
        </el-form-item>
        <el-form-item label="验证码" prop="code">
          <div class="verification-code-input">
            <el-input
              v-model="emailForm.code"
              placeholder="请输入6位验证码"
              :prefix-icon="Key"
              clearable
              maxlength="6"
              class="custom-input code-input"
            />
            <el-button
              type="primary"
              :disabled="countdown > 0"
              @click="sendCode"
              class="code-button"
            >
              <span class="button-text">{{
                countdown > 0 ? `${countdown}秒后重试` : '获取验证码'
              }}</span>
            </el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="email-dialog-footer">
        <el-button @click="cancelEmailVerify" class="cancel-button">取消</el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="confirmEmailVerify"
          class="confirm-button"
        >
          {{ loading ? '验证中...' : '确认并登录' }}
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 登录状态检查弹窗 -->
  <div class="login-status-modal" v-if="checkingStatus">
    <div class="login-status-content">
      <div class="status-icon-container">
        <div class="status-icon">
          <el-icon class="loading-icon"><Loading /></el-icon>
        </div>
        <div class="status-icon-ring"></div>
      </div>
      <h3 class="status-title">正在检查登录状态</h3>
      <p class="status-message">请稍候，正在验证您的身份...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { User, Lock, Message, Key, InfoFilled, Loading } from '@element-plus/icons-vue'
import type { FormInstance } from 'element-plus'
import { login, loginByEmail, emailLogin, type SellerLoginDTO } from '@/api/seller'
import { setToken } from '@/utils/auth'
import { useUserStore } from '@/stores/user'
import { getSellerById } from '@/api/sellerAdmin'

// 实际的类型已在@/api/seller中定义，这里使用它们
interface EmailFormType {
  email: string
  code: string
}

// API响应接口
interface ApiResponse<T> {
  code: number
  msg: string | null
  data: T | null
}

// 登录结果数据接口
interface LoginData {
  id: number
  token?: string
  accountName: string
  expireTime?: number
  userRole?: string
  permission?: string[]
}

const router = useRouter()
const loading = ref(false)
const loginFormRef = ref<FormInstance>()
const emailFormRef = ref<FormInstance>()
const rememberMe = ref(false)
const emailVerifyDialogVisible = ref(false)
const countdown = ref(0)
let countdownTimer: number | null = null
const checkingStatus = ref(false)

// 使用真实的用户存储
const userStore = useUserStore()

// 登录表单，不包含email和code
const loginForm = reactive<Omit<SellerLoginDTO, 'email' | 'code'>>({
  accountName: '',
  password: '',
})

const emailForm = reactive<EmailFormType>({
  email: '',
  code: '',
})

const loginRules = {
  accountName: [
    { required: true, message: '请输入账号', trigger: 'blur' },
    { min: 3, max: 20, message: '账号长度在3-20个字符之间', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在6-20个字符之间', trigger: 'blur' },
  ],
}

const emailRules = {
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' },
  ],
  code: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { min: 6, max: 6, message: '验证码必须为6位', trigger: 'blur' },
    { pattern: /^\d{6}$/, message: '验证码必须为6位数字', trigger: 'blur' },
  ],
}

/**
 * 处理登录逻辑，直接调用后端接口
 */
const handleLogin = async () => {
  if (!loginFormRef.value) return

  await loginFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true

      // 显示登录状态检查弹窗
      checkingStatus.value = true

      try {
        // 添加延时模拟检查登录状态的过程
        await new Promise((resolve) => setTimeout(resolve, 800))

        console.log('开始登录请求...')
        // 首次登录尝试，只发送账号和密码，使用login接口 - /seller/login
        const loginData: SellerLoginDTO = {
          ...loginForm,
          email: '', // 首次登录时不需要email
          code: '', // 首次登录时不需要验证码
        }

        // 调用登录接口
        const response = await login(loginData)

        // 隐藏登录状态检查弹窗
        checkingStatus.value = false

        // 根据后端返回结果处理
        if (response.code === 1 && response.data) {
          // 登录成功，24小时内登录过
          handleLoginSuccess(response)
          console.log('登录信息：',response.data);
          
        } else if (response.msg === 'EMAIL') {
          // 超过24小时，需要邮箱验证
          ElMessage.info('安全验证：请完成邮箱验证后继续登录')
          // 显示邮箱验证弹窗
          emailVerifyDialogVisible.value = true
        } else {
          // 其他登录失败情况
          console.log('登录失败，后端返回:', response)
          ElMessage.error(response.msg || '登录失败，请检查账号密码')
        }
      } catch (error: any) {
        // 隐藏登录状态检查弹窗
        checkingStatus.value = false

        console.error('登录请求出错:', error)
        ElMessage.error(error.response?.data?.msg || '登录失败，请稍后重试')
      } finally {
        loading.value = false
      }
    }
  })
}

/**
 * 发送验证码
 */
const sendCode = async () => {
  if (!emailForm.email) {
    ElMessage.warning('请先输入邮箱地址')
    return
  }

  try {
    // 构建邮箱验证所需参数
    const emailData: SellerLoginDTO = {
      ...loginForm,
      email: emailForm.email,
      code: '', // 发送验证码时不需要code
    }

    // 调用新的发送验证码接口
    const response = await loginByEmail(emailData)

    if (response && response.code === 1) {
      ElMessage.success('验证码已发送，请查收邮件')
      // 开始倒计时
      countdown.value = 60
      countdownTimer = window.setInterval(() => {
        countdown.value--
        if (countdown.value <= 0 && countdownTimer) {
          clearInterval(countdownTimer)
          countdownTimer = null
        }
      }, 1000)
    } else {
      ElMessage.error((response && response.msg) || '验证码发送失败')
    }
  } catch (error: any) {
    console.error('发送验证码出错:', error)
    ElMessage.error('验证码发送失败，请稍后重试')
  }
}

/**
 * 取消邮箱验证
 */
const cancelEmailVerify = () => {
  emailVerifyDialogVisible.value = false
  emailForm.email = ''
  emailForm.code = ''
}

/**
 * 确认邮箱验证并登录
 */
const confirmEmailVerify = async () => {
  if (!emailFormRef.value) return

  await emailFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true

      try {
        // 构建完整的登录参数，包含账号名、密码、邮箱和验证码
        const loginData: SellerLoginDTO = {
          ...loginForm,
          email: emailForm.email,
          code: emailForm.code,
        }

        // 调用邮箱登录接口
        const response = await emailLogin(loginData)

        // 根据后端返回结果处理
        if (response.code === 1 && response.data) {
          // 登录成功
          handleLoginSuccess(response)
        } else {
          // 登录失败
          console.log('邮箱验证登录失败，后端返回:', response)
          ElMessage.error(response.msg || '验证失败，请检查邮箱和验证码')
        }
      } catch (error: any) {
        console.error('邮箱验证登录请求出错:', error)
        ElMessage.error(error.response?.data?.msg || '验证失败，请稍后重试')
      } finally {
        loading.value = false
      }
    }
  })
}

/**
 * 处理登录成功逻辑
 */
const handleLoginSuccess = async(response: ApiResponse<LoginData>) => {
  console.log('登录成功，处理token...')
  if (response.data && response.data.token) {
    // 使用后端返回的过期时间，如果没有则默认24小时
    const expiresIn = response.data.expireTime || 24 * 60 * 60 * 1000
    setToken(response.data.token, expiresIn)
  } else if (response.data && response.data.id) {
    // 即使没有token，也使用用户ID创建一个临时token，默认24小时
    setToken(`user_${response.data.id}`, 24 * 60 * 60 * 1000)
  }

  console.log('response:',response);
  const userInfo=await getSellerById(response.data.id)
  console.log("当前登录的用户身份：",userInfo.data.seller.userRole);
  
  // 设置用户信息
  if (response.data) {
    userStore.setUserInfo({
      id: response.data.id,
      accountName: response.data.accountName,
      role: userInfo.data.seller.userRole || '普通用户',
      permissions: response.data.permission || [],
    })

    // 如果有权限数据，单独设置到store中
    if (response.data.permission) {
      userStore.setPermissions(response.data.permission)
    }
  }

  // 记住用户名
  if (rememberMe.value) {
    localStorage.setItem('lastAccountName', loginForm.accountName)
    localStorage.setItem('rememberMe', 'true')
  } else {
    localStorage.removeItem('lastAccountName')
    localStorage.removeItem('rememberMe')
  }

  // 关闭验证窗口
  emailVerifyDialogVisible.value = false

  ElMessage.success('登录成功')

  // 打印调试信息
  console.log('登录成功，用户信息设置完毕:', userStore.userInfo)

  // 确保路由跳转执行
  console.log('准备跳转到欢迎页面...')

  // 添加延迟确保状态更新完成
  setTimeout(() => {
    router.push('/main/welcome')
  }, 100)
}

// 在组件加载时检查是否已登录
onMounted(() => {
  // 检查登录状态
  if (userStore.checkLoginStatus()) {
    router.push('/main/welcome')
    return
  }

  // 检查是否记住了账号
  const savedAccountName = localStorage.getItem('lastAccountName')
  const rememberSetting = localStorage.getItem('rememberMe')
  if (savedAccountName && rememberSetting === 'true') {
    loginForm.accountName = savedAccountName
    rememberMe.value = true
  }
})

// 组件卸载前清除定时器
onBeforeUnmount(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }
})

// 处理忘记密码
const handleForgotPassword = () => {
  ElMessageBox.alert(
    `<div class="forgot-password-dialog">
      <div class="dialog-icon">
        <i class="el-icon-lock"></i>
      </div>
      <div class="dialog-title">密码找回提示</div>
      <div class="dialog-content">
        <p>尊敬的用户：</p>
        <p>为了保障您的账户安全，目前密码找回功能需要人工处理。</p>
        <p>请通过以下方式联系我们：</p>
        <div class="contact-info">
          <p><i class="el-icon-message"></i> 客服邮箱：<EMAIL></p>
          <p><i class="el-icon-phone"></i> 服务热线：400-xxx-xxxx</p>
          <p><i class="el-icon-time"></i> 服务时间：周一至周五 9:00-18:00</p>
        </div>
        <div class="dialog-tip">我们将会在1-2个工作日内处理您的请求</div>
      </div>
    </div>`,
    {
      dangerouslyUseHTMLString: true,
      showClose: true,
      showConfirmButton: true,
      confirmButtonText: '我知道了',
      customClass: 'custom-message-box',
      callback: (action: string) => {
        if (action === 'confirm') {
          ElMessage({
            message: '如需帮助，请联系客服',
            type: 'info',
          })
        }
      },
    },
  )
}
</script>

<style scoped lang="scss">
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #eef2f7 100%);
  padding: 0;
  margin: 0;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  overflow: hidden;

  .login-content {
    position: relative;
    width: 100%;
    max-width: 1200px;
    display: flex;
    justify-content: center;
  }

  .login-box {
    width: 420px;
    padding: 40px 50px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    position: relative;
    z-index: 10;
    animation: fadeIn 0.8s ease-out;

    .login-header {
      margin-bottom: 36px;
      text-align: center;
    }

    .login-title {
      font-size: 32px;
      font-weight: 600;
      color: #333;
      margin-bottom: 8px;
      background: linear-gradient(90deg, var(--el-color-primary), #409eff);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;

      span {
        font-weight: 400;
        font-size: 28px;
      }
    }

    .login-subtitle {
      font-size: 14px;
      color: #909399;
      margin-top: 8px;
    }

    .login-button {
      width: 100%;
      height: 46px;
      font-size: 16px;
      border-radius: 8px;
      margin-top: 10px;
      font-weight: 500;
      letter-spacing: 2px;
      transition: all 0.3s;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(var(--el-color-primary-rgb), 0.3);
      }
    }

    .login-footer {
      margin-top: 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .el-link {
        font-size: 14px;
        transition: all 0.3s;

        &:hover {
          transform: translateY(-2px);
        }
      }
    }
  }

  // 装饰元素
  .decoration-circles {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;

    .circle {
      position: absolute;
      border-radius: 50%;
      opacity: 0.6;
    }

    .circle-1 {
      width: 300px;
      height: 300px;
      background: linear-gradient(
        135deg,
        rgba(var(--el-color-primary-rgb), 0.2),
        rgba(var(--el-color-primary-rgb), 0.05)
      );
      top: -100px;
      right: 10%;
      animation: float 8s ease-in-out infinite;
    }

    .circle-2 {
      width: 200px;
      height: 200px;
      background: linear-gradient(
        135deg,
        rgba(var(--el-color-primary-rgb), 0.15),
        rgba(var(--el-color-primary-rgb), 0.05)
      );
      bottom: -50px;
      left: 10%;
      animation: float 6s ease-in-out infinite 1s;
    }

    .circle-3 {
      width: 150px;
      height: 150px;
      background: linear-gradient(
        135deg,
        rgba(var(--el-color-primary-rgb), 0.1),
        rgba(var(--el-color-primary-rgb), 0.03)
      );
      top: 40%;
      left: 20%;
      animation: float 7s ease-in-out infinite 0.5s;
    }
  }
}

// 邮箱验证弹窗样式优化
:deep(.email-verify-dialog) {
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.18);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);

  .el-dialog__header {
    margin: 0;
    padding: 0;
  }

  .el-dialog__body {
    padding: 0;
  }

  .el-dialog__footer {
    padding: 25px;
    background: #f8fafc;
    border-top: 1px solid #ebeef5;
  }

  .el-dialog__wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

// 确保弹窗全局居中
:deep(.el-overlay) {
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

:deep(.el-overlay-dialog) {
  display: flex;
  align-items: center;
  justify-content: center;
}

.email-dialog-header {
  background: linear-gradient(135deg, var(--el-color-primary), #4a7bff);
  color: white;
  padding: 35px 30px 30px;
  text-align: center;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: 0;
    background: radial-gradient(circle at top right, rgba(255, 255, 255, 0.25), transparent 70%);
    z-index: 1;
  }

  &::after {
    content: '';
    position: absolute;
    width: 200px;
    height: 200px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
    border-radius: 50%;
    bottom: -100px;
    right: -50px;
    z-index: 1;
    opacity: 0.6;
  }

  .email-icon-wrapper {
    position: relative;
    width: 90px;
    height: 90px;
    margin: 0 auto 20px;
  }

  .email-icon {
    margin: 0 auto;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 2;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    transform: translateY(0);
    transition: transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
    animation: iconPulse 2s infinite alternate;

    &:hover {
      transform: translateY(-5px) scale(1.05);
    }

    .el-icon {
      font-size: 42px;
    }
  }

  .email-icon-glow {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0) 70%);
    z-index: 1;
    top: 0;
    left: 0;
    animation: glowPulse 2.5s infinite alternate;
    filter: blur(10px);
  }

  .email-title {
    font-size: 28px;
    font-weight: 600;
    margin: 0 0 10px;
    position: relative;
    z-index: 2;
    letter-spacing: 0.5px;
  }

  .email-subtitle {
    font-size: 15px;
    font-weight: 400;
    margin: 0;
    opacity: 0.85;
    position: relative;
    z-index: 2;
  }
}

.email-dialog-content {
  padding: 40px;
  background: #fff;

  .security-tip {
    margin-bottom: 30px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
    }

    :deep(.el-alert) {
      border-radius: 12px;
      border-left: 4px solid var(--el-color-primary);
      padding: 16px;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.03);
    }

    .security-tip-content {
      display: flex;
      align-items: center;
      gap: 12px;
      font-size: 15px;

      .el-icon {
        color: var(--el-color-primary);
        font-size: 18px;
      }
    }
  }

  .email-form {
    margin-top: 25px;

    :deep(.el-form-item__label) {
      font-weight: 500;
      padding-bottom: 8px;
      font-size: 15px;
      color: #333;
    }
  }
}

:deep(.custom-input) {
  &.el-input {
    --el-input-border-radius: 12px;
    --el-input-hover-border-color: var(--el-color-primary-light-5);

    .el-input__wrapper {
      box-shadow: 0 3px 10px rgba(0, 0, 0, 0.03) !important;
      transition: all 0.3s;
      border: 1px solid #e0e7ff;
      background-color: #f8faff;
      padding: 2px 15px;
      height: 50px;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05) !important;
        border-color: #c0d0ff;
      }

      &.is-focus {
        background-color: #fff;
        transform: translateY(-2px);
        box-shadow: 0 5px 20px rgba(var(--el-color-primary-rgb), 0.1) !important;
        border-color: var(--el-color-primary-light-3);
      }
    }

    .el-input__inner {
      font-size: 16px;
      color: #333;
    }

    .el-input__prefix {
      color: #a0a8c0;
    }
  }

  &.code-input {
    .el-input__wrapper {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
      border-right: none;
    }
  }
}

.email-dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
}

// 验证码输入框样式
.verification-code-input {
  display: flex;
  gap: 0;

  .el-input {
    flex: 1;
  }

  .code-button {
    width: 140px;
    font-size: 14px;
    border-radius: 0 12px 12px 0;
    height: 50px;
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    background: linear-gradient(135deg, var(--el-color-primary), #4a7bff);
    border: none;
    box-shadow: 0 5px 15px rgba(var(--el-color-primary-rgb), 0.2);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
      z-index: 1;
    }

    .button-text {
      position: relative;
      z-index: 2;
    }

    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(var(--el-color-primary-rgb), 0.3);
      background: linear-gradient(135deg, var(--el-color-primary), #5d89ff);
    }

    &:active:not(:disabled) {
      transform: translateY(1px);
      box-shadow: 0 3px 10px rgba(var(--el-color-primary-rgb), 0.25);
    }

    &:disabled {
      opacity: 0.8;
      background: linear-gradient(135deg, #b0c4ff, #8eabff);
    }
  }
}

.cancel-button {
  border-radius: 12px;
  height: 46px;
  font-size: 15px;
  padding: 0 25px;
  font-weight: 500;
  border-color: #e1e6ef;
  color: #606880;
  transition: all 0.3s;

  &:hover {
    color: #333;
    border-color: #c0c9e0;
    background-color: #f5f7fa;
    transform: translateY(-2px);
  }
}

.confirm-button {
  border-radius: 12px;
  height: 46px;
  font-size: 15px;
  padding: 0 25px;
  font-weight: 500;
  background: linear-gradient(135deg, var(--el-color-primary), #4a7bff);
  box-shadow: 0 5px 15px rgba(var(--el-color-primary-rgb), 0.2);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  border: none;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(var(--el-color-primary-rgb), 0.3);
    background: linear-gradient(135deg, var(--el-color-primary), #5d89ff);
  }

  &:active {
    transform: translateY(1px);
    box-shadow: 0 3px 10px rgba(var(--el-color-primary-rgb), 0.25);
  }
}

@keyframes iconPulse {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.08);
  }
}

@keyframes glowPulse {
  0% {
    opacity: 0.5;
  }
  100% {
    opacity: 0.2;
  }
}

// 动画效果
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(5deg);
  }
  100% {
    transform: translateY(0) rotate(0deg);
  }
}

// 覆盖Element Plus样式
:deep(.el-input__wrapper) {
  background-color: #f9fafc;
  border: 1px solid #ebeef5;
  box-shadow: none;
  border-radius: 8px;
  padding: 0 15px;
  height: 46px;
  transition: all 0.3s;

  &:hover {
    border-color: #c0c4cc;
  }

  &.is-focus {
    border-color: var(--el-color-primary);
    background-color: #fff;
    box-shadow: 0 0 0 1px var(--el-color-primary-light-8);
  }

  .el-input__inner {
    height: 46px;
    color: #333;
    font-size: 15px;
  }

  .el-input__prefix {
    color: #909399;
  }
}

:deep(.el-form-item__error) {
  padding-top: 4px;
  font-size: 13px;
}

:deep(.el-button--primary) {
  background: linear-gradient(90deg, var(--el-color-primary), #409eff);
  border: none;

  &:hover,
  &:focus {
    background: linear-gradient(90deg, var(--el-color-primary-light-3), #66b1ff);
  }

  &:active {
    background: linear-gradient(90deg, var(--el-color-primary-dark-2), #337ecc);
  }
}

// 响应式调整
@media (max-width: 768px) {
  .login-container .login-box {
    width: 90%;
    max-width: 420px;
    padding: 30px;
  }
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

// 添加忘记密码弹窗样式
:deep(.custom-message-box) {
  padding: 0;
  border-radius: 16px;
  overflow: hidden;
  max-width: 460px;

  .el-message-box__header {
    display: none;
  }

  .el-message-box__content {
    padding: 0;
    margin: 0;

    .el-message-box__status {
      display: none;
    }

    .el-message-box__message {
      padding: 0;
      margin: 0;
    }
  }

  .el-message-box__btns {
    padding: 15px 20px;
    background: #f8fafc;
    border-top: 1px solid #ebeef5;

    .el-button {
      padding: 10px 24px;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 500;

      &--primary {
        background: linear-gradient(90deg, var(--el-color-primary), #409eff);
        border: none;

        &:hover {
          background: linear-gradient(90deg, var(--el-color-primary-light-3), #66b1ff);
          transform: translateY(-1px);
        }
      }
    }
  }
}

.forgot-password-dialog {
  padding: 30px 30px 20px;

  .dialog-icon {
    text-align: center;
    margin-bottom: 20px;

    i {
      font-size: 48px;
      color: var(--el-color-primary);
      background: linear-gradient(135deg, var(--el-color-primary), #409eff);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  .dialog-title {
    font-size: 22px;
    font-weight: 600;
    color: #303133;
    text-align: center;
    margin-bottom: 20px;
  }

  .dialog-content {
    color: #606266;
    font-size: 14px;
    line-height: 1.8;

    p {
      margin: 0 0 8px;
    }
  }

  .contact-info {
    background: #f8fafc;
    border-radius: 8px;
    padding: 15px 20px;
    margin: 15px 0;

    p {
      margin: 8px 0;
      display: flex;
      align-items: center;
      gap: 8px;

      i {
        color: var(--el-color-primary);
        font-size: 16px;
      }
    }
  }

  .dialog-tip {
    font-size: 13px;
    color: #909399;
    text-align: center;
    margin-top: 15px;
    font-style: italic;
  }
}

// 登录状态检查弹窗
.login-status-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(6px);
  z-index: 3000;
  animation: fadeIn 0.3s ease;
}

.login-status-content {
  width: 340px;
  padding: 40px 30px;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
  text-align: center;
  animation: scaleIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.status-icon-container {
  position: relative;
  width: 100px;
  height: 100px;
  margin: 0 auto 25px;
}

.status-icon {
  position: relative;
  width: 80px;
  height: 80px;
  margin: 10px auto;
  background: linear-gradient(135deg, var(--el-color-primary), #4a7bff);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
  box-shadow: 0 8px 25px rgba(var(--el-color-primary-rgb), 0.25);

  .loading-icon {
    font-size: 42px;
    color: white;
    animation: spinAround 1.2s linear infinite;
  }
}

.status-icon-ring {
  position: absolute;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  border: 3px solid rgba(var(--el-color-primary-rgb), 0.2);
  border-top: 3px solid var(--el-color-primary);
  animation: spinAround 1.5s linear infinite;
}

.status-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 15px;
}

.status-message {
  font-size: 16px;
  color: #606266;
  margin: 0;
  line-height: 1.6;
}

@keyframes spinAround {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
</style>
