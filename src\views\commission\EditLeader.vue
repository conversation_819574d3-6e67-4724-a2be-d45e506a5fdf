<template>
  <div class="edit-leader-container">
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">编辑团长</h1>
        <div class="page-subtitle">编辑团长信息、佣金比例和邀请码设置</div>
      </div>
      <div class="header-right">
        <el-button @click="goBack">
          <el-icon><Back /></el-icon>返回列表
        </el-button>
      </div>
    </div>

    <el-card shadow="hover" class="form-card" v-loading="loading">
      <el-form
        v-if="!loading"
        ref="formRef"
        :model="form"
        :rules="rules"
        label-position="top"
        status-icon
        require-asterisk-position="end"
      >
        <el-row :gutter="20">
          <el-col :xs="24" :sm="12">
            <el-form-item label="团长类型" prop="type">
              <el-radio-group v-model="form.type">
                <el-radio-button label="merchant">商家招募团长</el-radio-button>
                <el-radio-button label="user">用户招募团长</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :xs="24" :sm="12">
            <el-form-item label="团长姓名" prop="name">
              <el-input v-model="form.name" placeholder="请输入团长姓名" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="form.phone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :xs="24" :sm="12">
            <el-form-item label="性别" prop="gender">
              <el-select v-model="form.gender" placeholder="请选择性别" style="width: 100%">
                <el-option label="男" value="male" />
                <el-option label="女" value="female" />
                <el-option label="保密" value="secret" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12">
            <el-form-item label="身份证号" prop="idCard">
              <el-input v-model="form.idCard" placeholder="请输入身份证号" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :xs="24" :sm="12">
            <el-form-item label="电子邮箱" prop="email">
              <el-input v-model="form.email" placeholder="请输入电子邮箱" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12">
            <el-form-item label="银行卡号" prop="bankAccount">
              <el-input v-model="form.bankAccount" placeholder="请输入佣金结算银行卡号" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider>佣金设置</el-divider>

        <el-row :gutter="20">
          <el-col :xs="24" :sm="12">
            <el-form-item :label="`${typeLabel}佣金比例 (%)`" prop="commissionRate">
              <el-input-number
                v-model="form.commissionRate"
                :min="0"
                :max="20"
                :precision="1"
                :step="0.5"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12">
            <el-form-item label="佣金结算周期" prop="settlementCycle">
              <el-select
                v-model="form.settlementCycle"
                placeholder="请选择结算周期"
                style="width: 100%"
              >
                <el-option label="每周结算" value="weekly" />
                <el-option label="每月结算" value="monthly" />
                <el-option label="每季度结算" value="quarterly" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="佣金说明" prop="commissionDesc">
              <el-input
                v-model="form.commissionDesc"
                type="textarea"
                :rows="3"
                placeholder="请输入佣金说明，将展示给团长"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider>邀请码设置</el-divider>

        <el-row :gutter="20">
          <el-col :xs="24" :sm="8">
            <el-form-item label="邀请码前缀" prop="codePrefix">
              <el-input v-model="form.codePrefix" placeholder="邀请码前缀" disabled>
                <template #append>
                  <el-tooltip content="编辑模式下不可修改邀请码前缀">
                    <el-icon><QuestionFilled /></el-icon>
                  </el-tooltip>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8">
            <el-form-item label="邀请码" prop="invitationCode">
              <el-input v-model="form.invitationCode" placeholder="团长邀请码" disabled>
                <template #append>
                  <el-tooltip content="编辑模式下不可修改邀请码">
                    <el-icon><InfoFilled /></el-icon>
                  </el-tooltip>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8">
            <el-form-item label="状态" prop="status">
              <el-switch
                v-model="form.status"
                :active-value="1"
                :inactive-value="0"
                active-text="正常"
                inactive-text="已禁用"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="form.remark"
                type="textarea"
                :rows="3"
                placeholder="请输入备注信息（选填）"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="团长数据统计" class="stats-item">
              <el-descriptions :column="4" border>
                <el-descriptions-item label="已邀请商家">
                  <span class="stats-value">{{ leaderStats.invitedMerchants }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="已邀请用户">
                  <span class="stats-value">{{ leaderStats.invitedUsers }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="累计佣金">
                  <span class="stats-value highlight">¥{{ leaderStats.totalCommission }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="创建时间">
                  <span>{{ leaderStats.createTime }}</span>
                </el-descriptions-item>
              </el-descriptions>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item>
          <el-button type="primary" @click="submitForm" :loading="submitting">保存</el-button>
          <el-button @click="resetForm">重置</el-button>
          <el-button
            type="danger"
            @click="confirmDelete"
            :disabled="submitting"
            style="float: right"
          >
            删除团长
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 确认删除对话框 -->
    <el-dialog v-model="deleteDialogVisible" title="确认删除" width="400px" center>
      <div class="delete-confirm">
        <el-icon class="warning-icon"><Warning /></el-icon>
        <p>确定要删除该团长吗？删除后将无法恢复，该团长的邀请码也将失效。</p>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deleteDialogVisible = false">取消</el-button>
          <el-button type="danger" @click="handleDelete" :loading="deleting">确定删除</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Back, QuestionFilled, InfoFilled, Warning } from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()
const formRef = ref<FormInstance>()
const loading = ref(true)
const submitting = ref(false)
const deleteDialogVisible = ref(false)
const deleting = ref(false)

// 获取路由参数中的id
const leaderId = computed(() => route.params.id as string)

// 团长数据统计
const leaderStats = reactive({
  invitedMerchants: 0,
  invitedUsers: 0,
  totalCommission: '0.00',
  createTime: '',
})

// 表单数据
const form = reactive({
  id: 0,
  type: 'merchant',
  name: '',
  phone: '',
  gender: 'male',
  idCard: '',
  email: '',
  bankAccount: '',
  commissionRate: 5.0,
  settlementCycle: 'monthly',
  commissionDesc: '',
  codePrefix: '',
  invitationCode: '',
  status: 1,
  remark: '',
})

// 表单验证规则
const rules = reactive<FormRules>({
  type: [{ required: true, message: '请选择团长类型', trigger: 'change' }],
  name: [{ required: true, message: '请输入团长姓名', trigger: 'blur' }],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' },
  ],
  idCard: [
    {
      pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
      message: '请输入正确的身份证号',
      trigger: 'blur',
    },
  ],
  email: [
    {
      pattern: /^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/,
      message: '请输入正确的邮箱地址',
      trigger: 'blur',
    },
  ],
  commissionRate: [{ required: true, message: '请设置佣金比例', trigger: 'blur' }],
  settlementCycle: [{ required: true, message: '请选择结算周期', trigger: 'change' }],
})

// 计算属性：团长类型标签
const typeLabel = computed(() => {
  return form.type === 'merchant' ? '商家招募' : '用户招募'
})

// 获取团长详情
const fetchLeaderDetail = async () => {
  try {
    loading.value = true

    // 模拟API请求获取团长详情
    setTimeout(() => {
      // 这里应该是实际的API调用，使用leaderId.value获取数据
      // 模拟数据，实际项目中应该从API响应获取
      const leaderData = {
        id: Number(leaderId.value),
        type: 'merchant',
        name: '张三',
        phone: '***********',
        gender: 'male',
        idCard: '330122198901011234',
        email: '<EMAIL>',
        bankAccount: '6222021234567890123',
        commissionRate: 5.0,
        settlementCycle: 'monthly',
        commissionDesc:
          '佣金将按照实际成交订单金额计算，每月结算一次，系统自动结算至您的银行账户。',
        codePrefix: 'MRCNT',
        invitationCode: 'MRCNT0001',
        status: 1,
        remark: '优质团长，有丰富的资源',
        invitedMerchants: 12,
        invitedUsers: 0,
        totalCommission: '6,580.00',
        createTime: '2023-06-15 10:30:22',
      }

      // 更新表单数据
      Object.keys(form).forEach((key) => {
        if (key in leaderData) {
          form[key] = leaderData[key]
        }
      })

      // 更新统计数据
      leaderStats.invitedMerchants = leaderData.invitedMerchants
      leaderStats.invitedUsers = leaderData.invitedUsers
      leaderStats.totalCommission = leaderData.totalCommission
      leaderStats.createTime = leaderData.createTime

      loading.value = false
    }, 800)
  } catch (error) {
    console.error('获取团长详情失败', error)
    ElMessage.error('获取团长详情失败，请重试')
    loading.value = false
  }
}

// 返回列表页
const goBack = () => {
  router.push('/main/commission/leader-list')
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return

  await formRef.value.validate((valid, fields) => {
    if (valid) {
      submitting.value = true

      // 格式化提交数据
      const submitData = {
        ...form,
        type: form.type === 'merchant' ? '商家招募' : '用户招募',
        gender: form.gender === 'male' ? '男' : form.gender === 'female' ? '女' : '保密',
      }

      // 这里应该调用API保存数据
      console.log('提交编辑表单数据', submitData)

      // 模拟API请求
      setTimeout(() => {
        submitting.value = false
        ElMessage.success('团长信息已更新！')
        router.push('/main/commission/leader-list')
      }, 1000)
    } else {
      console.error('表单验证失败', fields)
    }
  })
}

// 重置表单
const resetForm = () => {
  ElMessageBox.confirm('确定要重置表单？这将恢复到上次保存的状态。', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      fetchLeaderDetail()
      ElMessage.info('表单已重置')
    })
    .catch(() => {})
}

// 确认删除团长
const confirmDelete = () => {
  deleteDialogVisible.value = true
}

// 执行删除操作
const handleDelete = () => {
  deleting.value = true

  // 这里应该调用API删除团长
  setTimeout(() => {
    deleting.value = false
    deleteDialogVisible.value = false
    ElMessage.success('团长已成功删除')
    router.push('/main/commission/leader-list')
  }, 1000)
}

// 初始化数据
onMounted(() => {
  fetchLeaderDetail()
})
</script>

<style scoped lang="scss">
.edit-leader-container {
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #eef2f5 100%);
  border-radius: 12px;
  min-height: calc(100vh - 40px);

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    .header-left {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        margin: 0 0 8px 0;
        background: linear-gradient(120deg, #3a7bd5, #2c5499);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .page-subtitle {
        font-size: 14px;
        color: #606266;
      }
    }

    .header-right {
      display: flex;
      gap: 16px;
    }
  }

  .form-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    padding: 24px;

    &:hover {
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }
  }

  :deep(.el-form-item__label) {
    font-weight: 600;
  }

  :deep(.el-divider__text) {
    font-weight: 600;
    color: #409eff;
    font-size: 16px;
  }

  .stats-item {
    margin-top: 16px;

    .stats-value {
      font-weight: 600;

      &.highlight {
        color: #ff6b00;
      }
    }
  }

  .delete-confirm {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px 0;

    .warning-icon {
      font-size: 24px;
      color: #f56c6c;
    }

    p {
      margin: 0;
      font-size: 14px;
      color: #606266;
    }
  }
}

@media screen and (max-width: 768px) {
  .edit-leader-container {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-right {
        width: 100%;
      }
    }
  }
}
</style>
