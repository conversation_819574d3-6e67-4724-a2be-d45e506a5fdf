# 回款功能开发文档

## 📋 目录
1. [功能概述](#功能概述)
2. [系统架构](#系统架构)
3. [数据库设计](#数据库设计)
4. [实体类定义](#实体类定义)
5. [接口定义](#接口定义)
6. [业务规则](#业务规则)
7. [API文档](#api文档)
8. [部署说明](#部署说明)

## 🎯 功能概述

回款功能是电商平台的核心财务模块，用于管理商家订单的回款流程。系统按照T+14的业务规则，自动计算回款时间并管理回款状态。

### 核心功能
- **自动回款计算**：支付时间+14天为账单日期
- **回款日期计算**：账单周期下个月10号后第一个工作日
- **状态管理**：未到期、待回款、已回款三种状态
- **商家端查询**：商家查看自己的回款情况
- **平台端管理**：管理员管理所有商家回款

## 🏗️ 系统架构

```
回款功能架构
├── 实体层 (Entity)
│   ├── OrderSettlementInfo - 订单回款信息
│   └── SettlementConfig - 回款配置
├── 数据传输层 (DTO/VO)
│   ├── SettlementQueryDTO - 查询条件
│   ├── SettlementCompleteDTO - 回款完成
│   ├── SettlementInfoVO - 回款信息视图
│   └── SettlementSummaryVO - 回款汇总视图
├── 数据访问层 (Mapper)
│   ├── OrderSettlementInfoMapper - 回款信息数据访问
│   └── SettlementConfigMapper - 配置数据访问
├── 业务逻辑层 (Service)
│   └── SettlementService - 回款业务逻辑
└── 控制器层 (Controller)
    ├── SettlementController - 商家端接口
    └── AdminSettlementController - 平台端接口
```

## 📊 数据库设计

### 1. 订单回款信息表 (order_settlement_info)

| 字段名 | 类型 | 说明 | 备注 |
|--------|------|------|------|
| id | BIGINT | 主键ID | 自增 |
| order_id | BIGINT | 订单ID | 外键，唯一 |
| order_number | VARCHAR(50) | 订单号 | 便于查询 |
| seller_id | BIGINT | 商家ID | 外键 |
| seller_name | VARCHAR(100) | 商家名称 | 冗余字段 |
| pay_time | DATETIME | 支付时间 | 来源订单表 |
| billing_date | DATE | 账单日期 | 支付时间+14天 |
| billing_cycle | VARCHAR(7) | 账单周期 | 格式：2025-01 |
| settlement_date | DATE | 回款日期 | 计算得出 |
| order_amount | DECIMAL(12,2) | 订单金额 | 原始金额 |
| settlement_amount | DECIMAL(12,2) | 回款金额 | 扣除佣金后 |
| settlement_status | TINYINT | 回款状态 | 0-未到期,1-待回款,2-已回款 |
| settlement_time | DATETIME | 实际回款时间 | 完成时填写 |
| remark | VARCHAR(500) | 备注信息 | 可选 |
| create_time | DATETIME | 创建时间 | 系统字段 |
| update_time | DATETIME | 更新时间 | 系统字段 |

### 2. 回款配置表 (settlement_config)

| 字段名 | 类型 | 说明 | 备注 |
|--------|------|------|------|
| id | BIGINT | 主键ID | 自增 |
| config_key | VARCHAR(50) | 配置键 | 唯一 |
| config_value | VARCHAR(200) | 配置值 | 字符串格式 |
| config_desc | VARCHAR(200) | 配置描述 | 说明用途 |
| create_time | DATETIME | 创建时间 | 系统字段 |
| update_time | DATETIME | 更新时间 | 系统字段 |

## 📝 实体类定义

### 1. OrderSettlementInfo (订单回款信息)

```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderSettlementInfo implements Serializable {
    
    // 回款状态常量
    public static final Integer STATUS_NOT_DUE = 0;      // 未到期
    public static final Integer STATUS_PENDING = 1;      // 待回款
    public static final Integer STATUS_COMPLETED = 2;    // 已回款
    
    private Long id;                    // 主键ID
    private Long orderId;               // 订单ID
    private String orderNumber;         // 订单号
    private Long sellerId;              // 商家ID
    private String sellerName;          // 商家名称
    private LocalDateTime payTime;      // 支付时间
    private LocalDate billingDate;      // 账单日期
    private String billingCycle;        // 账单周期
    private LocalDate settlementDate;   // 回款日期
    private BigDecimal orderAmount;     // 订单金额
    private BigDecimal settlementAmount; // 回款金额
    private Integer settlementStatus;   // 回款状态
    private LocalDateTime settlementTime; // 实际回款时间
    private String remark;              // 备注信息
    private LocalDateTime createTime;   // 创建时间
    private LocalDateTime updateTime;   // 更新时间
}
```

### 2. SettlementConfig (回款配置)

```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SettlementConfig implements Serializable {
    private Long id;                    // 主键ID
    private String configKey;           // 配置键
    private String configValue;         // 配置值
    private String configDesc;          // 配置描述
    private LocalDateTime createTime;   // 创建时间
    private LocalDateTime updateTime;   // 更新时间
}
```

### 3. SettlementQueryDTO (查询条件)

```java
@Data
public class SettlementQueryDTO implements Serializable {
    private Integer page = 1;           // 页码
    private Integer pageSize = 10;      // 每页记录数
    private Long sellerId;              // 商家ID
    private String orderNumber;         // 订单号
    private Integer settlementStatus;   // 回款状态
    private String billingCycle;        // 账单周期
    private LocalDate settlementStartDate; // 回款开始日期
    private LocalDate settlementEndDate;   // 回款结束日期
    private String sellerName;          // 商家名称
}
```

### 4. SettlementInfoVO (回款信息视图)

```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SettlementInfoVO implements Serializable {
    private Long id;                    // 主键ID
    private Long orderId;               // 订单ID
    private String orderNumber;         // 订单号
    private Long sellerId;              // 商家ID
    private String sellerName;          // 商家名称
    private LocalDateTime payTime;      // 支付时间
    private LocalDate billingDate;      // 账单日期
    private String billingCycle;        // 账单周期
    private LocalDate settlementDate;   // 回款日期
    private BigDecimal orderAmount;     // 订单金额
    private BigDecimal settlementAmount; // 回款金额
    private Integer settlementStatus;   // 回款状态
    private String settlementStatusDesc; // 回款状态描述
    private LocalDateTime settlementTime; // 实际回款时间
    private String remark;              // 备注信息
    private Integer isDue;              // 是否到期(0-否,1-是)
    private Integer daysToSettlement;   // 距离回款天数
}
```

### 5. SettlementSummaryVO (回款汇总视图)

```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SettlementSummaryVO implements Serializable {
    private Long sellerId;              // 商家ID
    private String sellerName;          // 商家名称
    private Integer totalOrders;        // 总订单数
    private BigDecimal totalOrderAmount; // 总订单金额
    private BigDecimal totalSettlementAmount; // 总回款金额
    private Integer notDueOrders;       // 未到期订单数
    private BigDecimal notDueAmount;    // 未到期金额
    private Integer pendingOrders;      // 待回款订单数
    private BigDecimal pendingAmount;   // 待回款金额
    private Integer completedOrders;    // 已回款订单数
    private BigDecimal completedAmount; // 已回款金额
}
```

### 6. SettlementCompleteDTO (回款完成)

```java
@Data
public class SettlementCompleteDTO implements Serializable {
    @NotNull(message = "回款记录ID不能为空")
    private Long id;                    // 回款记录ID
    private String remark;              // 备注
}
```

## 🔧 业务规则

### 1. 回款计算规则
- **账单日期**：订单支付时间 + 14天回款
- **账单周期**：账单日期所在的自然月（格式：YYYY-MM）
- **回款日期**：账单周期下个月10号后的第一个工作日
- **回款金额**：订单金额 × (1 - 平台佣金比例)，默认扣除5%

### 2. 状态流转规则
```
订单支付 → 创建回款记录(未到期) → 到达回款日期(待回款) → 平台确认(已回款)
```

### 3. 工作日计算
- 周一到周五为工作日
- 周六、周日为非工作日
- 可通过配置表扩展节假日规则

## 📡 API文档

### 商家端接口 (/merchant/settlement)

#### 1. 查询回款汇总信息
```http
GET /merchant/settlement/summary
```
**响应示例：**
```json
{
  "code": 1,
  "msg": "success",
  "data": {
    "sellerId": 16,
    "sellerName": "测试商家",
    "totalOrders": 10,
    "totalOrderAmount": 1000.00,
    "totalSettlementAmount": 950.00,
    "notDueOrders": 3,
    "notDueAmount": 285.00,
    "pendingOrders": 2,
    "pendingAmount": 190.00,
    "completedOrders": 5,
    "completedAmount": 475.00
  }
}
```

#### 2. 分页查询回款订单
```http
GET /merchant/settlement/orders?page=1&pageSize=10
```

#### 3. 按状态查询订单
```http
GET /merchant/settlement/orders/status/{status}?page=1&pageSize=10
```

#### 4. 按账单周期查询订单
```http
GET /merchant/settlement/orders/cycle/{billingCycle}?page=1&pageSize=10
```

### 平台端接口 (/admin/settlement)

#### 1. 查询所有商家汇总
```http
GET /admin/settlement/summary
```

#### 2. 分页查询所有回款订单
```http
GET /admin/settlement/orders?page=1&pageSize=10
```

#### 3. 查询待回款订单
```http
GET /admin/settlement/pending
```

#### 4. 按商家查询回款信息
```http
GET /admin/settlement/seller/{sellerId}?page=1&pageSize=10
```

#### 5. 标记回款完成
```http
PUT /admin/settlement/complete
Content-Type: application/json

{
  "id": 1,
  "remark": "回款已完成"
}
```

#### 6. 手动更新回款状态
```http
POST /admin/settlement/update-status
```

#### 7. 为订单创建回款信息
```http
POST /admin/settlement/create/{orderId}
```

## 🚀 部署说明

### 1. 数据库初始化
```sql
-- 执行建表脚本
source /path/to/settlement_tables.sql;
```

### 2. 配置检查
确保以下配置正确：
- 数据库连接配置
- MyBatis映射文件路径
- 分页插件配置

### 3. 依赖检查
确保项目包含以下依赖：
- Spring Boot Starter Web
- MyBatis Plus
- PageHelper
- Lombok

### 4. 测试验证
- 运行单元测试
- 执行API测试
- 验证业务逻辑

## 📞 技术支持

如有问题，请联系开发团队或查看相关技术文档。
