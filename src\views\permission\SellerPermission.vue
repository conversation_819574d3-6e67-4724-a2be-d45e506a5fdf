<template>
  <div class="seller-permission-container">
    <div class="page-header">
      <h2>商家权限管理</h2>
      <p>查看并管理所有商家的权限</p>
    </div>

    <el-card class="search-card" shadow="hover">
      <div class="search-container">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索账号名/邮箱/联系电话"
          clearable
          @keyup.enter="searchSellers"
          class="search-input"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <el-button type="primary" @click="searchSellers">搜索</el-button>
        <el-button @click="resetSearch">重置</el-button>
      </div>
    </el-card>

    <el-card class="action-card" shadow="hover">
      <div class="action-container">
        <div class="left">
          <el-button type="primary" @click="navigateToRules">
            <el-icon><Setting /></el-icon>权限规则管理
          </el-button>
        </div>
      </div>
    </el-card>

    <el-card class="table-card" shadow="hover">
      <div class="table-header">
        <div class="left">
          <h3>用户列表</h3>
          <el-tag type="info">共 {{ totalCount }} 条记录</el-tag>
        </div>
      </div>

      <el-table v-loading="loading" :data="filteredSellers" border style="width: 100%">
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="id" label="用户ID" width="80" />
        <el-table-column prop="accountName" label="账号名" min-width="120" />
        <el-table-column label="头像" width="80">
          <template #default="{ row }">
            <el-avatar :size="40" :src="row.photoUrl || defaultAvatar" @error="() => true" />
          </template>
        </el-table-column>
        <el-table-column prop="gender" label="性别" width="80">
          <template #default="{ row }">
            {{ row.gender === '1' ? '男' : row.gender === '2' ? '女' : '未知' }}
          </template>
        </el-table-column>
        <el-table-column prop="phone" label="联系电话" width="130" />
        <el-table-column prop="email" label="邮箱" min-width="180" />
        <el-table-column prop="userRole" label="角色" width="120">
          <template #default="{ row }">
            <el-tag v-if="row.userRole === '超级管理员'" type="danger">超级管理员</el-tag>
            <el-tag v-else-if="row.userRole === '管理员'" type="warning">管理员</el-tag>
            <el-tag v-else type="info">普通用户</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag v-if="row.accountStatus === 1" type="success">已启用</el-tag>
            <el-tag v-else-if="row.accountStatus === 0" type="warning">待审核</el-tag>
            <el-tag v-else-if="row.accountStatus === -1" type="danger">已禁用</el-tag>
            <el-tag v-else type="info">未知</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="openPermissionDialog(row)">
              权限设置
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 权限设置对话框 -->
    <el-dialog
      v-model="permissionDialogVisible"
      :title="`权限设置 - ${selectedSeller?.accountName || ''}`"
      width="600px"
    >
      <div v-if="selectedSeller" class="permission-dialog-content">
        <div class="seller-info-section">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="账号名">{{
              selectedSeller?.accountName || '未知'
            }}</el-descriptions-item>
            <el-descriptions-item label="用户ID">{{
              selectedSeller?.id || '-'
            }}</el-descriptions-item>
            <el-descriptions-item label="联系方式">{{
              selectedSeller?.phone || '-'
            }}</el-descriptions-item>
            <el-descriptions-item label="邮箱">{{
              selectedSeller?.email || '-'
            }}</el-descriptions-item>
            <el-descriptions-item label="角色">{{
              selectedSeller?.userRole || '普通用户'
            }}</el-descriptions-item>
            <el-descriptions-item label="账号状态">
              <el-tag v-if="selectedSeller?.accountStatus === 1" type="success">已启用</el-tag>
              <el-tag v-else-if="selectedSeller?.accountStatus === 0" type="warning">待审核</el-tag>
              <el-tag v-else-if="selectedSeller?.accountStatus === -1" type="danger">已禁用</el-tag>
              <el-tag v-else type="info">未知</el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="current-permissions-section">
          <h4>当前权限</h4>
          <div class="permission-tags">
            <el-tag
              v-for="perm in selectedSeller?.Permission"
              :key="perm"
              class="permission-tag"
              closable
              @close="handleRemovePermission(perm)"
            >
              {{ getPermissionLabel(perm) }}
            </el-tag>
            <div
              v-if="!selectedSeller?.Permission || selectedSeller?.Permission.length === 0"
              class="no-permissions"
            >
              暂无权限
            </div>
          </div>
        </div>

        <div class="add-permission-section">
          <h4>添加新权限</h4>
          <el-form
            :model="newPermission"
            label-width="120px"
            :rules="permissionRules"
            ref="permissionFormRef"
          >
            <el-form-item label="权限标识" prop="permissionCode">
              <el-select
                v-model="newPermission.permissionCode"
                placeholder="选择权限标识"
                style="width: 100%"
              >
                <el-option-group
                  v-for="group in permissionGroups"
                  :key="group.label"
                  :label="group.label"
                >
                  <el-option
                    v-for="item in group.options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                    :disabled="
                      selectedSeller?.Permission && selectedSeller?.Permission.includes(item.value)
                    "
                  />
                </el-option-group>
              </el-select>
            </el-form-item>
            <el-form-item label="权限描述" prop="description">
              <el-input v-model="newPermission.description" placeholder="请输入权限描述" />
            </el-form-item>
          </el-form>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="permissionDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="addPermission" :loading="savingPermission">
            添加权限
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus'
import { Search, Setting } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import {
  getAllSellers,
  addSellerPermission,
  removeSellerPermission,
  getSellerPermission,
  type Seller as SellerType,
  type SellerPermission as SellerPermissionType,
} from '@/api/permission'

// 路由
const router = useRouter()

// 头像默认图片
const defaultAvatar = 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'

// 使用导入的并合并自定义字段
type SellerWithPermission = {
  id: number
  accountName: string
  gender?: string
  phone?: string
  email?: string
  accountStatus?: number
  photoUrl?: string
  userRole?: string
  createTime?: string | Date
  lastLoginTime?: string | Date
  Permission?: string[]
}

// 表格数据
const loading = ref(false)
const sellerList = ref<SellerWithPermission[]>([])
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)

// 时间格式化函数
const formatDateTime = (time: string | Date | undefined) => {
  if (!time) return '-'

  if (typeof time === 'string') {
    // 如果已经是格式化好的字符串，直接返回
    if (time.includes('-') && time.includes(':')) return time

    // 尝试转换为日期对象
    time = new Date(time)
  }

  if (!(time instanceof Date) || isNaN(time.getTime())) {
    return '-'
  }

  const year = time.getFullYear()
  const month = String(time.getMonth() + 1).padStart(2, '0')
  const day = String(time.getDate()).padStart(2, '0')
  const hours = String(time.getHours()).padStart(2, '0')
  const minutes = String(time.getMinutes()).padStart(2, '0')
  const seconds = String(time.getSeconds()).padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 过滤后的商家列表
const filteredSellers = computed(() => {
  let result = sellerList.value

  // 如果有搜索关键字，进行过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(
      (seller) =>
        (seller.accountName && seller.accountName.toLowerCase().includes(keyword)) ||
        (seller.email && seller.email.toLowerCase().includes(keyword)) ||
        (seller.phone && seller.phone.includes(keyword)),
    )
  }

  // 计算总记录数
  totalCount.value = result.length

  // 分页处理
  const startIndex = (currentPage.value - 1) * pageSize.value
  const endIndex = startIndex + pageSize.value
  return result.slice(startIndex, endIndex)
})

// 权限设置相关
const permissionDialogVisible = ref(false)
const selectedSeller = ref<SellerWithPermission | null>(null)
const savingPermission = ref(false)
const permissionFormRef = ref<FormInstance>()

// 新增权限表单
const newPermission = reactive<SellerPermissionType>({
  sellerId: 0,
  permissionCode: '',
  description: '',
})

// 表单验证规则
const permissionRules = {
  permissionCode: [{ required: true, message: '请选择权限标识', trigger: 'change' }],
  description: [
    { required: true, message: '请输入权限描述', trigger: 'blur' },
    { min: 2, max: 50, message: '描述长度应在2到50个字符之间', trigger: 'blur' },
  ],
}

// 权限组选项
const permissionGroups = [
  {
    label: '基础功能',
    options: [
      { label: '欢迎页面', value: 'welcome' },
      { label: '控制台', value: 'dashboard' },
      { label: '商户销售', value: 'merchant-sales' },
      { label: '退款分析', value: 'refund-analysis' },
      { label: '热销商品', value: 'hot-products' },
    ],
  },
  {
    label: '商品管理',
    options: [
      { label: '商品管理', value: 'Product' },
      { label: '商品列表', value: 'ProductList' },
      { label: '添加商品', value: 'ProductAdd' },
      { label: '编辑商品', value: 'ProductEdit' },
      { label: '商品审核', value: 'ProductAudit' },
    ],
  },
  {
    label: '用户管理',
    options: [
      { label: '用户管理', value: 'UserManagement' },
      { label: '用户列表', value: 'UserList' },
      { label: '用户详情', value: 'UserDetail' },
      { label: '购买分析', value: 'PurchaseAnalysis' },
    ],
  },
  {
    label: '佣金管理',
    options: [
      { label: '佣金模式', value: 'Commission' },
      { label: '团长列表', value: 'LeaderList' },
      { label: '添加团长', value: 'AddLeader' },
      { label: '编辑团长', value: 'EditLeader' },
      { label: '佣金设置', value: 'CommissionSettings' },
      { label: '邀请码管理', value: 'InvitationCodes' },
      { label: '佣金统计', value: 'CommissionStatistics' },
    ],
  },
  {
    label: '消息管理',
    options: [
      { label: '消息中心', value: 'Message' },
      { label: '消息列表', value: 'MessageList' },
    ],
  },
  {
    label: '商家管理',
    options: [
      { label: '商家管理', value: 'Seller' },
      { label: '商家审核', value: 'SellerAudit' },
      { label: '商家详情', value: 'SellerDetail' },
    ],
  },
  {
    label: '权限管理',
    options: [
      { label: '权限管理', value: 'Permission' },
      { label: '商家权限', value: 'SellerPermission' },
      { label: '规则管理', value: 'RuleManagement' },
    ],
  },
  {
    label: '分类管理',
    options: [
      { label: '分类管理', value: 'Category' },
      { label: '分类列表', value: 'CategoryList' },
    ],
  },
  {
    label: '客户与营销',
    options: [
      { label: '客户列表', value: 'customer-list' },
      { label: '营销优惠券', value: 'marketing-coupons' },
      { label: '营销折扣', value: 'marketing-discounts' },
    ],
  },
  {
    label: '个人中心',
    options: [
      { label: '个人资料', value: 'UserProfile' },
      { label: '用户设置', value: 'UserSettings' },
      { label: '帮助文档', value: 'help-docs' },
    ],
  },
]

// 获取权限标签名称
const getPermissionLabel = (permCode: string): string => {
  for (const group of permissionGroups) {
    const option = group.options.find((opt) => opt.value === permCode)
    if (option) return option.label
  }
  return permCode
}

// 搜索商家
const searchSellers = () => {
  currentPage.value = 1
  // 搜索逻辑已经通过计算属性 filteredSellers 实现
}

// 重置搜索条件
const resetSearch = () => {
  searchKeyword.value = ''
  currentPage.value = 1
}

// 处理分页变化
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

// 导航到规则管理页面
const navigateToRules = () => {
  router.push('/main/permission/rules')
}

// 打开权限设置对话框
const openPermissionDialog = async (seller: SellerWithPermission) => {
  selectedSeller.value = JSON.parse(JSON.stringify(seller))
  // 确保 Permission 字段存在
  if (selectedSeller.value && !selectedSeller.value.Permission) {
    selectedSeller.value.Permission = []
  }
  // 重置表单
  if (selectedSeller.value) {
    newPermission.sellerId = selectedSeller.value.id
    newPermission.permissionCode = ''
    newPermission.description = ''

    // 获取用户的最新权限数据
    try {
      const response = await getSellerPermission(selectedSeller.value.id)
      if (response && response.code === 1 && response.data) {
        // 提取权限代码到数组
        const permissions = response.data.map((item: any) => item.permissionCode)
        selectedSeller.value.Permission = permissions
      }
    } catch (error) {
      console.error('获取用户权限失败:', error)
      // 使用已有的权限数据，不阻止对话框打开
    }
  }
  permissionDialogVisible.value = true
}

// 移除商家权限
const handleRemovePermission = (permCode: string) => {
  if (!selectedSeller.value) return

  ElMessageBox.confirm(`确定要移除权限 "${getPermissionLabel(permCode)}" 吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      if (selectedSeller.value && selectedSeller.value.Permission && selectedSeller.value.id) {
        try {
          // 调用封装的API删除权限
          const response = await removeSellerPermission(selectedSeller.value.id, permCode)

          // 检查响应是否成功
          if (response && response.code === 1) {
            // 更新本地状态
            selectedSeller.value.Permission = selectedSeller.value.Permission.filter(
              (p) => p !== permCode,
            )
            ElMessage.success('权限移除成功')
          } else {
            ElMessage.error(response?.msg || '权限移除失败')
          }
        } catch (error) {
          console.error('移除权限失败:', error)
          ElMessage.error('权限移除失败，请稍后重试')
        }
      }
    })
    .catch(() => {
      // 用户取消操作
    })
}

// 添加新权限
const addPermission = async () => {
  if (!permissionFormRef.value) return
  if (!selectedSeller.value) return

  await permissionFormRef.value.validate(async (valid) => {
    if (valid && selectedSeller.value) {
      savingPermission.value = true

      try {
        // 准备请求数据
        const permissionData: SellerPermissionType = {
          sellerId: selectedSeller.value.id,
          permissionCode: newPermission.permissionCode,
          description: newPermission.description,
        }

        // 调用封装的API添加权限
        const response = await addSellerPermission(permissionData)

        if (response.code === 1) {
          ElMessage.success('权限添加成功')

          // 更新当前选中商家的权限列表
          if (selectedSeller.value && selectedSeller.value.Permission) {
            if (!selectedSeller.value.Permission.includes(newPermission.permissionCode)) {
              selectedSeller.value.Permission.push(newPermission.permissionCode)
            }
          }

          // 重置表单
          newPermission.permissionCode = ''
          newPermission.description = ''
        } else {
          // 处理不同情况的响应
          if (response.msg === '该权限已经授权给用户') {
            ElMessage.warning(response.msg)
          } else {
            ElMessage.error(response.msg || '权限添加失败')
          }
        }
      } catch (error) {
        console.error('添加权限失败:', error)
        ElMessage.error('权限添加失败，请稍后重试')
      } finally {
        savingPermission.value = false
      }
    }
  })
}

// 加载商家列表
const loadSellerList = async () => {
  loading.value = true
  try {
    const response = await getAllSellers()

    if (response && response.code === 1) {
      // 转换数据类型
      sellerList.value = (response.data || []).map((item: any) => ({
        id: item.id,
        accountName: item.accountName,
        gender: item.gender,
        phone: item.phone,
        email: item.email,
        accountStatus: item.accountStatus,
        photoUrl: item.photoUrl,
        userRole: item.userRole || '普通用户',
        createTime: item.createTime,
        Permission: item.permissions || [],
      }))
      totalCount.value = sellerList.value.length
    } else {
      ElMessage.error(response?.msg || '获取用户列表失败')
      // 加载模拟数据
      loadMockData()
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败，请稍后重试')
    // 加载模拟数据
    loadMockData()
  } finally {
    loading.value = false
  }
}

// 加载模拟数据
const loadMockData = () => {
  sellerList.value = Array.from({ length: 15 }, (_, i) => ({
    id: i + 1,
    accountName: `user${i + 1}`,
    gender: i % 2 === 0 ? '1' : '2',
    phone: `**********${i}`,
    email: `user${i + 1}@example.com`,
    accountStatus: i % 3 === 0 ? 0 : i % 3 === 1 ? 1 : -1,
    photoUrl: '',
    userRole: i % 3 === 0 ? '超级管理员' : i % 3 === 1 ? '管理员' : '普通用户',
    createTime: new Date(Date.now() - i * ******** * 30),
    Permission:
      i % 3 === 0
        ? ['dashboard', 'Product', 'ProductList', 'ProductAdd']
        : i % 3 === 1
          ? ['welcome', 'Product']
          : [],
  }))
  totalCount.value = sellerList.value.length
}

// 组件挂载时加载商家列表
onMounted(() => {
  loadSellerList()
})
</script>

<style scoped lang="scss">
.seller-permission-container {
  padding: 20px;

  .page-header {
    margin-bottom: 20px;

    h2 {
      margin: 0 0 8px 0;
      font-size: 24px;
      color: #303133;
    }

    p {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }

  .search-card {
    margin-bottom: 20px;

    .search-container {
      display: flex;
      gap: 15px;

      .search-input {
        width: 350px;
      }
    }
  }

  .action-card {
    margin-bottom: 20px;

    .action-container {
      display: flex;
      justify-content: space-between;
    }
  }

  .table-card {
    .table-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 15px;

      .left {
        display: flex;
        align-items: center;

        h3 {
          margin: 0 10px 0 0;
          font-size: 16px;
        }
      }
    }

    .pagination-container {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }

  .permission-dialog-content {
    .seller-info-section {
      margin-bottom: 20px;
    }

    .current-permissions-section {
      margin-bottom: 20px;

      h4 {
        margin: 0 0 10px 0;
        font-size: 16px;
        color: #303133;
      }

      .permission-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;

        .permission-tag {
          margin-right: 0;
        }

        .no-permissions {
          color: #909399;
          font-style: italic;
        }
      }
    }

    .add-permission-section {
      h4 {
        margin: 0 0 15px 0;
        font-size: 16px;
        color: #303133;
      }
    }
  }
}
</style>
