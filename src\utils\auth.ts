/**
 * Token 相关工具函数
 */

// Token相关的存储key
const TOKEN_KEY = 'sharewharf_token'
const TOKEN_EXPIRY_KEY = 'sharewharf_token_expiry'

/**
 * 获取 token
 * @returns 存储的 token
 */
export function getToken(): string | null {
  return localStorage.getItem(TOKEN_KEY)
}

/**
 * 设置 token
 * @param token token 值
 * @param expiresIn 过期时间（毫秒）
 */
export function setToken(token: string, expiresIn: number = 24 * 60 * 60 * 1000) {
  localStorage.setItem(TOKEN_KEY, token)

  // 设置过期时间
  const expiryTime = Date.now() + expiresIn
  localStorage.setItem(TOKEN_EXPIRY_KEY, expiryTime.toString())
}

/**
 * 移除 token
 */
export function removeToken() {
  localStorage.removeItem(TOKEN_KEY)
  localStorage.removeItem(TOKEN_EXPIRY_KEY)
}

/**
 * 检查 token 是否过期
 * @returns boolean
 */
export function isTokenExpired(): boolean {
  const expiryTime = localStorage.getItem(TOKEN_EXPIRY_KEY)
  if (!expiryTime) return true

  const expiry = parseInt(expiryTime)
  return isNaN(expiry) || Date.now() > expiry
}

/**
 * 刷新Token过期时间
 * @param expiresIn 新的过期时间（毫秒）
 */
export function refreshTokenExpiry(expiresIn: number = 24 * 60 * 60 * 1000) {
  const expiryTime = Date.now() + expiresIn
  localStorage.setItem(TOKEN_EXPIRY_KEY, expiryTime.toString())
}
