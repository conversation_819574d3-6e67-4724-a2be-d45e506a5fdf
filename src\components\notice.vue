<template>
  <div class="coming-soon-page">
    <div class="content-wrapper">
      <div class="card">
        <div class="card-header">
          <div class="logo-container">
            <div class="logo-circle">
              <el-icon class="header-icon"><Tools /></el-icon>
            </div>
          </div>
        </div>

        <div class="card-body">
          <h1 class="title">商品审核系统升级中</h1>
          <p class="subtitle">我们正在打造更好的审核体验</p>

          <div class="progress-section">
            <div class="progress-bar">
              <div class="progress-fill"></div>
            </div>
            <div class="progress-text">开发进度：75%</div>
          </div>

          <div class="features">
            <div class="feature-item">
              <el-icon class="feature-icon"><Check /></el-icon>
              <div class="feature-text">批量审核</div>
            </div>
            <div class="feature-item">
              <el-icon class="feature-icon"><Check /></el-icon>
              <div class="feature-text">智能分类</div>
            </div>
            <div class="feature-item">
              <el-icon class="feature-icon"><Loading /></el-icon>
              <div class="feature-text">数据分析</div>
            </div>
            <div class="feature-item">
              <el-icon class="feature-icon"><Loading /></el-icon>
              <div class="feature-text">自动审核</div>
            </div>
          </div>

          <div class="description">
            <p>
              尊敬的管理员，商品审核系统正在进行全面升级。新版本将提供更直观的界面、更高效的审核流程和更全面的数据分析能力。
            </p>
            <p>我们预计将在<span class="highlight">两周内</span>完成更新。</p>
          </div>

          <div class="action-buttons">
            <el-button type="primary" class="remind-button" @click="showNotification">
              <el-icon class="button-icon"><Bell /></el-icon>上线提醒
            </el-button>
            <el-button class="back-button" @click="goBack">
              <el-icon class="button-icon"><Back /></el-icon>返回首页
            </el-button>
          </div>
        </div>

        <div class="card-footer">
          <div class="pulse-dots">
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 提醒弹窗 -->
    <transition name="notification">
      <div v-if="isNotificationVisible" class="notification">
        <div class="notification-content">
          <el-icon class="success-icon"><SuccessFilled /></el-icon>
          <div class="notification-text">
            <h3>提醒设置成功</h3>
            <p>系统上线时，我们会立即通知您</p>
          </div>
          <el-icon class="close-icon" @click="isNotificationVisible = false"><Close /></el-icon>
        </div>
      </div>
    </transition>

    <!-- 背景装饰 -->
    <div class="decoration decoration-1"></div>
    <div class="decoration decoration-2"></div>
    <div class="decoration decoration-3"></div>
    <div class="decoration decoration-4"></div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { Check, Bell, Back, Tools, Loading, SuccessFilled, Close } from '@element-plus/icons-vue'

const router = useRouter()
const isNotificationVisible = ref(false)

const showNotification = () => {
  isNotificationVisible.value = true
  setTimeout(() => {
    isNotificationVisible.value = false
  }, 3000)
}

const goBack = () => {
  router.push('/main/welcome')
}
</script>

<style scoped lang="scss">
.coming-soon-page {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #eef2f7 100%);
  position: relative;
  overflow: hidden;
  padding: 20px;
}

.content-wrapper {
  position: relative;
  z-index: 10;
  width: 100%;
  max-width: 800px;
}

.card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  position: relative;
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.12);
  }
}

.card-header {
  padding: 30px 0 0;
  text-align: center;
}

.logo-container {
  display: flex;
  justify-content: center;
}

.logo-circle {
  width: 100px;
  height: 100px;
  background: linear-gradient(
    45deg,
    var(--el-color-primary-light-7),
    var(--el-color-primary-light-3)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  box-shadow: 0 4px 15px rgba(var(--el-color-primary-rgb), 0.2);
  animation: pulse 2s infinite;

  .header-icon {
    font-size: 40px;
    color: var(--el-color-primary);
  }
}

.card-body {
  padding: 20px 40px 40px;
  text-align: center;
}

.title {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 10px;
  background: linear-gradient(90deg, var(--el-color-primary), #409eff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.subtitle {
  font-size: 16px;
  color: #606266;
  margin-bottom: 30px;
}

.progress-section {
  margin: 30px 0;
}

.progress-bar {
  height: 8px;
  background-color: #f0f2f5;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  margin-bottom: 10px;
}

.progress-fill {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 75%;
  background: linear-gradient(90deg, var(--el-color-primary-light-5), var(--el-color-primary));
  border-radius: 4px;
  animation: fillProgress 2s ease-out;
}

.progress-text {
  text-align: right;
  font-size: 14px;
  color: #909399;
}

.features {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 30px;
  margin: 30px 0;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100px;
}

.feature-icon {
  width: 50px;
  height: 50px;
  background-color: #f5f7fa;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  font-size: 20px;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.1);
    background-color: var(--el-color-primary-light-8);
    color: var(--el-color-primary);
  }
}

.feature-text {
  font-size: 14px;
  color: #606266;
}

.description {
  margin: 30px 0;
  text-align: left;
  font-size: 14px;
  color: #606266;
  line-height: 1.6;

  .highlight {
    color: var(--el-color-primary);
    font-weight: 600;
    margin: 0 4px;
  }
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 30px;
}

.button-icon {
  margin-right: 5px;
}

.remind-button {
  box-shadow: 0 4px 12px rgba(var(--el-color-primary-rgb), 0.3);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(var(--el-color-primary-rgb), 0.4);
  }
}

.back-button {
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
  }
}

.card-footer {
  padding: 15px 0;
  background: linear-gradient(to right, #f5f7fa, #f0f2f5);
  display: flex;
  justify-content: center;
}

.pulse-dots {
  display: flex;
  align-items: center;
  gap: 8px;
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--el-color-primary-light-5);

  &:nth-child(1) {
    animation: pulseDot 1.5s infinite 0s;
  }

  &:nth-child(2) {
    animation: pulseDot 1.5s infinite 0.3s;
  }

  &:nth-child(3) {
    animation: pulseDot 1.5s infinite 0.6s;
  }
}

.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  max-width: 350px;

  .notification-content {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    border-left: 4px solid var(--el-color-success);
  }

  .success-icon {
    font-size: 24px;
    color: var(--el-color-success);
    margin-right: 15px;
  }

  .notification-text {
    flex: 1;

    h3 {
      margin: 0 0 5px 0;
      font-size: 16px;
      color: #303133;
    }

    p {
      margin: 0;
      font-size: 13px;
      color: #909399;
    }
  }

  .close-icon {
    color: #c0c4cc;
    cursor: pointer;
    font-size: 16px;
    padding: 5px;

    &:hover {
      color: #909399;
    }
  }
}

/* 装饰元素 */
.decoration {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(
    135deg,
    rgba(var(--el-color-primary-rgb), 0.1),
    rgba(var(--el-color-primary-rgb), 0.03)
  );
  z-index: 0;
  pointer-events: none;
}

.decoration-1 {
  width: 300px;
  height: 300px;
  top: -100px;
  right: 10%;
  animation: float 8s ease-in-out infinite;
}

.decoration-2 {
  width: 200px;
  height: 200px;
  bottom: -50px;
  left: 10%;
  animation: float 6s ease-in-out infinite 1s;
}

.decoration-3 {
  width: 150px;
  height: 150px;
  top: 40%;
  left: 5%;
  animation: float 7s ease-in-out infinite 0.5s;
}

.decoration-4 {
  width: 100px;
  height: 100px;
  bottom: 15%;
  right: 15%;
  animation: float 5s ease-in-out infinite 1.5s;
}

/* 动画 */
@keyframes pulse {
  0% {
    box-shadow: 0 4px 15px rgba(var(--el-color-primary-rgb), 0.2);
  }
  50% {
    box-shadow: 0 4px 25px rgba(var(--el-color-primary-rgb), 0.4);
  }
  100% {
    box-shadow: 0 4px 15px rgba(var(--el-color-primary-rgb), 0.2);
  }
}

@keyframes pulseDot {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.5);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0.7;
  }
}

@keyframes fillProgress {
  from {
    width: 0;
  }
  to {
    width: 75%;
  }
}

@keyframes float {
  0% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(5deg);
  }
  100% {
    transform: translateY(0) rotate(0deg);
  }
}

.notification-enter-active,
.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.notification-leave-to {
  opacity: 0;
  transform: translateY(-30px);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .card-body {
    padding: 20px;
  }

  .features {
    gap: 15px;
  }

  .feature-item {
    width: 80px;
  }

  .action-buttons {
    flex-direction: column;

    .remind-button,
    .back-button {
      width: 100%;
    }
  }

  .title {
    font-size: 24px;
  }

  .notification {
    left: 20px;
    right: 20px;
    max-width: none;
  }
}
</style>
