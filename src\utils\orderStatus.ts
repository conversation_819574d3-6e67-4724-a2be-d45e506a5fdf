/**
 * 订单状态管理工具类
 * 根据API文档定义的状态码和业务规则
 */

// 订单状态枚举 - 根据新API文档更新
export enum OrderStatus {
  PENDING_PAYMENT = 1,  // 待支付
  PAID = 2,             // 已支付
  CANCELLED = 3,        // 已取消
  SHIPPED = 4,          // 已发货
  COMPLETED = 5,        // 已完成
  CLOSED = 6            // 已关闭
}

// 状态文本映射
export const STATUS_TEXT_MAP: Record<number, string> = {
  [OrderStatus.PENDING_PAYMENT]: '待支付',
  [OrderStatus.PAID]: '已支付',
  [OrderStatus.CANCELLED]: '已取消',
  [OrderStatus.SHIPPED]: '已发货',
  [OrderStatus.COMPLETED]: '已完成',
  [OrderStatus.CLOSED]: '已关闭'
}

// 状态类型映射（用于Element Plus的tag组件）
export const STATUS_TYPE_MAP: Record<number, string> = {
  [OrderStatus.PENDING_PAYMENT]: 'warning',
  [OrderStatus.PAID]: 'info',
  [OrderStatus.CANCELLED]: 'danger',
  [OrderStatus.SHIPPED]: 'success',
  [OrderStatus.COMPLETED]: 'success',
  [OrderStatus.CLOSED]: 'info'
}

// 状态流转规则定义 - 根据新API文档更新
export const STATUS_FLOW_RULES: Record<number, number[]> = {
  [OrderStatus.PENDING_PAYMENT]: [OrderStatus.PAID, OrderStatus.CANCELLED],
  [OrderStatus.PAID]: [OrderStatus.SHIPPED, OrderStatus.CANCELLED],
  [OrderStatus.CANCELLED]: [], // 已取消状态不能再变更
  [OrderStatus.SHIPPED]: [OrderStatus.COMPLETED],
  [OrderStatus.COMPLETED]: [OrderStatus.CLOSED],
  [OrderStatus.CLOSED]: [] // 已关闭状态不能再变更
}

// 可执行操作定义 - 根据新API文档更新
export const AVAILABLE_ACTIONS: Record<number, string[]> = {
  [OrderStatus.PENDING_PAYMENT]: ['pay', 'cancel'],
  [OrderStatus.PAID]: ['ship', 'cancel'],
  [OrderStatus.CANCELLED]: [],
  [OrderStatus.SHIPPED]: ['complete'],
  [OrderStatus.COMPLETED]: ['close', 'refund'],
  [OrderStatus.CLOSED]: []
}

/**
 * 获取状态文本
 */
export function getStatusText(status: number): string {
  return STATUS_TEXT_MAP[status] || '未知状态'
}

/**
 * 获取状态类型（用于Element Plus的tag组件）
 */
export function getStatusType(status: number): string {
  return STATUS_TYPE_MAP[status] || 'info'
}

/**
 * 检查状态是否可以流转到目标状态
 */
export function canTransitionTo(currentStatus: number, targetStatus: number): boolean {
  const allowedStatuses = STATUS_FLOW_RULES[currentStatus] || []
  return allowedStatuses.includes(targetStatus)
}

/**
 * 获取当前状态可以流转到的状态列表
 */
export function getAvailableTransitions(currentStatus: number): number[] {
  return STATUS_FLOW_RULES[currentStatus] || []
}

/**
 * 获取当前状态可执行的操作列表
 */
export function getAvailableActions(currentStatus: number): string[] {
  return AVAILABLE_ACTIONS[currentStatus] || []
}

/**
 * 检查是否可以执行指定操作
 */
export function canPerformAction(currentStatus: number, action: string): boolean {
  const availableActions = getAvailableActions(currentStatus)
  return availableActions.includes(action)
}

/**
 * 获取操作对应的目标状态 - 根据新API文档更新
 */
export function getTargetStatusForAction(action: string): number | null {
  const actionStatusMap: Record<string, number> = {
    'pay': OrderStatus.PAID,
    'ship': OrderStatus.SHIPPED,
    'complete': OrderStatus.COMPLETED,
    'cancel': OrderStatus.CANCELLED,
    'close': OrderStatus.CLOSED,
    'refund': OrderStatus.COMPLETED // 退款后订单状态保持已完成
  }

  return actionStatusMap[action] || null
}

/**
 * 验证状态流转是否合法
 */
export function validateStatusTransition(
  currentStatus: number, 
  targetStatus: number, 
  action?: string
): { valid: boolean; message?: string } {
  // 检查状态是否存在
  if (!STATUS_TEXT_MAP[currentStatus]) {
    return { valid: false, message: '当前状态不存在' }
  }
  
  if (!STATUS_TEXT_MAP[targetStatus]) {
    return { valid: false, message: '目标状态不存在' }
  }
  
  // 检查状态流转是否合法
  if (!canTransitionTo(currentStatus, targetStatus)) {
    return { 
      valid: false, 
      message: `订单状态不能从"${getStatusText(currentStatus)}"变更为"${getStatusText(targetStatus)}"` 
    }
  }
  
  // 如果指定了操作，检查操作是否合法
  if (action) {
    if (!canPerformAction(currentStatus, action)) {
      return { 
        valid: false, 
        message: `当前状态"${getStatusText(currentStatus)}"不支持操作"${action}"` 
      }
    }
    
    const expectedTargetStatus = getTargetStatusForAction(action)
    if (expectedTargetStatus && expectedTargetStatus !== targetStatus) {
      return { 
        valid: false, 
        message: `操作"${action}"的目标状态应该是"${getStatusText(expectedTargetStatus)}"` 
      }
    }
  }
  
  return { valid: true }
}

/**
 * 获取状态的颜色（用于UI显示） - 根据新API文档更新
 */
export function getStatusColor(status: number): string {
  const colorMap: Record<number, string> = {
    [OrderStatus.PENDING_PAYMENT]: '#E6A23C',
    [OrderStatus.PAID]: '#409EFF',
    [OrderStatus.CANCELLED]: '#F56C6C',
    [OrderStatus.SHIPPED]: '#67C23A',
    [OrderStatus.COMPLETED]: '#67C23A',
    [OrderStatus.CLOSED]: '#909399'
  }

  return colorMap[status] || '#909399'
}

/**
 * 判断订单是否为终态（不能再变更状态） - 根据新API文档更新
 */
export function isFinalStatus(status: number): boolean {
  return [
    OrderStatus.CANCELLED,
    OrderStatus.CLOSED
  ].includes(status)
}

/**
 * 判断订单是否可以取消 - 根据新API文档更新
 */
export function canCancel(status: number): boolean {
  return [
    OrderStatus.PENDING_PAYMENT,
    OrderStatus.PAID
  ].includes(status)
}

/**
 * 判断订单是否可以退款 - 根据新API文档更新
 */
export function canRefund(status: number): boolean {
  return [
    OrderStatus.COMPLETED
  ].includes(status)
}
