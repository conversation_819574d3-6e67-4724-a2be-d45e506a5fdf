/**
 * 退款相关类型定义
 */

import type { OrderVO } from './order'

// 退款查询DTO
export interface RefundQueryDTO {
  refundNo?: string           // 退款申请单号
  orderId?: number           // 订单ID
  orderNumber?: string       // 订单号
  buyerId?: number          // 申请人ID
  applicationStatus?: number // 申请状态：1-待处理，2-已同意，3-已拒绝，4-已取消，5-退款中，6-退款成功，7-退款失败
  refundType?: number       // 退款类型：1-仅退款，2-退货退款
  needApproval?: number     // 是否需要审核：0-不需要，1-需要
  approvalStatus?: number   // 审核状态：1-待审核，2-审核通过，3-审核拒绝
  beginTime?: string        // 开始时间（根据文档修正）
  endTime?: string          // 结束时间
  page?: number             // 页码
  pageSize?: number         // 每页大小
}

// 退款审核DTO - 根据后端DTO完整字段
export interface RefundApprovalDTO {
  refundApplicationId: number  // 退款申请ID
  approvalResult: number      // 审核结果：1-通过，2-拒绝
  approvalRemark?: string     // 审核备注
  approverName?: string       // 审核人姓名
  refundMethod?: number       // 退款方式：1-原路退回，2-余额退款
  actualRefundAmount?: number // 实际退款金额
}

// 退款申请VO
export interface RefundApplicationVO {
  id: number                    // 退款申请ID
  refundNo: string             // 退款申请单号
  orderId: number              // 订单ID
  orderNumber: string          // 订单号
  orderAmount: number          // 订单金额
  buyerId: number              // 申请人ID
  buyerName?: string           // 申请人姓名
  buyerPhone?: string          // 申请人手机号（根据文档补充）
  refundAmount: number         // 申请退款金额
  refundReason: string         // 退款理由
  refundType: number           // 退款类型：1-仅退款，2-退货退款
  refundTypeDesc?: string      // 退款类型描述
  applicationStatus: number    // 申请状态：1-待处理，2-已同意，3-已拒绝，4-已取消，5-退款中，6-退款成功，7-退款失败
  applicationStatusDesc?: string // 申请状态描述
  needApproval: number         // 是否需要审核：0-不需要，1-需要
  approvalStatus?: number      // 审核状态：1-待审核，2-审核通过，3-审核拒绝
  approvalStatusDesc?: string  // 审核状态描述
  approverId?: number          // 审核人ID
  approverName?: string        // 审核人姓名
  approvalTime?: string        // 审核时间
  approvalRemark?: string      // 审核备注
  refundMethod?: number        // 退款方式：1-原路退回，2-余额退款
  refundMethodDesc?: string    // 退款方式描述
  actualRefundAmount?: number  // 实际退款金额
  refundTime?: string          // 退款完成时间
  createTime: string           // 创建时间
  updateTime?: string          // 更新时间
  orderInfo?: OrderVO          // 订单信息
  approvalRecords?: RefundApprovalRecordVO[] // 审核记录列表
}

// 退款审核记录VO
export interface RefundApprovalRecordVO {
  id: number                   // 记录ID
  refundApplicationId: number  // 退款申请ID
  approverId: number          // 审核人ID
  approverName: string        // 审核人姓名
  approvalStatus: number      // 审核状态
  approvalRemark?: string     // 审核备注
  createTime: string          // 创建时间
}

// 退款统计信息
export interface RefundStatistics {
  totalApplications: number      // 总申请数
  pendingApprovals: number       // 待审核数
  approvedApplications: number   // 已通过数
  rejectedApplications: number   // 已拒绝数
  completedRefunds: number       // 已完成退款数
}

// 分页结果
export interface PageResult<T> {
  list: T[]                    // 数据列表
  total: number               // 总记录数
  page: number                // 当前页码
  pageSize: number            // 每页条数
  totalPages: number          // 总页数
}

// 退款状态枚举
export enum RefundApplicationStatus {
  PENDING = 1,        // 待处理
  APPROVED = 2,       // 已同意
  REJECTED = 3,       // 已拒绝
  CANCELLED = 4,      // 已取消
  REFUNDING = 5,      // 退款中
  REFUND_SUCCESS = 6, // 退款成功
  REFUND_FAILED = 7   // 退款失败
}

// 退款类型枚举
export enum RefundType {
  REFUND_ONLY = 1,    // 仅退款
  RETURN_REFUND = 2   // 退货退款
}

// 审核状态枚举
export enum ApprovalStatus {
  PENDING = 1,        // 待审核
  APPROVED = 2,       // 审核通过
  REJECTED = 3        // 审核拒绝
}

// 审核结果枚举（用于提交审核时的参数）
export enum ApprovalResult {
  APPROVED = 1,       // 通过
  REJECTED = 2        // 拒绝
}

// 退款方式枚举
export enum RefundMethod {
  ORIGINAL_ROUTE = 1, // 原路退回
  BALANCE = 2         // 余额退款
}

// 状态文本映射
export const REFUND_STATUS_TEXT_MAP: Record<number, string> = {
  [RefundApplicationStatus.PENDING]: '待处理',
  [RefundApplicationStatus.APPROVED]: '已同意',
  [RefundApplicationStatus.REJECTED]: '已拒绝',
  [RefundApplicationStatus.CANCELLED]: '已取消',
  [RefundApplicationStatus.REFUNDING]: '退款中',
  [RefundApplicationStatus.REFUND_SUCCESS]: '退款成功',
  [RefundApplicationStatus.REFUND_FAILED]: '退款失败'
}

// 退款类型文本映射
export const REFUND_TYPE_TEXT_MAP: Record<number, string> = {
  [RefundType.REFUND_ONLY]: '仅退款',
  [RefundType.RETURN_REFUND]: '退货退款'
}

// 审核状态文本映射
export const APPROVAL_STATUS_TEXT_MAP: Record<number, string> = {
  [ApprovalStatus.PENDING]: '待审核',
  [ApprovalStatus.APPROVED]: '审核通过',
  [ApprovalStatus.REJECTED]: '审核拒绝'
}

// 审核结果文本映射
export const APPROVAL_RESULT_TEXT_MAP: Record<number, string> = {
  [ApprovalResult.APPROVED]: '通过',
  [ApprovalResult.REJECTED]: '拒绝'
}

// 退款方式文本映射
export const REFUND_METHOD_TEXT_MAP: Record<number, string> = {
  [RefundMethod.ORIGINAL_ROUTE]: '原路退回',
  [RefundMethod.BALANCE]: '余额退款'
}

// 状态类型映射（用于Element Plus的tag组件）
export const REFUND_STATUS_TYPE_MAP: Record<number, string> = {
  [RefundApplicationStatus.PENDING]: 'warning',
  [RefundApplicationStatus.APPROVED]: 'success',
  [RefundApplicationStatus.REJECTED]: 'danger',
  [RefundApplicationStatus.CANCELLED]: 'info',
  [RefundApplicationStatus.REFUNDING]: 'primary',
  [RefundApplicationStatus.REFUND_SUCCESS]: 'success',
  [RefundApplicationStatus.REFUND_FAILED]: 'danger'
}

/**
 * 获取退款状态文本
 */
export function getRefundStatusText(status: number): string {
  return REFUND_STATUS_TEXT_MAP[status] || '未知状态'
}

/**
 * 获取退款类型文本
 */
export function getRefundTypeText(type: number): string {
  return REFUND_TYPE_TEXT_MAP[type] || '未知类型'
}

/**
 * 获取审核状态文本
 */
export function getApprovalStatusText(status: number): string {
  return APPROVAL_STATUS_TEXT_MAP[status] || '未知状态'
}

/**
 * 获取审核结果文本
 */
export function getApprovalResultText(result: number): string {
  return APPROVAL_RESULT_TEXT_MAP[result] || '未知结果'
}

/**
 * 获取退款方式文本
 */
export function getRefundMethodText(method: number): string {
  return REFUND_METHOD_TEXT_MAP[method] || '未知方式'
}

/**
 * 获取状态类型（用于Element Plus的tag组件）
 */
export function getRefundStatusType(status: number): string {
  return REFUND_STATUS_TYPE_MAP[status] || 'info'
}

/**
 * 判断是否可以审核
 */
export function canApprove(refund: RefundApplicationVO): boolean {
  return refund.needApproval === 1 && refund.approvalStatus === ApprovalStatus.PENDING
}

/**
 * 判断是否可以处理退款
 */
export function canProcessRefund(refund: RefundApplicationVO): boolean {
  return refund.applicationStatus === RefundApplicationStatus.APPROVED && 
         refund.approvalStatus === ApprovalStatus.APPROVED
}

/**
 * 判断是否可以取消
 */
export function canCancel(refund: RefundApplicationVO): boolean {
  return refund.applicationStatus === RefundApplicationStatus.PENDING || 
         refund.applicationStatus === RefundApplicationStatus.APPROVED
}
