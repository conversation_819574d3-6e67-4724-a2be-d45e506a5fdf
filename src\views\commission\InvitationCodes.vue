<template>
  <div class="invitation-codes-container">
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">邀请码管理</h1>
        <div class="page-subtitle">查看和管理团长邀请码使用情况</div>
      </div>
      <div class="header-right">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索邀请码/团长姓名"
          clearable
          style="width: 250px"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <el-button type="primary" @click="generateCodes">
          <el-icon><Plus /></el-icon>批量生成邀请码
        </el-button>
      </div>
    </div>

    <!-- 邀请码统计卡片 -->
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card shadow="hover" class="stats-card">
          <div class="stats-info">
            <div class="stats-label">总邀请码数量</div>
            <div class="stats-value">{{ stats.totalCodes }}</div>
            <div class="stats-desc">已生成的总邀请码数量</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="stats-card">
          <div class="stats-info">
            <div class="stats-label">已使用邀请码</div>
            <div class="stats-value">{{ stats.usedCodes }}</div>
            <div class="stats-desc">已被团长使用的邀请码数量</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="stats-card">
          <div class="stats-info">
            <div class="stats-label">剩余可用邀请码</div>
            <div class="stats-value">{{ stats.availableCodes }}</div>
            <div class="stats-desc">未被使用的邀请码数量</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="stats-card">
          <div class="stats-info">
            <div class="stats-label">本月新增邀请</div>
            <div class="stats-value">{{ stats.newInvitations }}</div>
            <div class="stats-desc">本月通过邀请码新增的团长数量</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 邀请码筛选条件 -->
    <el-card shadow="hover" class="filter-card">
      <div class="filter-container">
        <div class="filter-item">
          <span class="filter-label">状态：</span>
          <el-radio-group v-model="filter.status" size="small" @change="handleFilterChange">
            <el-radio-button label="all">全部</el-radio-button>
            <el-radio-button label="used">已使用</el-radio-button>
            <el-radio-button label="available">可用</el-radio-button>
            <el-radio-button label="expired">已过期</el-radio-button>
          </el-radio-group>
        </div>
        <div class="filter-item">
          <span class="filter-label">类型：</span>
          <el-radio-group v-model="filter.type" size="small" @change="handleFilterChange">
            <el-radio-button label="all">全部</el-radio-button>
            <el-radio-button label="merchant">商家招募</el-radio-button>
            <el-radio-button label="user">用户招募</el-radio-button>
          </el-radio-group>
        </div>
        <div class="filter-item">
          <span class="filter-label">生成时间：</span>
          <el-date-picker
            v-model="filter.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="small"
            style="width: 280px"
            @change="handleFilterChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 邀请码列表 -->
    <el-card shadow="hover" class="table-card">
      <template #header>
        <div class="card-header">
          <span>邀请码列表</span>
          <div class="header-actions">
            <el-button size="small" type="info" plain @click="exportCodes">
              <el-icon><Download /></el-icon>导出
            </el-button>
            <el-button
              size="small"
              type="danger"
              plain
              @click="batchDelete"
              :disabled="!selectedCodes.length"
            >
              <el-icon><Delete /></el-icon>批量删除
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        :data="filteredCodes"
        style="width: 100%"
        v-loading="loading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="code" label="邀请码" width="120" />
        <el-table-column prop="type" label="类型" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.type === 'merchant' ? 'success' : 'primary'" size="small">
              {{ scope.row.type === 'merchant' ? '商家招募' : '用户招募' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag
              :type="
                scope.row.status === 'available'
                  ? 'success'
                  : scope.row.status === 'used'
                    ? 'info'
                    : 'danger'
              "
              size="small"
            >
              {{
                scope.row.status === 'available'
                  ? '可用'
                  : scope.row.status === 'used'
                    ? '已使用'
                    : '已过期'
              }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="leaderName" label="使用团长" min-width="120">
          <template #default="scope">
            <span v-if="scope.row.leaderName">{{ scope.row.leaderName }}</span>
            <span v-else class="empty-value">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="leaderPhone" label="团长电话" width="120">
          <template #default="scope">
            <span v-if="scope.row.leaderPhone">{{ scope.row.leaderPhone }}</span>
            <span v-else class="empty-value">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="生成时间" width="160" />
        <el-table-column prop="useTime" label="使用时间" width="160">
          <template #default="scope">
            <span v-if="scope.row.useTime">{{ scope.row.useTime }}</span>
            <span v-else class="empty-value">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="expireTime" label="过期时间" width="160" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              link
              v-if="scope.row.leaderName"
              @click="viewLeader(scope.row.leaderId)"
            >
              查看团长
            </el-button>
            <el-button
              type="success"
              size="small"
              link
              v-if="scope.row.status === 'available'"
              @click="copyCode(scope.row.code)"
            >
              复制邀请码
            </el-button>
            <el-button
              type="danger"
              size="small"
              link
              v-if="scope.row.status === 'available'"
              @click="deleteCode(scope.row.id)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 批量生成邀请码对话框 -->
    <el-dialog v-model="generateDialogVisible" title="批量生成邀请码" width="500px">
      <el-form
        :model="generateForm"
        label-position="top"
        :rules="generateRules"
        ref="generateFormRef"
      >
        <el-form-item label="邀请码类型" prop="type">
          <el-radio-group v-model="generateForm.type">
            <el-radio-button label="merchant">商家招募</el-radio-button>
            <el-radio-button label="user">用户招募</el-radio-button>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="邀请码前缀" prop="prefix">
          <el-input v-model="generateForm.prefix" placeholder="请输入邀请码前缀">
            <template #append>
              <el-tooltip content="商家招募团长默认MRCNT，用户招募团长默认USER">
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="生成数量" prop="count">
          <el-input-number v-model="generateForm.count" :min="1" :max="100" style="width: 100%" />
        </el-form-item>

        <el-form-item label="有效期" prop="expireDays">
          <el-input-number
            v-model="generateForm.expireDays"
            :min="0"
            :step="1"
            style="width: 100%"
          />
          <div class="form-tip">设置天数，0表示永不过期</div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="generateDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmGenerate" :loading="generating">
            生成
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus, Download, Delete, QuestionFilled } from '@element-plus/icons-vue'

const router = useRouter()
const loading = ref(false)
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
const selectedCodes = ref<InvitationCode[]>([])
const generateDialogVisible = ref(false)
const generating = ref(false)
const generateFormRef = ref<FormInstance>()

// 统计数据
const stats = reactive({
  totalCodes: 128,
  usedCodes: 72,
  availableCodes: 50,
  newInvitations: 15,
})

// 筛选条件
const filter = reactive({
  status: 'all',
  type: 'all',
  dateRange: [],
})

// 生成邀请码表单
const generateForm = reactive({
  type: 'merchant',
  prefix: 'MRCNT',
  count: 10,
  expireDays: 30,
})

// 生成表单验证规则
const generateRules = reactive<FormRules>({
  type: [{ required: true, message: '请选择邀请码类型', trigger: 'change' }],
  prefix: [{ required: true, message: '请输入邀请码前缀', trigger: 'blur' }],
  count: [{ required: true, message: '请输入生成数量', trigger: 'blur' }],
})

// 邀请码类型接口
interface InvitationCode {
  id: number
  code: string
  type: string
  status: string
  leaderId?: number
  leaderName?: string
  leaderPhone?: string
  createTime: string
  useTime?: string
  expireTime: string
}

// 邀请码列表数据
const invitationCodes = ref<InvitationCode[]>([
  {
    id: 1,
    code: 'MRCNT0001',
    type: 'merchant',
    status: 'used',
    leaderId: 1,
    leaderName: '张三',
    leaderPhone: '13800138001',
    createTime: '2023-06-10 10:30:22',
    useTime: '2023-06-15 14:25:18',
    expireTime: '2023-12-10 10:30:22',
  },
  {
    id: 2,
    code: 'USER0002',
    type: 'user',
    status: 'used',
    leaderId: 2,
    leaderName: '李四',
    leaderPhone: '13900139002',
    createTime: '2023-06-15 11:20:15',
    useTime: '2023-07-22 09:15:32',
    expireTime: '2023-12-15 11:20:15',
  },
  {
    id: 3,
    code: 'MRCNT0003',
    type: 'merchant',
    status: 'used',
    leaderId: 3,
    leaderName: '王五',
    leaderPhone: '13700137003',
    createTime: '2023-07-05 16:45:30',
    useTime: '2023-08-10 16:55:20',
    expireTime: '2023-12-05 16:45:30',
  },
  {
    id: 4,
    code: 'USER0004',
    type: 'user',
    status: 'used',
    leaderId: 4,
    leaderName: '赵六',
    leaderPhone: '13600136004',
    createTime: '2023-07-10 09:30:00',
    useTime: '2023-09-05 11:10:45',
    expireTime: '2023-12-10 09:30:00',
  },
  {
    id: 5,
    code: 'MRCNT0005',
    type: 'merchant',
    status: 'available',
    createTime: '2023-08-01 14:20:10',
    expireTime: '2024-01-01 14:20:10',
  },
  {
    id: 6,
    code: 'USER0006',
    type: 'user',
    status: 'available',
    createTime: '2023-08-05 15:30:20',
    expireTime: '2024-01-05 15:30:20',
  },
  {
    id: 7,
    code: 'MRCNT0007',
    type: 'merchant',
    status: 'available',
    createTime: '2023-08-10 16:40:30',
    expireTime: '2024-01-10 16:40:30',
  },
  {
    id: 8,
    code: 'USER0008',
    type: 'user',
    status: 'expired',
    createTime: '2023-05-01 10:00:00',
    expireTime: '2023-08-01 10:00:00',
  },
])

// 根据筛选条件和搜索关键词过滤邀请码
const filteredCodes = computed(() => {
  let result = invitationCodes.value

  // 按状态过滤
  if (filter.status !== 'all') {
    result = result.filter((code) => code.status === filter.status)
  }

  // 按类型过滤
  if (filter.type !== 'all') {
    result = result.filter((code) => code.type === filter.type)
  }

  // 按日期范围过滤
  if (filter.dateRange && filter.dateRange.length === 2) {
    const startDate = filter.dateRange[0] as Date
    const endDate = filter.dateRange[1] as Date
    result = result.filter((code) => {
      const createTime = new Date(code.createTime)
      return createTime >= startDate && createTime <= endDate
    })
  }

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(
      (code) =>
        code.code.toLowerCase().includes(keyword) ||
        (code.leaderName && code.leaderName.toLowerCase().includes(keyword)),
    )
  }

  // 更新总数
  totalCount.value = result.length

  // 返回当前页数据
  const startIndex = (currentPage.value - 1) * pageSize.value
  return result.slice(startIndex, startIndex + pageSize.value)
})

// 处理筛选条件变化
const handleFilterChange = () => {
  currentPage.value = 1
}

// 处理每页条数变化
const handleSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1
}

// 处理页码变化
const handleCurrentChange = (val: number) => {
  currentPage.value = val
}

// 处理表格选择变化
const handleSelectionChange = (selection: InvitationCode[]) => {
  selectedCodes.value = selection
}

// 查看团长详情
const viewLeader = (leaderId: number) => {
  router.push(`/main/commission/edit-leader/${leaderId}`)
}

// 复制邀请码
const copyCode = (code: string) => {
  try {
    // 创建临时文本区域
    const textArea = document.createElement('textarea')
    textArea.value = code
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)

    ElMessage.success(`邀请码 ${code} 已复制到剪贴板`)
  } catch (error) {
    console.error('复制失败', error)
    ElMessage.error('复制失败，请手动复制')
  }
}

// 删除单个邀请码
const deleteCode = (id: number) => {
  ElMessageBox.confirm('确定要删除此邀请码吗？删除后将无法恢复。', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      // 这里应该调用API删除邀请码
      invitationCodes.value = invitationCodes.value.filter((item) => item.id !== id)
      ElMessage.success('邀请码已删除')
      updateStats()
    })
    .catch(() => {})
}

// 批量删除邀请码
const batchDelete = () => {
  if (selectedCodes.value.length === 0) {
    ElMessage.warning('请先选择要删除的邀请码')
    return
  }

  ElMessageBox.confirm(
    `确定要删除选中的 ${selectedCodes.value.length} 个邀请码吗？删除后将无法恢复。`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    },
  )
    .then(() => {
      // 这里应该调用API批量删除邀请码
      const selectedIds = selectedCodes.value.map((item) => item.id)
      invitationCodes.value = invitationCodes.value.filter((item) => !selectedIds.includes(item.id))
      ElMessage.success(`已删除 ${selectedIds.length} 个邀请码`)
      updateStats()
    })
    .catch(() => {})
}

// 导出邀请码
const exportCodes = () => {
  ElMessage.info('导出邀请码功能将在实际项目中实现')
}

// 打开批量生成邀请码对话框
const generateCodes = () => {
  generateDialogVisible.value = true

  // 根据选择的类型设置默认前缀
  generateForm.prefix = generateForm.type === 'merchant' ? 'MRCNT' : 'USER'
}

// 确认生成邀请码
const confirmGenerate = async () => {
  if (!generateFormRef.value) return

  await generateFormRef.value.validate((valid, fields) => {
    if (valid) {
      generating.value = true

      // 这里应该调用API生成邀请码
      // 模拟API请求
      setTimeout(() => {
        // 生成新邀请码并添加到列表
        const newCodes = []
        const now = new Date()
        const expireDate = new Date(now)
        expireDate.setDate(expireDate.getDate() + generateForm.expireDays)

        const formatDate = (date: Date) => {
          return date.toISOString().replace('T', ' ').substr(0, 19)
        }

        const lastId =
          invitationCodes.value.length > 0
            ? Math.max(...invitationCodes.value.map((item) => item.id))
            : 0

        for (let i = 0; i < generateForm.count; i++) {
          const randomDigits = Math.floor(Math.random() * 10000)
            .toString()
            .padStart(4, '0')
          const code = `${generateForm.prefix}${randomDigits}`

          newCodes.push({
            id: lastId + i + 1,
            code,
            type: generateForm.type,
            status: 'available',
            createTime: formatDate(now),
            expireTime: generateForm.expireDays > 0 ? formatDate(expireDate) : '永不过期',
          })
        }

        invitationCodes.value = [...newCodes, ...invitationCodes.value]

        generating.value = false
        generateDialogVisible.value = false
        ElMessage.success(`已成功生成 ${generateForm.count} 个邀请码`)
        updateStats()
      }, 1000)
    } else {
      console.error('表单验证失败', fields)
    }
  })
}

// 更新统计数据
const updateStats = () => {
  stats.totalCodes = invitationCodes.value.length
  stats.usedCodes = invitationCodes.value.filter((item) => item.status === 'used').length
  stats.availableCodes = invitationCodes.value.filter((item) => item.status === 'available').length
}

// 获取邀请码列表
const fetchInvitationCodes = () => {
  loading.value = true
  // 实际项目中这里会调用API获取邀请码列表
  setTimeout(() => {
    // 模拟请求延迟
    loading.value = false
    updateStats()
  }, 500)
}

onMounted(() => {
  fetchInvitationCodes()
})
</script>

<style scoped lang="scss">
.invitation-codes-container {
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #eef2f5 100%);
  border-radius: 12px;
  min-height: calc(100vh - 40px);

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    .header-left {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        margin: 0 0 8px 0;
        background: linear-gradient(120deg, #3a7bd5, #2c5499);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .page-subtitle {
        font-size: 14px;
        color: #606266;
      }
    }

    .header-right {
      display: flex;
      gap: 16px;
    }
  }

  .stats-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    margin-bottom: 20px;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .stats-info {
      text-align: center;
      padding: 10px;

      .stats-label {
        font-size: 14px;
        color: #606266;
        margin-bottom: 10px;
      }

      .stats-value {
        font-size: 24px;
        font-weight: 600;
        color: #409eff;
        margin-bottom: 10px;
      }

      .stats-desc {
        font-size: 12px;
        color: #909399;
      }
    }
  }

  .filter-card {
    margin-bottom: 20px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);

    .filter-container {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      padding: 10px;

      .filter-item {
        display: flex;
        align-items: center;

        .filter-label {
          margin-right: 8px;
          color: #606266;
          font-size: 14px;
        }
      }
    }
  }

  .table-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 16px;
      font-weight: 600;

      .header-actions {
        display: flex;
        gap: 16px;
      }
    }
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }

  .empty-value {
    color: #c0c4cc;
  }

  .form-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
  }
}

@media screen and (max-width: 768px) {
  .invitation-codes-container {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-right {
        width: 100%;
        flex-direction: column;
      }
    }

    .filter-container {
      flex-direction: column;
      align-items: flex-start;

      .filter-item {
        width: 100%;
      }
    }
  }
}
</style>
