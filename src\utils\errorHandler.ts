/**
 * 全局错误处理工具
 */

import { ElMessage, ElNotification } from 'element-plus'

// 错误类型枚举
export enum ErrorType {
  NETWORK = 'NETWORK',
  API = 'API',
  VALIDATION = 'VALIDATION',
  PERMISSION = 'PERMISSION',
  BUSINESS = 'BUSINESS',
  UNKNOWN = 'UNKNOWN'
}

// 错误信息接口
export interface ErrorInfo {
  type: ErrorType
  code?: string | number
  message: string
  details?: any
  timestamp: number
}

// API错误码映射
export const API_ERROR_CODES: Record<string | number, string> = {
  // 通用错误码
  400: '请求参数错误',
  401: '未授权访问',
  403: '权限不足',
  404: '资源不存在',
  405: '请求方法不允许',
  408: '请求超时',
  409: '资源冲突',
  422: '请求参数验证失败',
  429: '请求过于频繁',
  500: '服务器内部错误',
  502: '网关错误',
  503: '服务不可用',
  504: '网关超时',
  
  // 订单相关错误码
  1001: '订单不存在',
  1002: '订单状态不允许此操作',
  1003: '订单已被取消',
  1004: '订单已完成，无法修改',
  1005: '库存不足',
  1006: '支付失败',
  1007: '物流信息不完整',
  1008: '批量操作部分失败',
  1009: '导出任务创建失败',
  1010: '文件生成失败',
  
  // 用户相关错误码
  2001: '用户不存在',
  2002: '用户已被禁用',
  2003: '密码错误',
  2004: '验证码错误',
  2005: '登录已过期',
  
  // 权限相关错误码
  3001: '无访问权限',
  3002: '操作权限不足',
  3003: '角色权限不足'
}

/**
 * 解析错误信息
 */
export function parseError(error: any): ErrorInfo {
  const timestamp = Date.now()
  
  // 网络错误
  if (!error.response) {
    return {
      type: ErrorType.NETWORK,
      message: '网络连接失败，请检查网络设置',
      details: error,
      timestamp
    }
  }
  
  const { status, data } = error.response
  
  // API错误
  if (data && data.code !== undefined) {
    const message = data.msg || API_ERROR_CODES[data.code] || '操作失败'
    
    return {
      type: ErrorType.API,
      code: data.code,
      message,
      details: data,
      timestamp
    }
  }
  
  // HTTP状态码错误
  if (status) {
    const message = API_ERROR_CODES[status] || `HTTP错误 ${status}`
    
    return {
      type: ErrorType.API,
      code: status,
      message,
      details: error.response,
      timestamp
    }
  }
  
  // 未知错误
  return {
    type: ErrorType.UNKNOWN,
    message: error.message || '未知错误',
    details: error,
    timestamp
  }
}

/**
 * 处理错误并显示用户友好的提示
 */
export function handleError(error: any, options: {
  showMessage?: boolean
  showNotification?: boolean
  logError?: boolean
  customMessage?: string
} = {}) {
  const {
    showMessage = true,
    showNotification = false,
    logError = true,
    customMessage
  } = options
  
  const errorInfo = parseError(error)
  
  // 记录错误日志
  if (logError) {
    console.error('Error occurred:', errorInfo)
  }
  
  const displayMessage = customMessage || errorInfo.message
  
  // 显示错误消息
  if (showMessage) {
    ElMessage.error(displayMessage)
  }
  
  // 显示通知
  if (showNotification) {
    ElNotification.error({
      title: '操作失败',
      message: displayMessage,
      duration: 5000
    })
  }
  
  return errorInfo
}

/**
 * 处理API响应
 */
export function handleApiResponse<T = any>(
  response: any,
  options: {
    successMessage?: string
    showSuccessMessage?: boolean
    showErrorMessage?: boolean
    customErrorMessage?: string
  } = {}
): { success: boolean; data?: T; error?: ErrorInfo } {
  const {
    successMessage,
    showSuccessMessage = false,
    showErrorMessage = true,
    customErrorMessage
  } = options
  
  try {
    if (response.code === 1) {
      // 成功
      if (showSuccessMessage && successMessage) {
        ElMessage.success(successMessage)
      }
      
      return {
        success: true,
        data: response.data
      }
    } else {
      // 业务错误
      const errorInfo: ErrorInfo = {
        type: ErrorType.BUSINESS,
        code: response.code,
        message: response.msg || '操作失败',
        details: response,
        timestamp: Date.now()
      }
      
      if (showErrorMessage) {
        const displayMessage = customErrorMessage || errorInfo.message
        ElMessage.error(displayMessage)
      }
      
      return {
        success: false,
        error: errorInfo
      }
    }
  } catch (error) {
    // 解析错误
    const errorInfo = handleError(error, {
      showMessage: showErrorMessage,
      customMessage: customErrorMessage
    })
    
    return {
      success: false,
      error: errorInfo
    }
  }
}

/**
 * 创建带错误处理的异步操作包装器
 */
export function withErrorHandling<T extends (...args: any[]) => Promise<any>>(
  asyncFn: T,
  options: {
    loadingRef?: { value: boolean }
    successMessage?: string
    errorMessage?: string
    showSuccessMessage?: boolean
    showErrorMessage?: boolean
  } = {}
): T {
  const {
    loadingRef,
    successMessage,
    errorMessage,
    showSuccessMessage = false,
    showErrorMessage = true
  } = options
  
  return (async (...args: Parameters<T>) => {
    try {
      if (loadingRef) {
        loadingRef.value = true
      }
      
      const result = await asyncFn(...args)
      
      if (showSuccessMessage && successMessage) {
        ElMessage.success(successMessage)
      }
      
      return result
    } catch (error) {
      handleError(error, {
        showMessage: showErrorMessage,
        customMessage: errorMessage
      })
      throw error
    } finally {
      if (loadingRef) {
        loadingRef.value = false
      }
    }
  }) as T
}

/**
 * 防抖错误处理（避免短时间内重复显示相同错误）
 */
const errorCache = new Map<string, number>()
const DEBOUNCE_TIME = 3000 // 3秒内不重复显示相同错误

export function handleErrorWithDebounce(error: any, options: {
  showMessage?: boolean
  customMessage?: string
} = {}) {
  const errorInfo = parseError(error)
  const errorKey = `${errorInfo.type}_${errorInfo.code}_${errorInfo.message}`
  const now = Date.now()
  
  // 检查是否在防抖时间内
  if (errorCache.has(errorKey)) {
    const lastTime = errorCache.get(errorKey)!
    if (now - lastTime < DEBOUNCE_TIME) {
      return errorInfo
    }
  }
  
  // 更新缓存时间
  errorCache.set(errorKey, now)
  
  // 处理错误
  return handleError(error, options)
}

/**
 * 清理错误缓存
 */
export function clearErrorCache() {
  errorCache.clear()
}
