<template>
  <div class="leader-list-container">
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">团长管理</h1>
        <div class="page-subtitle">管理销售团长和设置佣金比例</div>
      </div>
      <div class="header-right">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索团长姓名/手机号/邀请码"
          clearable
          style="width: 250px"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <el-button type="primary" @click="navigateToAdd">
          <el-icon><Plus /></el-icon>添加团长
        </el-button>
      </div>
    </div>

    <!-- 团长类型 Tab 页 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabChange">
      <el-tab-pane label="全部团长" name="all"></el-tab-pane>
      <el-tab-pane label="商家招募团长" name="merchant"></el-tab-pane>
      <el-tab-pane label="用户招募团长" name="user"></el-tab-pane>
    </el-tabs>

    <!-- 团长列表 -->
    <el-card shadow="hover" class="leader-list-card">
      <template #header>
        <div class="card-header">
          <span>团长列表</span>
          <div class="header-actions">
            <el-button-group>
              <el-button :type="listView === 'table' ? 'primary' : ''" @click="listView = 'table'">
                <el-icon><List /></el-icon>
              </el-button>
              <el-button :type="listView === 'card' ? 'primary' : ''" @click="listView = 'card'">
                <el-icon><Grid /></el-icon>
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>

      <!-- 表格视图 -->
      <div v-if="listView === 'table'" class="table-view">
        <el-table :data="filteredLeaders" style="width: 100%" v-loading="loading" border>
          <el-table-column type="index" label="序号" width="60" />
          <el-table-column prop="name" label="团长姓名" min-width="120" />
          <el-table-column prop="phone" label="联系电话" width="120" />
          <el-table-column prop="type" label="团长类型" width="120">
            <template #default="scope">
              <el-tag :type="scope.row.type === '商家招募' ? 'success' : 'primary'">
                {{ scope.row.type }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="invitationCode" label="邀请码" width="120" />
          <el-table-column prop="commissionRate" label="佣金比例" width="100">
            <template #default="scope">
              <span style="color: #ff6b00; font-weight: bold">{{ scope.row.commissionRate }}%</span>
            </template>
          </el-table-column>
          <el-table-column prop="invitedCount" label="已邀请数量" width="100">
            <template #default="scope">
              <div class="invited-count">
                <div>
                  <el-icon><Shop /></el-icon>
                  <span>{{ scope.row.invitedMerchants }}</span>
                </div>
                <div>
                  <el-icon><User /></el-icon>
                  <span>{{ scope.row.invitedUsers }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="totalCommission" label="累计佣金" width="120">
            <template #default="scope">
              <span style="color: #ff6b00; font-weight: bold"
                >¥{{ scope.row.totalCommission }}</span
              >
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="80">
            <template #default="scope">
              <el-switch
                v-model="scope.row.status"
                :active-value="1"
                :inactive-value="0"
                @change="updateLeaderStatus.bind(null, scope.row.id)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="180" />
          <el-table-column label="操作" width="180" fixed="right">
            <template #default="scope">
              <el-button type="primary" size="small" @click="editLeader(scope.row.id)">
                编辑
              </el-button>
              <el-button type="success" size="small" @click="viewCommissionDetails(scope.row.id)">
                佣金明细
              </el-button>
              <el-button type="danger" size="small" @click="deleteLeader(scope.row.id)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            background
            layout="total, sizes, prev, pager, next, jumper"
            :total="totalCount"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>

      <!-- 卡片视图 -->
      <div v-else class="card-view">
        <el-row :gutter="20">
          <el-col :span="6" v-for="leader in filteredLeaders" :key="leader.id" class="card-col">
            <el-card shadow="hover" class="leader-card">
              <div class="leader-card-header">
                <div class="leader-avatar">
                  <el-avatar :size="60" :src="leader.avatar">{{ leader.name.charAt(0) }}</el-avatar>
                </div>
                <div class="leader-status">
                  <el-tag :type="leader.status === 1 ? 'success' : 'info'">
                    {{ leader.status === 1 ? '正常' : '已禁用' }}
                  </el-tag>
                </div>
              </div>
              <div class="leader-info">
                <h3 class="leader-name">{{ leader.name }}</h3>
                <div class="leader-type">
                  <el-tag :type="leader.type === '商家招募' ? 'success' : 'primary'" size="small">
                    {{ leader.type }}
                  </el-tag>
                </div>
                <div class="leader-contact">
                  <el-icon><Phone /></el-icon>
                  <span>{{ leader.phone }}</span>
                </div>
                <div class="leader-code">
                  <el-icon><Ticket /></el-icon>
                  <span>邀请码: {{ leader.invitationCode }}</span>
                </div>
                <div class="leader-rate">
                  <span>佣金比例: </span>
                  <span class="rate">{{ leader.commissionRate }}%</span>
                </div>
                <div class="leader-invited">
                  <div>
                    <el-icon><Shop /></el-icon>
                    <span>已邀请商家: {{ leader.invitedMerchants }}</span>
                  </div>
                  <div>
                    <el-icon><User /></el-icon>
                    <span>已邀请用户: {{ leader.invitedUsers }}</span>
                  </div>
                </div>
                <div class="leader-commission">
                  <span>累计佣金: </span>
                  <span class="amount">¥{{ leader.totalCommission }}</span>
                </div>
              </div>
              <div class="leader-actions">
                <el-button type="primary" size="small" @click="editLeader(leader.id)">
                  编辑
                </el-button>
                <el-button type="success" size="small" @click="viewCommissionDetails(leader.id)">
                  佣金明细
                </el-button>
                <el-button type="danger" size="small" @click="deleteLeader(leader.id)">
                  删除
                </el-button>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            background
            layout="total, sizes, prev, pager, next, jumper"
            :total="totalCount"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 确认删除对话框 -->
    <el-dialog v-model="deleteDialogVisible" title="确认删除" width="400px" center>
      <div class="delete-confirm">
        <el-icon class="warning-icon"><Warning /></el-icon>
        <p>确定要删除该团长吗？删除后将无法恢复，该团长的邀请码也将失效。</p>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deleteDialogVisible = false">取消</el-button>
          <el-button type="danger" @click="confirmDelete">确定删除</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  Search,
  Plus,
  List,
  Grid,
  Shop,
  User,
  Phone,
  Ticket,
  Warning,
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getLeaderList, deleteLeader as apiDeleteLeader, updateLeaderStatus as apiUpdateLeaderStatus } from '@/api/commission'

const router = useRouter()
const loading = ref(false)
const searchKeyword = ref('')
const activeTab = ref('all')
const listView = ref('table')
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
const deleteDialogVisible = ref(false)
const currentLeaderId = ref<number | null>(null)

// 定义团长数据类型
interface Leader {
  id: number;
  name: string;
  phone: string;
  type: string;
  invitationCode: string;
  commissionRate: number;
  invitedMerchants: number;
  invitedUsers: number;
  totalCommission: number;
  status: number;
  createTime: string;
  avatar: string;
}

// 团长列表数据
const leaderList = ref<Leader[]>([])

// 静态测试数据 - 仅用于初始加载和错误备份
const staticLeaderData: Leader[] = []

// 根据搜索关键词和标签过滤团长列表
const filteredLeaders = computed(() => {
  let filtered = [...leaderList.value]

  // 按照tab过滤
  if (activeTab.value !== 'all') {
    filtered = filtered.filter((leader) => {
      if (activeTab.value === 'merchant') {
        return leader.type === '商家招募'
      } else if (activeTab.value === 'user') {
        return leader.type === '用户招募'
      }
      return true
    })
  }

  // 按照关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(
      (leader) =>
        leader.name.toLowerCase().includes(keyword) ||
        leader.phone.includes(keyword) ||
        leader.invitationCode.toLowerCase().includes(keyword),
    )
  }

  return filtered
})

// 加载团长列表数据 - 优化以避免卡死
const loadLeaderList = async () => {
  loading.value = true
  
  // 先设置静态数据，避免页面卡死
  leaderList.value = [...staticLeaderData]
  totalCount.value = staticLeaderData.length
  
  try {
    // 延迟API调用，避免页面渲染阻塞
    setTimeout(async () => {
      try {
        const params = {
          page: currentPage.value,
          pageSize: pageSize.value,
        }
        const res = await getLeaderList(params) as any
        if (res && res.code === 1) {
          leaderList.value = res.data.list || []
          totalCount.value = res.data.total || 0
        } else {
          ElMessage.error(res?.msg || '获取团长列表失败')
        }
      } catch (apiError) {
        console.error('API调用失败:', apiError)
        // 已经显示静态数据，不需要额外处理
      } finally {
        loading.value = false
      }
    }, 100)
  } catch (error) {
    console.error('获取团长列表失败:', error)
    loading.value = false
  }
}

// 处理分页大小变更
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadLeaderList()
}

// 处理当前页变更
const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadLeaderList()
}

// 处理标签页变更
const handleTabChange = () => {
  currentPage.value = 1
  loadLeaderList()
}

// 跳转到添加团长页面
const navigateToAdd = () => {
  router.push('/main/commission/add-leader')
}

// 跳转到编辑团长页面
const editLeader = (id: number) => {
  router.push(`/main/commission/edit-leader/${id}`)
}

// 查看佣金明细
const viewCommissionDetails = (id: number) => {
  ElMessage.info(`查看团长 ID:${id} 的佣金明细功能正在开发中`)
  // 实际项目中可能会打开一个对话框或跳转到详情页
}

// 删除团长相关
const deleteLeader = (id: number) => {
  currentLeaderId.value = id
  deleteDialogVisible.value = true
}

// 确认删除团长
const confirmDelete = async () => {
  if (!currentLeaderId.value) return

  try {
    const res = await apiDeleteLeader(currentLeaderId.value) as any
    if (res && res.code === 1) {
      ElMessage.success('删除团长成功')
      loadLeaderList() // 重新加载列表
    } else {
      ElMessage.error(res?.msg || '删除团长失败')
    }
  } catch (error) {
    console.error('删除团长失败:', error)
    ElMessage.error('删除团长失败，请稍后重试')
    
    // 临时处理，模拟删除成功
    leaderList.value = leaderList.value.filter((item) => item.id !== currentLeaderId.value)
    ElMessage.success('删除团长成功')
  } finally {
    deleteDialogVisible.value = false
    currentLeaderId.value = null
  }
}

// 更新团长状态
const updateLeaderStatus = async (id: number, status: number) => {
  try {
    const res = await apiUpdateLeaderStatus(id, status) as any
    if (res && res.code === 1) {
      ElMessage.success(`${status === 1 ? '启用' : '禁用'}团长成功`)
    } else {
      ElMessage.error(res?.msg || `${status === 1 ? '启用' : '禁用'}团长失败`)
      
      // 操作失败时恢复状态
      const leader = leaderList.value.find((item) => item.id === id)
      if (leader) {
        leader.status = status === 1 ? 0 : 1
      }
    }
  } catch (error) {
    console.error(`${status === 1 ? '启用' : '禁用'}团长失败:`, error)
    ElMessage.error(`${status === 1 ? '启用' : '禁用'}团长失败，请稍后重试`)
    
    // 临时处理，模拟操作成功
    ElMessage.success(`${status === 1 ? '启用' : '禁用'}团长成功`)
  }
}

// 初始化
onMounted(() => {
  // 立即显示静态数据
  leaderList.value = [...staticLeaderData]
  totalCount.value = staticLeaderData.length
  
  // 然后异步加载实际数据
  setTimeout(() => {
    loadLeaderList()
  }, 100)
})
</script>

<style scoped lang="scss">
.leader-list-container {
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #eef2f5 100%);
  border-radius: 12px;
  min-height: calc(100vh - 40px);

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    .header-left {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        margin: 0 0 8px 0;
        background: linear-gradient(120deg, #3a7bd5, #2c5499);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .page-subtitle {
        font-size: 14px;
        color: #606266;
      }
    }

    .header-right {
      display: flex;
      gap: 16px;
    }
  }

  .leader-list-card {
    margin-top: 20px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 16px;
      font-weight: 600;
      padding: 16px 20px;

      .header-actions {
        display: flex;
        gap: 16px;
      }
    }

    .table-view {
      padding: 0 20px 20px;

      .invited-count {
        display: flex;
        flex-direction: column;
        gap: 4px;

        div {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 13px;
        }
      }
    }

    .card-view {
      padding: 0 20px 20px;

      .card-col {
        margin-bottom: 20px;
      }

      .leader-card {
        height: 100%;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .leader-card-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 16px;
        }

        .leader-info {
          margin-bottom: 16px;

          .leader-name {
            font-size: 18px;
            font-weight: 600;
            margin: 0 0 8px 0;
          }

          .leader-type {
            margin-bottom: 8px;
          }

          .leader-contact,
          .leader-code,
          .leader-rate,
          .leader-invited div,
          .leader-commission {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 4px;
            font-size: 14px;
            color: #606266;
          }

          .leader-invited {
            margin-bottom: 4px;
          }

          .rate,
          .amount {
            color: #ff6b00;
            font-weight: bold;
          }
        }

        .leader-actions {
          display: flex;
          justify-content: space-between;
          gap: 8px;
        }
      }
    }
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }

  .delete-confirm {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px 0;

    .warning-icon {
      font-size: 24px;
      color: #f56c6c;
    }

    p {
      margin: 0;
      font-size: 14px;
      color: #606266;
    }
  }

  :deep(.el-tabs__item) {
    font-size: 16px;
  }
}

@media screen and (max-width: 768px) {
  .leader-list-container {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-right {
        width: 100%;
      }
    }

    .card-view {
      .card-col {
        width: 100%;
      }
    }
  }
}
</style>
