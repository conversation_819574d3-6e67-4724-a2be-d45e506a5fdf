<template>
  <div class="refund-test">
    <el-card>
      <template #header>
        <h3>退款功能测试</h3>
      </template>

      <div class="test-section">
        <h4>API接口测试</h4>
        <div class="api-tests">
          <el-button @click="testRefundList" :loading="loading.list">
            测试退款列表
          </el-button>
          <el-button @click="testPendingRefunds" :loading="loading.pending">
            测试待审核退款
          </el-button>
          <el-button @click="testRefundStatistics" :loading="loading.statistics">
            测试退款统计
          </el-button>
          <el-button @click="testRefundDetail" :loading="loading.detail">
            测试退款详情
          </el-button>
        </div>
      </div>

      <div class="test-section">
        <h4>类型定义测试</h4>
        <div class="type-tests">
          <div class="type-item">
            <span class="label">退款状态：</span>
            <el-tag v-for="status in refundStatuses" :key="status.value" 
                    :type="getRefundStatusType(status.value)" size="small" class="status-tag">
              {{ status.label }}
            </el-tag>
          </div>
          <div class="type-item">
            <span class="label">退款类型：</span>
            <el-tag v-for="type in refundTypes" :key="type.value" 
                    :type="type.value === 1 ? 'primary' : 'warning'" size="small" class="status-tag">
              {{ type.label }}
            </el-tag>
          </div>
          <div class="type-item">
            <span class="label">审核状态：</span>
            <el-tag v-for="status in approvalStatuses" :key="status.value" 
                    :type="status.value === 1 ? 'warning' : status.value === 2 ? 'success' : 'danger'" 
                    size="small" class="status-tag">
              {{ status.label }}
            </el-tag>
          </div>
        </div>
      </div>

      <div class="test-section">
        <h4>模拟数据测试</h4>
        <div class="mock-data">
          <el-button @click="generateMockData">生成模拟数据</el-button>
          <div v-if="mockRefund" class="mock-refund">
            <h5>模拟退款申请：</h5>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="退款单号">{{ mockRefund.refundNo }}</el-descriptions-item>
              <el-descriptions-item label="订单号">{{ mockRefund.orderNumber }}</el-descriptions-item>
              <el-descriptions-item label="申请人">{{ mockRefund.buyerName }}</el-descriptions-item>
              <el-descriptions-item label="退款金额">¥{{ mockRefund.refundAmount.toFixed(2) }}</el-descriptions-item>
              <el-descriptions-item label="退款类型">
                <el-tag :type="mockRefund.refundType === 1 ? 'primary' : 'warning'" size="small">
                  {{ getRefundTypeText(mockRefund.refundType) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="申请状态">
                <el-tag :type="getRefundStatusType(mockRefund.applicationStatus)" size="small">
                  {{ getRefundStatusText(mockRefund.applicationStatus) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="退款原因" :span="2">{{ mockRefund.refundReason }}</el-descriptions-item>
            </el-descriptions>
          </div>
        </div>
      </div>

      <div class="test-section">
        <h4>UI优化测试</h4>
        <div class="ui-tests">
          <el-button type="primary" @click="showApprovalDialog" size="large">
            <el-icon><CircleCheck /></el-icon>
            测试优化后的审核对话框
          </el-button>
          <p class="test-desc">点击按钮测试新的审核对话框UI设计，包括卡片化信息展示和美化的审核表单</p>
        </div>
      </div>

      <div class="test-section">
        <h4>API功能测试</h4>
        <div class="api-function-tests">
          <el-row :gutter="16">
            <el-col :span="8">
              <el-button type="success" @click="testProcessRefund" :loading="testing.process">
                <el-icon><Money /></el-icon>
                测试处理退款
              </el-button>
            </el-col>
            <el-col :span="8">
              <el-button type="info" @click="testGetStatistics" :loading="testing.statistics">
                <el-icon><DataAnalysis /></el-icon>
                测试获取统计
              </el-button>
            </el-col>
            <el-col :span="8">
              <el-button type="warning" @click="testGetByNo" :loading="testing.getByNo">
                <el-icon><Search /></el-icon>
                测试单号查询
              </el-button>
            </el-col>
          </el-row>
          <p class="test-desc">测试各种API接口功能，验证前后端交互是否正常</p>
        </div>
      </div>

      <div class="test-section">
        <h4>测试结果</h4>
        <div class="test-results">
          <pre>{{ JSON.stringify(testResults, null, 2) }}</pre>
        </div>
      </div>
    </el-card>

    <!-- 审核对话框测试 -->
    <!-- <RefundApprovalDialog
      v-model="approvalDialogVisible"
      :refund="mockRefund"
      @refresh="handleRefresh"
    /> -->
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { CircleCheck, Money, DataAnalysis, Search } from '@element-plus/icons-vue'
// import RefundApprovalDialog from './components/RefundApprovalDialog.vue'
import {
  getRefundList,
  getPendingRefunds,
  getRefundStatistics,
  getRefundById,
  processRefund,
  getRefundByNo
} from '@/api/refund'
import type { RefundApplicationVO } from '@/types/refund'
import {
  getRefundStatusText,
  getRefundStatusType,
  getRefundTypeText,
  RefundApplicationStatus,
  RefundType,
  ApprovalStatus
} from '@/types/refund'

// 加载状态
const loading = reactive({
  list: false,
  pending: false,
  statistics: false,
  detail: false
})

// 测试结果
const testResults = ref<any>({})

// 模拟退款数据
const mockRefund = ref<RefundApplicationVO | null>(null)

// 状态选项
const refundStatuses = [
  { value: RefundApplicationStatus.PENDING, label: '待处理' },
  { value: RefundApplicationStatus.APPROVED, label: '已同意' },
  { value: RefundApplicationStatus.REJECTED, label: '已拒绝' },
  { value: RefundApplicationStatus.CANCELLED, label: '已取消' },
  { value: RefundApplicationStatus.REFUNDING, label: '退款中' },
  { value: RefundApplicationStatus.REFUND_SUCCESS, label: '退款成功' },
  { value: RefundApplicationStatus.REFUND_FAILED, label: '退款失败' }
]

const refundTypes = [
  { value: RefundType.REFUND_ONLY, label: '仅退款' },
  { value: RefundType.RETURN_REFUND, label: '退货退款' }
]

const approvalStatuses = [
  { value: ApprovalStatus.PENDING, label: '待审核' },
  { value: ApprovalStatus.APPROVED, label: '审核通过' },
  { value: ApprovalStatus.REJECTED, label: '审核拒绝' }
]

// 测试退款列表
const testRefundList = async () => {
  loading.list = true
  try {
    const response = await getRefundList({
      page: 1,
      pageSize: 10
    })
    testResults.value.refundList = response
    ElMessage.success('退款列表测试成功')
  } catch (error) {
    testResults.value.refundListError = error
    ElMessage.error('退款列表测试失败')
  } finally {
    loading.list = false
  }
}

// 测试待审核退款
const testPendingRefunds = async () => {
  loading.pending = true
  try {
    const response = await getPendingRefunds(1, 10)
    testResults.value.pendingRefunds = response
    ElMessage.success('待审核退款测试成功')
  } catch (error) {
    testResults.value.pendingRefundsError = error
    ElMessage.error('待审核退款测试失败')
  } finally {
    loading.pending = false
  }
}

// 测试退款统计
const testRefundStatistics = async () => {
  loading.statistics = true
  try {
    const response = await getRefundStatistics()
    testResults.value.statistics = response
    ElMessage.success('退款统计测试成功')
  } catch (error) {
    testResults.value.statisticsError = error
    ElMessage.error('退款统计测试失败')
  } finally {
    loading.statistics = false
  }
}

// 测试退款详情
const testRefundDetail = async () => {
  loading.detail = true
  try {
    // 使用模拟ID测试
    const response = await getRefundById(1)
    testResults.value.refundDetail = response
    ElMessage.success('退款详情测试成功')
  } catch (error) {
    testResults.value.refundDetailError = error
    ElMessage.error('退款详情测试失败')
  } finally {
    loading.detail = false
  }
}

// 生成模拟数据
const generateMockData = () => {
  mockRefund.value = {
    id: Math.floor(Math.random() * 1000) + 1,
    refundNo: `RF${Date.now()}`,
    orderId: Math.floor(Math.random() * 1000) + 1,
    orderNumber: `ORD${Date.now()}`,
    orderAmount: Math.floor(Math.random() * 1000) + 100,
    buyerId: Math.floor(Math.random() * 100) + 1,
    buyerName: `用户${Math.floor(Math.random() * 100) + 1}`,
    refundAmount: Math.floor(Math.random() * 500) + 50,
    refundReason: '商品质量问题，申请退款',
    refundType: Math.random() > 0.5 ? RefundType.REFUND_ONLY : RefundType.RETURN_REFUND,
    applicationStatus: RefundApplicationStatus.PENDING,
    needApproval: 1,
    approvalStatus: ApprovalStatus.PENDING,
    createTime: new Date().toISOString().slice(0, 19).replace('T', ' ')
  }
  
  ElMessage.success('模拟数据生成成功')
}

// 审核对话框相关
const approvalDialogVisible = ref(false)

// API测试状态
const testing = reactive({
  process: false,
  statistics: false,
  getByNo: false
})

// 显示审核对话框
const showApprovalDialog = () => {
  // 设置模拟数据 - 使用正确的状态码
  mockRefund.value = {
    id: 1,
    refundNo: 'RF202401150001',
    orderNumber: 'ORD202401150001',
    refundAmount: 299.99,
    buyerName: '张三',
    buyerPhone: '13800138000',
    refundReason: '商品质量问题，申请退款。收到的商品与描述不符，存在明显的质量缺陷，影响正常使用。希望能够尽快处理退款申请。',
    needApproval: 1,
    approvalStatus: 1,    // 待审核
    applicationStatus: 1  // 待处理
  }
  approvalDialogVisible.value = true
  console.log('打开审核对话框，模拟数据:', mockRefund.value)
}

// 刷新处理
const handleRefresh = () => {
  ElMessage.success('审核完成，数据已刷新')
  console.log('刷新退款列表数据')
}

// 测试处理退款
const testProcessRefund = async () => {
  testing.process = true
  try {
    // 使用模拟的退款申请ID
    const response = await processRefund(1)
    if (response.code === 1) {
      ElMessage.success('处理退款测试成功')
      console.log('处理退款响应:', response)
    } else {
      ElMessage.error(response.msg || '处理退款测试失败')
    }
  } catch (error) {
    console.error('处理退款测试失败:', error)
    ElMessage.error('处理退款测试失败')
  } finally {
    testing.process = false
  }
}

// 测试获取统计信息
const testGetStatistics = async () => {
  testing.statistics = true
  try {
    const response = await getRefundStatistics()
    if (response.code === 1) {
      ElMessage.success('获取统计信息测试成功')
      console.log('统计信息:', response.data)
    } else {
      ElMessage.error(response.msg || '获取统计信息测试失败')
    }
  } catch (error) {
    console.error('获取统计信息测试失败:', error)
    ElMessage.error('获取统计信息测试失败')
  } finally {
    testing.statistics = false
  }
}

// 测试根据单号查询
const testGetByNo = async () => {
  testing.getByNo = true
  try {
    // 使用模拟的退款单号
    const response = await getRefundByNo('RF202401150001')
    if (response.code === 1) {
      ElMessage.success('根据单号查询测试成功')
      console.log('查询结果:', response.data)
    } else {
      ElMessage.error(response.msg || '根据单号查询测试失败')
    }
  } catch (error) {
    console.error('根据单号查询测试失败:', error)
    ElMessage.error('根据单号查询测试失败')
  } finally {
    testing.getByNo = false
  }
}
</script>

<style scoped lang="scss">
.refund-test {
  padding: 20px;

  .test-section {
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;

    h4 {
      margin: 0 0 15px 0;
      color: #303133;
      border-bottom: 2px solid #409eff;
      padding-bottom: 8px;
    }

    .api-tests {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
    }

    .ui-tests {
      text-align: center;

      .test-desc {
        margin-top: 15px;
        color: #606266;
        font-size: 14px;
        line-height: 1.6;
      }
    }

    .api-function-tests {
      .test-desc {
        margin-top: 15px;
        color: #606266;
        font-size: 14px;
        line-height: 1.6;
        text-align: center;
      }

      .el-button {
        width: 100%;
        margin-bottom: 10px;
      }
    }

    .type-tests {
      .type-item {
        margin-bottom: 15px;
        display: flex;
        align-items: center;

        .label {
          font-weight: 500;
          color: #606266;
          min-width: 100px;
          margin-right: 15px;
        }

        .status-tag {
          margin-right: 8px;
        }
      }
    }

    .mock-data {
      .mock-refund {
        margin-top: 20px;
        padding: 15px;
        background: white;
        border-radius: 4px;
        border: 1px solid #dcdfe6;

        h5 {
          margin: 0 0 15px 0;
          color: #303133;
        }
      }
    }

    .test-results {
      max-height: 400px;
      overflow-y: auto;
      background: white;
      padding: 15px;
      border-radius: 4px;
      border: 1px solid #dcdfe6;

      pre {
        margin: 0;
        font-size: 12px;
        line-height: 1.4;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .refund-test {
    padding: 10px;

    .test-section {
      padding: 15px;

      .api-tests {
        flex-direction: column;

        .el-button {
          width: 100%;
        }
      }

      .type-tests {
        .type-item {
          flex-direction: column;
          align-items: flex-start;

          .label {
            min-width: auto;
            margin-bottom: 8px;
          }
        }
      }
    }
  }
}
</style>
