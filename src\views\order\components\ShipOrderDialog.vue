<template>
  <el-dialog
    v-model="visible"
    title="订单发货"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="shipForm"
      :rules="rules"
      label-width="100px"
      label-position="left"
    >
      <el-form-item label="订单信息">
        <div class="order-info">
          <p><strong>订单号：</strong>{{ orderInfo?.number }}</p>
          <p><strong>买家ID：</strong>{{ orderInfo?.buyerId }}</p>
          <p><strong>订单金额：</strong>¥{{ orderInfo?.amount }}</p>
        </div>
      </el-form-item>

      <el-form-item label="快递公司" prop="courierCode" required>
        <el-select
          v-model="shipForm.courierCode"
          placeholder="请选择快递公司"
          filterable
          clearable
          style="width: 100%"
          @change="handleCourierChange"
        >
          <el-option
            v-for="courier in courierList"
            :key="courier.code"
            :label="`${courier.name} (${courier.country})`"
            :value="courier.code"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="物流单号" prop="trackingNumber" required>
        <el-input
          v-model="shipForm.trackingNumber"
          placeholder="请输入物流单号"
          clearable
          maxlength="50"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="发货备注" prop="shipNote">
        <el-input
          v-model="shipForm.shipNote"
          type="textarea"
          placeholder="请输入发货备注（可选）"
          :rows="3"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="submitting"
          @click="handleSubmit"
        >
          确认发货
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { shipOrder, getCourierList } from '@/api/order'
import type { OrderVO, ShipOrderDTO, Courier } from '@/types/order'

interface Props {
  modelValue: boolean
  orderInfo?: OrderVO
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = ref(false)
const submitting = ref(false)
const formRef = ref<FormInstance>()
const courierList = ref<Courier[]>([])

// 发货表单
const shipForm = reactive<ShipOrderDTO>({
  orderId: 0,
  trackingNumber: '',
  courierCode: '',
  courierName: '',
  shipNote: ''
})

// 表单验证规则
const rules: FormRules = {
  courierCode: [
    { required: true, message: '请选择快递公司', trigger: 'change' }
  ],
  trackingNumber: [
    { required: true, message: '请输入物流单号', trigger: 'blur' },
    { min: 1, max: 50, message: '物流单号长度在 1 到 50 个字符', trigger: 'blur' }
  ]
}

// 监听对话框显示状态
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal && props.orderInfo) {
    resetForm()
    shipForm.orderId = props.orderInfo.id
  }
})

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 获取快递公司列表
const loadCourierList = async () => {
  try {
    const response = await getCourierList()
    if (response.code === 1) {
      courierList.value = response.data || []
    } else {
      ElMessage.error(response.msg || '获取快递公司列表失败')
    }
  } catch (error) {
    console.error('获取快递公司列表失败:', error)
    ElMessage.error('获取快递公司列表失败')
  }
}

// 快递公司选择变化
const handleCourierChange = (courierCode: string) => {
  const selectedCourier = courierList.value.find(c => c.code === courierCode)
  if (selectedCourier) {
    shipForm.courierName = selectedCourier.name
  }
}

// 提交发货
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    submitting.value = true

    const response = await shipOrder(shipForm)
    if (response.code === 1) {
      ElMessage.success('发货成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error(response.msg || '发货失败')
    }
  } catch (error) {
    console.error('发货失败:', error)
    ElMessage.error('发货失败')
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  shipForm.orderId = 0
  shipForm.trackingNumber = ''
  shipForm.courierCode = ''
  shipForm.courierName = ''
  shipForm.shipNote = ''
  formRef.value?.clearValidate()
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
}

// 组件挂载时加载快递公司列表
onMounted(() => {
  loadCourierList()
})
</script>

<style scoped lang="scss">
.order-info {
  background-color: #f5f7fa;
  padding: 16px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;

  p {
    margin: 0 0 8px 0;
    color: #606266;

    &:last-child {
      margin-bottom: 0;
    }

    strong {
      color: #303133;
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
