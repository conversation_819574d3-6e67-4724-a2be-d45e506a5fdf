/**
 * 物流跟踪相关类型定义
 */

// 注册物流单号DTO
export interface RegisterTrackingDTO {
  trackingNumber: string          // 物流单号
  carrierCode: string            // 运输商代码
  orderId?: number               // 关联订单ID
  originCountry?: string         // 发货国家代码
  destinationCountry?: string    // 目的地国家代码
  tag?: string                   // 自定义标签
  remark?: string                // 备注信息
  lang?: string                  // 翻译语言代码
  param?: string                 // 附加跟踪参数
}

// 物流查询DTO
export interface TrackingQueryDTO {
  page?: number                  // 页码
  pageSize?: number              // 每页大小
  trackingNumber?: string        // 物流单号
  carrierCode?: string           // 运输商代码
  orderId?: number               // 订单ID
  status?: string                // 物流状态
  trackingStatus?: string        // 跟踪状态
  beginTime?: string             // 开始时间
  endTime?: string               // 结束时间
}

// 物流跟踪记录
export interface TrackingRecord {
  id: number                     // 主键ID
  trackingNumber: string         // 物流单号
  carrierCode: string            // 运输商代码
  carrierName: string            // 运输商名称
  orderId?: number               // 关联订单ID
  orderNumber?: string           // 订单号
  originCountry?: string         // 发货国家代码
  destinationCountry?: string    // 目的地国家代码
  status?: string                // 物流主状态
  subStatus?: string             // 物流子状态
  subStatusDesc?: string         // 状态描述
  trackingStatus?: string        // 跟踪状态：Tracking/Stopped
  registerTime?: string          // 注册时间
  trackTime?: string             // 最后跟踪时间
  pushTime?: string              // 最后推送时间
  pushStatus?: string            // 推送状态：Success/Failure/NotPushed
  stopTrackTime?: string         // 停止跟踪时间
  stopTrackReason?: string       // 停止跟踪原因
  isRetracked?: boolean          // 是否已重新跟踪
  carrierChangeCount?: number    // 运输商修改次数
  tag?: string                   // 自定义标签
  remark?: string                // 备注信息
  lang?: string                  // 翻译语言代码
  param?: string                 // 附加跟踪参数
  latestEventTime?: string       // 最新事件时间
  latestEventInfo?: string       // 最新事件信息
  pickupTime?: string            // 揽收时间
  deliveryTime?: string          // 签收时间
  daysAfterOrder?: number        // 下单后天数
  createTime: string             // 创建时间
  updateTime?: string            // 更新时间
}

// 物流详情视图对象
export interface TrackingDetailVO {
  id: number                     // 主键ID
  trackingNumber: string         // 物流单号
  carrierCode: string            // 运输商代码
  carrierName: string            // 运输商名称
  orderId?: number               // 关联订单ID
  orderNumber?: string           // 订单号
  originCountry?: string         // 发货国家代码
  destinationCountry?: string    // 目的地国家代码
  status?: string                // 物流主状态
  statusDesc?: string            // 状态描述
  subStatus?: string             // 物流子状态
  subStatusDesc?: string         // 子状态描述
  trackingStatus?: string        // 跟踪状态
  registerTime?: string          // 注册时间
  trackTime?: string             // 最后跟踪时间
  latestEventTime?: string       // 最新事件时间
  latestEventInfo?: string       // 最新事件信息
  pickupTime?: string            // 揽收时间
  deliveryTime?: string          // 签收时间
  events?: TrackingEventVO[]     // 物流事件列表
  createTime: string             // 创建时间
  updateTime?: string            // 更新时间
}

// 物流事件视图对象
export interface TrackingEventVO {
  id: number                     // 主键ID
  trackingRecordId: number       // 跟踪记录ID
  trackingNumber: string         // 物流单号
  carrierCode: string            // 运输商代码
  eventTime: string              // 事件时间
  eventTimeRaw?: string          // 原始事件时间
  eventDescription: string       // 事件描述
  eventLocation?: string         // 事件地点
  eventCode?: string             // 事件代码
  eventStatus?: string           // 事件状态
  createTime: string             // 创建时间
}

// 运输商信息 - 与后端返回数据结构保持一致
export interface Carrier {
  code: string                   // 运输商代码
  courierCode: string            // 运输商代码（备用）
  name: string                   // 运输商名称
  courierName: string            // 运输商名称（备用）
  nameEn?: string                // 英文名称
  country: string                // 国家代码
  website?: string               // 官网地址
  phone?: string                 // 客服电话
  active: boolean                // 是否启用
}

// 物流状态枚举
export enum TrackingStatus {
  TRACKING = 'Tracking',         // 跟踪中
  STOPPED = 'Stopped'            // 已停止
}

// 推送状态枚举
export enum PushStatus {
  SUCCESS = 'Success',           // 推送成功
  FAILURE = 'Failure',           // 推送失败
  NOT_PUSHED = 'NotPushed'       // 未推送
}

// 物流主状态枚举
export enum LogisticsStatus {
  PENDING = 'Pending',           // 待处理
  INFO_RECEIVED = 'InfoReceived', // 信息已录入
  IN_TRANSIT = 'InTransit',      // 运输中
  OUT_FOR_DELIVERY = 'OutForDelivery', // 派送中
  DELIVERED = 'Delivered',       // 已签收
  EXCEPTION = 'Exception',       // 异常
  EXPIRED = 'Expired'            // 已过期
}

// 状态文本映射
export const TRACKING_STATUS_TEXT_MAP: Record<string, string> = {
  [TrackingStatus.TRACKING]: '跟踪中',
  [TrackingStatus.STOPPED]: '已停止'
}

export const PUSH_STATUS_TEXT_MAP: Record<string, string> = {
  [PushStatus.SUCCESS]: '推送成功',
  [PushStatus.FAILURE]: '推送失败',
  [PushStatus.NOT_PUSHED]: '未推送'
}

export const LOGISTICS_STATUS_TEXT_MAP: Record<string, string> = {
  [LogisticsStatus.PENDING]: '待处理',
  [LogisticsStatus.INFO_RECEIVED]: '信息已录入',
  [LogisticsStatus.IN_TRANSIT]: '运输中',
  [LogisticsStatus.OUT_FOR_DELIVERY]: '派送中',
  [LogisticsStatus.DELIVERED]: '已签收',
  [LogisticsStatus.EXCEPTION]: '异常',
  [LogisticsStatus.EXPIRED]: '已过期'
}

// 状态类型映射（用于Element Plus的tag组件）
export const TRACKING_STATUS_TYPE_MAP: Record<string, string> = {
  [TrackingStatus.TRACKING]: 'primary',
  [TrackingStatus.STOPPED]: 'info'
}

export const LOGISTICS_STATUS_TYPE_MAP: Record<string, string> = {
  [LogisticsStatus.PENDING]: 'warning',
  [LogisticsStatus.INFO_RECEIVED]: 'info',
  [LogisticsStatus.IN_TRANSIT]: 'primary',
  [LogisticsStatus.OUT_FOR_DELIVERY]: 'warning',
  [LogisticsStatus.DELIVERED]: 'success',
  [LogisticsStatus.EXCEPTION]: 'danger',
  [LogisticsStatus.EXPIRED]: 'info'
}

/**
 * 获取跟踪状态文本
 */
export function getTrackingStatusText(status: string): string {
  return TRACKING_STATUS_TEXT_MAP[status] || status
}

/**
 * 获取推送状态文本
 */
export function getPushStatusText(status: string): string {
  return PUSH_STATUS_TEXT_MAP[status] || status
}

/**
 * 获取物流状态文本
 */
export function getLogisticsStatusText(status: string): string {
  return LOGISTICS_STATUS_TEXT_MAP[status] || status
}

/**
 * 获取跟踪状态类型（用于Element Plus的tag组件）
 */
export function getTrackingStatusType(status: string): string {
  return TRACKING_STATUS_TYPE_MAP[status] || 'info'
}

/**
 * 获取物流状态类型（用于Element Plus的tag组件）
 */
export function getLogisticsStatusType(status: string): string {
  return LOGISTICS_STATUS_TYPE_MAP[status] || 'info'
}

/**
 * 判断是否已签收
 */
export function isDelivered(status: string): boolean {
  return status === LogisticsStatus.DELIVERED
}

/**
 * 判断是否异常
 */
export function isException(status: string): boolean {
  return status === LogisticsStatus.EXCEPTION
}

/**
 * 判断是否在途
 */
export function isInTransit(status: string): boolean {
  return status === LogisticsStatus.IN_TRANSIT || status === LogisticsStatus.OUT_FOR_DELIVERY
}

/**
 * 格式化物流事件时间
 */
export function formatEventTime(eventTime: string): string {
  try {
    const date = new Date(eventTime)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch {
    return eventTime
  }
}

/**
 * 计算运输天数
 */
export function calculateTransitDays(startTime: string, endTime?: string): number {
  try {
    const start = new Date(startTime)
    const end = endTime ? new Date(endTime) : new Date()
    const diffTime = end.getTime() - start.getTime()
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  } catch {
    return 0
  }
}
