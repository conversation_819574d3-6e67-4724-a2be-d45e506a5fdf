/**
 * 图片处理工具函数
 */

/**
 * 生成随机颜色
 * @returns 随机颜色字符串
 */
export function getRandomColor(): string {
  const colors = [
    '#f56a00',
    '#7265e6',
    '#ffbf00',
    '#00a2ae',
    '#f56c6c',
    '#909399',
    '#1890ff',
    '#52c41a',
    '#2980b9',
    '#8e44ad',
    '#e74c3c',
    '#16a085',
    '#f39c12',
    '#d35400',
    '#2c3e50',
    '#27ae60',
  ]
  return colors[Math.floor(Math.random() * colors.length)]
}

/**
 * 获取用户名首字母作为头像
 * @param name 用户名
 * @returns 首字母
 */
export function getNameInitial(name: string): string {
  if (!name) return '?'
  return name.charAt(0).toUpperCase()
}

/**
 * 为用户名生成头像图片URL
 * @param name 用户名
 * @param size 图片大小
 * @returns 头像图片的DataURL
 */
export function generateAvatarFromName(name: string, size = 100): string {
  const canvas = document.createElement('canvas')
  canvas.width = size
  canvas.height = size
  const ctx = canvas.getContext('2d')

  if (!ctx) return ''

  const initial = getNameInitial(name)
  const color = getRandomColor()

  // 绘制背景
  ctx.fillStyle = color
  ctx.fillRect(0, 0, size, size)

  // 绘制文字
  ctx.fillStyle = '#ffffff'
  ctx.font = `${size / 2}px Arial`
  ctx.textAlign = 'center'
  ctx.textBaseline = 'middle'
  ctx.fillText(initial, size / 2, size / 2)

  // 返回图片DataURL
  return canvas.toDataURL('image/png')
}

/**
 * 检查图片URL是否有效
 * @param url 图片URL
 * @returns Promise<boolean>
 */
export function isImageValid(url: string): Promise<boolean> {
  if (!url) return Promise.resolve(false)

  return new Promise((resolve) => {
    const img = new Image()
    img.onload = () => resolve(true)
    img.onerror = () => resolve(false)
    img.src = url
  })
}

/**
 * 获取用户头像URL，如果无效则返回基于名称的生成头像
 * @param photoUrl 头像URL
 * @param name 用户名
 * @returns Promise<string> 头像URL
 */
export async function getAvatarUrl(photoUrl: string, name: string): Promise<string> {
  const isValid = await isImageValid(photoUrl)
  if (isValid) return photoUrl

  return generateAvatarFromName(name)
}

/**
 * 将文件转换为Base64编码
 * @param file 文件对象
 * @returns Promise<string> Base64编码的字符串
 */
export function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => resolve(reader.result as string)
    reader.onerror = (error) => reject(error)
  })
}

// src/utils/imageHelpers.ts

/**
 * 将Base64数据转换为Blob对象
 * @param dataUrl Base64格式的图片数据
 * @returns Blob对象
 */
export function dataURLtoBlob(dataUrl: string): Blob | null {
  if (!dataUrl) return null;

  const arr = dataUrl.split(',');
  const mimeMatch = arr[0].match(/:(.*?);/);
  const mime = mimeMatch ? mimeMatch[1] : '';
  const raw = atob(arr[1]);
  const uInt8Array = new Uint8Array(raw.length);

  for (let i = 0; i < raw.length; i++) {
    uInt8Array[i] = raw.charCodeAt(i);
  }

  return new Blob([uInt8Array], { type: mime });
}
/**
 * 压缩图片
 * @param dataUrl 图片的DataURL
 * @param maxWidth 最大宽度
 * @param maxHeight 最大高度
 * @param quality 压缩质量(0-1)
 * @returns Promise<string> 压缩后的DataURL
 */
export function compressImage(
  dataUrl: string,
  maxWidth = 800,
  maxHeight = 800,
  quality = 0.8,
): Promise<string> {
  return new Promise((resolve) => {
    const img = new Image()
    img.src = dataUrl

    img.onload = () => {
      let width = img.width
      let height = img.height

      // 计算新尺寸
      if (width > height) {
        if (width > maxWidth) {
          height = Math.round((height * maxWidth) / width)
          width = maxWidth
        }
      } else {
        if (height > maxHeight) {
          width = Math.round((width * maxHeight) / height)
          height = maxHeight
        }
      }

      // 绘制到Canvas
      const canvas = document.createElement('canvas')
      canvas.width = width
      canvas.height = height

      const ctx = canvas.getContext('2d')
      ctx?.drawImage(img, 0, 0, width, height)

      // 转换为DataURL
      resolve(canvas.toDataURL('image/jpeg', quality))
    }
  })
}

/**
 * 检查图片大小并在需要时压缩
 * @param file 文件对象
 * @param maxSizeInMB 最大文件大小（MB）
 * @returns Promise<string> 处理后的图片DataURL
 */
export async function processImageForUpload(file: File, maxSizeInMB = 1): Promise<string> {
  const maxSize = maxSizeInMB * 1024 * 1024

  // 如果文件小于最大大小，直接转换为base64
  if (file.size <= maxSize) {
    return fileToBase64(file)
  }

  // 否则压缩
  const base64 = await fileToBase64(file)
  const compressed = await compressImage(base64, 1200, 1200, Math.min(maxSize / file.size, 0.9))

  return compressed
}

export async function base64ToBlob(base64String:string) {
  const arr = base64String.split(',');
  const mimeMatch = arr[0].match(/:(.*?);/);
  const mime = mimeMatch ? mimeMatch[1] : ''
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new Blob([u8arr], { type: mime });
}