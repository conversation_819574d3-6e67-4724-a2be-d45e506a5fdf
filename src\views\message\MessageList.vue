<template>
  <div class="message-list-container">
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">站内信管理</h1>
        <div class="page-subtitle">平台消息通知与管理</div>
      </div>
      <div class="header-right">
        <el-button-group class="view-toggle">
          <el-button :type="viewMode === 'all' ? 'primary' : ''" @click="viewMode = 'all'">
            <el-icon><ChatDotRound /></el-icon>全部消息
          </el-button>
          <el-button :type="viewMode === 'unread' ? 'primary' : ''" @click="viewMode = 'unread'">
            <el-icon><ChatLineRound /></el-icon>未读消息
            <el-badge v-if="unreadCount > 0" :value="unreadCount" class="unread-badge" />
          </el-button>
        </el-button-group>
        <el-button v-if="userStore.userInfo.role==='超级管理员'|| userStore.userInfo.role==='管理员'" type="primary" @click="showSendMessageDialog">
          <el-icon><Plus /></el-icon>发送新消息
        </el-button>
        <el-button v-else disabled type="primary" @click="showSendMessageDialog">
          <el-icon><Plus /></el-icon>发送新消息(商家站内信功能暂时关闭)
        </el-button>
      </div>
    </div>

    <!-- 消息类型标签页 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabChange">
      <el-tab-pane label="全部消息" name="all"></el-tab-pane>
      <el-tab-pane label="系统公告" name="announcement"></el-tab-pane>
      <el-tab-pane label="商家消息" name="merchant"></el-tab-pane>
    </el-tabs>

    <!-- 搜索和批量操作工具栏 -->
    <div class="toolbar">
      <div class="search-box">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索消息标题或内容"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
      <div class="action-box">
        <el-button type="primary" @click="handleAllMarkAsRead" plain>
          <el-icon><Check /></el-icon>全部标为已读
        </el-button>
        <el-button
          type="primary"
          :disabled="selectedMessages.length === 0"
          @click="handleBatchMarkAsRead"
          plain
        >
          <el-icon><Check /></el-icon>标为已读
        </el-button>
        <el-button
          type="danger"
          :disabled="selectedMessages.length === 0"
          @click="handleBatchDelete"
          plain
        >
          <el-icon><Delete /></el-icon>批量删除
        </el-button>
      </div>
    </div>

    <!-- 消息列表 -->
    <el-card shadow="hover" class="message-list-card" v-loading="loading">
      <template v-if="filteredMessages.length > 0">
        <el-table
          ref="messageTable"
          :data="filteredMessages"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column width="40">
            <template #default="scope">
              <el-badge is-dot :hidden="scope.row.isRead" type="danger">
                <el-icon><Message /></el-icon>
              </el-badge>
            </template>
          </el-table-column>
          <el-table-column prop="title" label="标题">
            <template #default="scope">
              <div class="message-title" :class="{ 'is-unread': !scope.row.isRead }">
                {{ scope.row.title }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="type" label="消息类型" width="120">
            <template #default="scope">
              <el-tag :type="getMessageTypeTag(scope.row.type)">
                {{ scope.row.msgType }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="sender" label="发送者" width="150" />
          <el-table-column prop="sendTime" label="发送时间" width="180" />
          <el-table-column label="操作" width="240" fixed="right">
            <template #default="scope">
              <el-button type="primary" size="small" @click="viewMessageDetail(scope.row.msgID)">
                查看
              </el-button>
              <el-button
                v-if="!scope.row.read"
                type="success"
                size="small"
                @click="markMessageAsRead(scope.row.msgID)"
              >
                标为已读
              </el-button>
              <el-button type="danger" size="small" @click="handelDeleteMessage(scope.row.msgID)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            background
            layout="total, sizes, prev, pager, next, jumper"
            :total="totalCount"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </template>

      <el-empty v-else description="暂无消息" />
    </el-card>

    <!-- 发送消息对话框 -->
    <el-dialog v-model="sendMessageDialogVisible" title="发送新消息" width="600px" destroy-on-close>
      <el-form
        ref="sendMessageFormRef"
        :model="sendMessageForm"
        :rules="sendMessageRules"
        label-width="100px"
      >
        <el-form-item label="消息类型" prop="type">
          <el-select
            v-model="sendMessageForm.type"
            placeholder="请选择消息类型"
            style="width: 100%"
            @change="handleMessageTypeChange"
          >
            <el-option
              v-if="userStore.userInfo.role=== '超级管理员' || userStore.userInfo.role === '管理员'"
              label="系统公告"
              value="系统公告"
            />
            <el-option
              v-if="userStore.userInfo.role === '超级管理员' || userStore.userInfo.role === '管理员'"
              label="商家消息"
              value="商家消息"
            />
            <el-option label="用户消息" value="用户消息" />
          </el-select>
        </el-form-item>

        <el-form-item
          v-if="sendMessageForm.type === '商家消息' && userStore.userInfo.role === '超级管理员'"
          label="搜索商家"
          prop="receiverIds"
        >
          <el-select
            ref="merchantSelectRef"
            v-model="sendMessageForm.receiver"
            filterable
            remote
            reserve-keyword
            placeholder="请输入商家名称搜索"
            :remote-method="remoteSearchMerchants"
            :loading="searchingReceivers"
            @change="handleMerchantSelect"
            style="width: 100%"
          >
            <el-option
              v-for="option in merchantOption"
              :label="option.name"
              :value="option"
              :key="option.id"
            >
              <div class="receiver-option">
                <span>{{ option.name }}</span>
                <span>-</span>
                <span class="receiver-info">{{ option.phone }}</span>
              </div>
            </el-option>
          </el-select>
          <div class="form-tip">
            <el-checkbox v-model="sendMessageForm.sendToAllMerchants">发送给所有商家</el-checkbox>
          </div>
        </el-form-item>

        <el-form-item
          v-if="sendMessageForm.type === '用户消息'"
          label="搜索用户"
          prop="receiverIds"
        >
          <el-select
            ref="userSelectRef"
            v-model="sendMessageForm.receiver"
            filterable
            remote
            placeholder="请输入用户名称搜索"
            :remote-method="remoteSearchUsers"
            :loading="searchingReceivers"
            style="width: 100%"
            @change="handleReceiverChange"
          >
            <el-option
              v-for="option in userOption"
              :label="option.name"
              :value="option.id"
              :key="option.id"
            >
              <div class="receiver-option">
                <span>{{ option.name }}</span>
                <span>-</span>
                <span class="receiver-info">{{ option.phone }}</span>
              </div>
            </el-option>
          </el-select>
          <div class="form-tip">
            <el-checkbox
              v-if="userStore.userInfo.role=== '超级管理员' || userStore.userInfo.role === '管理员'"
              v-model="sendMessageForm.sendToAllUsers"
              >发送给所有用户</el-checkbox
            >
          </div>
        </el-form-item>

        <el-form-item label="消息标题" prop="title">
          <el-input v-model="sendMessageForm.title" placeholder="请输入消息标题" />
        </el-form-item>

        <el-form-item label="消息内容" prop="content">
          <el-input
            v-model="sendMessageForm.content"
            type="textarea"
            placeholder="请输入消息内容"
            :rows="5"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="sendMessageDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitSendMessage" :loading="sending"> 发送 </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 消息详情对话框 -->
    <el-dialog
      v-model="messageDetailDialogVisible"
      :title="currentMessage?.title || '消息详情'"
      width="600px"
    >
      <div v-loading="loadingDetail">
        <div class="message-detail-header">
          <div class="message-type">
            <el-tag :type="getMessageTypeTag(currentMessage?.type)">
              {{ currentMessage?.msgType }}
            </el-tag>
          </div>
          <div class="message-meta">
            <span>发送者: {{ currentMessage?.senderName }}</span>
            <span>发送时间: {{ currentMessage?.sendTime }}</span>
          </div>
        </div>
        <div class="message-detail-content">
          <p>{{ currentMessage?.content }}</p>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="messageDetailDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {
  batchDeleteMessages,
  batchMarkAsRead,
  deleteMessage,
  getMessageDetail,
  getMessageList,
  getRecipientList,
  getUnreadCount,
  markAllAsRead,
  markAsRead,
  sendAnnouncement,
  sendMessage,
} from '@/api/message'
import { getSellerById } from '@/api/seller'
import { formatDate } from '@/utils/format'
import {
  ChatDotRound,
  ChatLineRound,
  Check,
  Delete,
  Message,
  Plus,
  Search,
} from '@element-plus/icons-vue'
import type { FormInstance } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'
import { throttle } from 'lodash'
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue'
import { useUserStore } from '@/stores/user'
let userInfo: any
// 视图模式: 全部或未读
const viewMode = ref('all')
// 标签页
const activeTab = ref('all')
const userStore = useUserStore()
// 搜索关键词
const searchKeyword = ref('')
// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)

const merchantSelectRef = ref()
const userSelectRef = ref()
// 加载状态
const loading = ref(false)
const loadingDetail = ref(false)
const sending = ref(false)
const searchingReceivers = ref(false)

// 消息列表
const messageList = ref<Message[]>([])
const selectedMessages = ref<Message[]>([])
const unreadCount = ref(0)
// 消息类型
type MessageType = '系统公告' | '商家消息'
// 消息接口
interface Message {
  msgID: string
  msgType: string
  title: string
  content: string
  sendTime: Date | string
  isRead: boolean
  type: MessageType
  senderName?: string
}
// // 消息详情相关
// interface currentMsg {
//   title: string
//   type: string
//   content: string
//   sender: string
//   sendTime: string
//   isRead: boolean
// }
// 当前选中的消息
const currentMessage = ref<Message>()
const messageDetailDialogVisible = ref(false)

// 接收者选项列表
interface receiver {
  id: number
  name: string
  phone: string
}
const merchantOption = ref<receiver[]>([])
const userOption = ref<receiver[]>([])
// 发送消息相关
const sendMessageDialogVisible = ref(false)
const sendMessageFormRef = ref<FormInstance | null>(null)
const sendMessageForm = ref({
  type: '', // 消息类型: announcement, merchant, user
  title: '',
  content: '',
  receiver: {
    type: '',
    id: 0,
  }, // 接收者
  sendToAllUsers: false, // 是否发送给所有用户
  sendToAllMerchants: false, // 是否发送给所有商家
})
const currentSelectUserID = ref(0)
const currentReseverList = ref<receiver[]>([])

// 表单验证规则
const sendMessageRules = {
  type: [{ required: true, message: '请选择消息类型', trigger: 'change' }],
  title: [{ required: true, message: '请输入消息标题', trigger: 'blur' }],
  content: [{ required: true, message: '请输入消息内容', trigger: 'blur' }],
  // receiverIds: [
  //   {
  //     required: true,
  //     validator: (rule, value, callback) => {
  //       // 如果是发送给所有商家或用户或消息类型是公告，则不需要选择接收者
  //       if (
  //         sendMessageForm.value.sendToAllUsers ||
  //         sendMessageForm.value.sendToAllMerchants ||
  //         sendMessageForm.value.type === 'announcement'
  //       ) {
  //         callback()
  //       } else if (!value || value.length === 0) {
  //         callback(new Error('请选择接收者'))
  //       } else {
  //         callback()
  //       }
  //     },
  //     trigger: 'change',
  //   },
  // ],
}

// 表格引用
const messageTable = ref()
// 当前发送的用户类型
const currentSendType = ref('user')

const pollingTime = ref(5*1000)
const longPollingTime = ref(60 * 1000)
const startOperationTime = ref(0)
const stopOperationTime = ref(0)
const pollingTimer = ref()

const startPollingTime = () => {
  if (pollingTimer.value) {
    clearTimeout(pollingTimer.value)
  }
  console.log('用户无操作的时间：', (stopOperationTime.value - startOperationTime.value) / 1000)
  if ((stopOperationTime.value - startOperationTime.value) / 1000 >= 300) {
    pollingTime.value = longPollingTime.value
  }
  pollingTimer.value = setTimeout(() => {
    stopOperationTime.value = Date.now()
    loadMessageList()
    startPollingTime()
  }, pollingTime.value)
}
const resetStopOperation = throttle(() => {
  startOperationTime.value = Date.now()
  if (pollingTime.value == longPollingTime.value) {
    pollingTime.value = 5000
    if (pollingTimer.value) {
      clearTimeout(pollingTimer.value)
    }
    loadMessageList()
    startPollingTime()
  }
}, 2000)

// 根据标签和搜索关键词过滤消息列表
const filteredMessages = computed(() => {

  let filtered = messageList.value
  // 根据视图模式过滤
  if (viewMode.value === 'unread') {
    activeTab.value='unread'
    filtered = filtered.filter((message) => !message.isRead)
  }
  // 根据标签过滤消息类型
  if (activeTab.value === 'all') {
    filtered = messageList.value
  }
  if (activeTab.value === 'announcement') {
    filtered = filtered.filter((message) => message.msgType === '系统公告')
  }
  if (activeTab.value === 'merchant') {
    filtered = filtered.filter((message) => message.msgType === '商家消息')
  }
  // 根据关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(
      (message) =>
        message.title.toLowerCase().includes(keyword) ||
        message.content.toLowerCase().includes(keyword),
    )
  }
  return filtered
})

// 获取消息类型标签样式
const getMessageTypeTag = (type: any) => {
  switch (type) {
    case 'announcement':
      return 'success'
    case 'merchant':
      return 'primary'
    case 'user':
      return 'warning'
    default:
      return 'info'
  }
}

// 获取搜索时的指定用户
const handleReceiverChange = (user: any) => {
  // nextTick(() => {
  //   // userSelectRef.value?.blur()
  // })
  sendMessageForm.value.receiver = user
  console.log('当前搜索的用户', sendMessageForm.value.receiver)
}

const handleMessageTypeChange = (data: any) => {
  if (data === '用户消息') currentSendType.value = 'user'
  else currentSendType.value = 'merchant'
}
// 获取消息类型名称
// const getMessageTypeName = (type: any) => {
//   switch (type) {
//     case 'announcement':
//       return '系统公告'
//     case 'merchant':
//       return '商家消息'
//     default:
//       return '未知类型'
//   }
// }

// 加载消息列表
const loadMessageList = async () => {
  loading.value = true

  try {
    console.log('userinfo为：', userInfo)
    const data = {
      type: 'merchant',
      id: userInfo.id,
    }
    console.log('获取用户信息列表参数：', data)

    const res = await getMessageList(data)
    messageList.value = res.data.list
    if (res.code === 1) {
      messageList.value = res.data.list || []
      totalCount.value = res.data.total || 0
    } else {
      ElMessage.error(res.msg || '获取消息列表失败')
    }
    // 更新未读消息数量
    loadUnreadCount()
  } catch (error) {
    console.error('获取消息列表失败:', error)
    ElMessage.error('获取消息列表失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 获取未读消息数量
const loadUnreadCount = async () => {
  try {
    const data = {
      type: 'merchant',
      id: userInfo.id,
    }
    const res = await getUnreadCount(data)
    if (res.code === 1) {
      unreadCount.value = res.data || 0
    }
  } catch (error) {
    console.error('获取未读消息数量失败:', error)
  }
}

// 查看消息详情
const viewMessageDetail = async (id: string) => {
  console.log('当前查看的消息：', id)

  messageDetailDialogVisible.value = true
  loadingDetail.value = true
  try {
    const res = await getMessageDetail(id, {
      type: 'merchant',
      id: userInfo.id,
    })
    if (res.code === 1) {
      currentMessage.value = res.data
      markMessageAsRead(id, false)
      loadMessageList()
    } else {
      ElMessage.error(res.msg || '获取消息详情失败')
    }
  } catch (error) {
    console.error('获取消息详情失败:', error)
    ElMessage.error('获取消息详情失败，请稍后重试')
    // 提供测试数据
    currentMessage.value = messageList.value.find((item) => item.msgID === id)
  } finally {
    loadingDetail.value = false
  }
}

// 标记消息为已读
const markMessageAsRead = async (id: string, loadRequired: boolean = true) => {
  try {
    const res = await markAsRead(id, {
      type: 'merchant',
      id: userInfo.id,
    })
    if (res.code === 1) {
      ElMessage.success('已标记为已读')

      if (loadRequired) loadMessageList()
    } else {
      ElMessage.error(res.msg || '标记已读失败')
    }
  } catch (error) {
    console.error('标记已读失败:', error)
    ElMessage.error('标记已读失败，请稍后重试')

    // // 模拟操作成功
    // const index = messageList.value.findIndex((item) => item.id === id)
    // if (index !== -1) {
    //   messageList.value[index].read = true
    //   unreadCount.value = Math.max(0, unreadCount.value - 1)
    //   ElMessage.success('已标记为已读')
    // }
  }
}



// 删除消息
const handelDeleteMessage = async (id: string) => {
  ElMessageBox.confirm('确定要删除该消息吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        console.log('id：', id)

        const res = await deleteMessage(id, {
          type: 'merchant',
          id: userInfo.id,
        })
        if (res.code === 1) {
          ElMessage.success('删除成功')
          loadMessageList()
        } else {
          ElMessage.error(res.msg || '删除失败')
        }
      } catch (error) {
        console.error('删除消息失败:', error)
        ElMessage.error('删除消息失败，请稍后重试')
      }
    })
    .catch(() => {
      // 取消删除
    })
}

// 表格选择变化
const handleSelectionChange = (selection) => {
  selectedMessages.value = selection
}

// 批量标记已读
const handleBatchMarkAsRead = async () => {
  const unreadMessages = selectedMessages.value.filter((item) => !item.isRead)
  if (unreadMessages.length === 0) {
    ElMessage.info('没有未读消息需要标记')
    return
  }

  const ids = unreadMessages.map((item) => item.msgID)
  try {
    const res = await batchMarkAsRead({ msgIDs: ids, type: 'merchant', id: userInfo.id })
    if (res.code === 1) {
      ElMessage.success('批量标记已读成功')

      loadMessageList()
    } else {
      ElMessage.error(res.msg || '批量标记已读失败')
    }
  } catch (error) {
    console.error('批量标记已读失败:', error)
    ElMessage.error('批量标记已读失败，请稍后重试')

    // 模拟操作成功
    // unreadMessages.forEach((message) => {
    //   const index = messageList.value.findIndex((item) => item.msgID === message.msgID)
    //   if (index !== -1) {
    //     messageList.value[index].isRead= true
    //   }
    // })
    unreadCount.value = Math.max(0, unreadCount.value - unreadMessages.length)
    ElMessage.success('批量标记已读成功')
  }
}
// 全部标记为已读
const handleAllMarkAsRead = async () => {
  const unreadMessages = messageList.value.filter((item) => !item.isRead)
  if (unreadMessages.length === 0) {
    ElMessage.info('没有未读消息需要标记')
    return
  }

  try {
    const res = await markAllAsRead({ type: 'merchant', id: userInfo.id })
    if (res.code === 1) {
      ElMessage.success('全部标记已读成功')
      loadMessageList()
    } else {
      ElMessage.error(res.msg || '全部标记已读失败')
    }
  } catch (error) {
    ElMessage.error('全部标记已读失败，请稍后重试')
    unreadCount.value = Math.max(0, unreadCount.value - unreadMessages.length)
    ElMessage.success('全部标记已读成功')
  }
}

// 批量删除
const handleBatchDelete = () => {
  if (selectedMessages.value.length === 0) {
    ElMessage.warning('请选择要删除的消息')
    return
  }

  ElMessageBox.confirm(`确定要删除选中的 ${selectedMessages.value.length} 条消息吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      const ids = selectedMessages.value.map((item) => item.msgID)
      try {
        const res = await batchDeleteMessages({ msgIDs: ids, type: 'merchant', id: userInfo.id })
        if (res.code === 1) {
          ElMessage.success('批量删除成功')
          loadMessageList()
        } else {
          ElMessage.error(res.msg || '批量删除失败')
        }
      } catch (error) {
        console.error('批量删除失败:', error)
        ElMessage.error('批量删除失败，请稍后重试')
      }
    })
    .catch(() => {
      // 取消删除
    })
}

// 处理标签页变更
const handleTabChange = (data: any) => {
  console.log('当前标签页信息：', data.props.name)
  activeTab.value = data.props.name
  currentPage.value = 1
  loadMessageList()
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
  // 由于使用的是计算属性进行过滤，这里不需要重新加载数据
}

// 处理分页大小变更
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadMessageList()
}

// 处理当前页变更
const handleCurrentChange = (page) => {
  currentPage.value = page
  loadMessageList()
}

// 显示发送消息对话框
const showSendMessageDialog = () => {
  sendMessageForm.value = {
    type: '',
    title: '',
    content: '',
    receiver: null,
    sendToAllUsers: false,
    sendToAllMerchants: false,
  }
  
  // 清空选项列表
  merchantOption.value = []
  userOption.value = []
  
  sendMessageDialogVisible.value = true
}
// 商家选择处理
const handleMerchantSelect = (user: any) => {
  console.log(user)
  sendMessageForm.value.receiver = {
    type: 'merchant',
    id: user.id,
  }
}
// 搜索商家
const remoteSearchMerchants = async (query: string) => {
  if (query) {
    searchingReceivers.value = true
    try {
      const data = {
        type: 'merchant',
        keyword: query,
      }
      const res = await getRecipientList(data)
      // currentUser.value={type:'merchant',id:}

      if (res.code === 1) {
        merchantOption.value = res.data || []
        console.log('商家列表：', merchantOption.value)
      } else {
        merchantOption.value = []
      }
    } catch (error) {
      console.error('搜索商家失败:', error)

      // 提供测试数据
      merchantOption.value = [
        { id: 1, name: '优品服饰专营店', phone: '13800138001' },
        { id: 2, name: '美妆世界旗舰店', phone: '13900139002' },
        { id: 3, name: '电子科技专卖店', phone: '13700137003' },
      ].filter((item) => item.name.includes(query))
    } finally {
      searchingReceivers.value = false
    }
  } else {
    merchantOption.value = []
  }
}

// 搜索用户
const remoteSearchUsers = async (query: string) => {
  if (query) {
    searchingReceivers.value = true
    try {
      const res = await getRecipientList({
        type: 'user',
        keyword: query,
      })
      if (res.code === 1) {
        userOption.value = res.data || []
      } else {
        userOption.value = []
      }
    } catch (error) {
      console.error('搜索用户失败:', error)

      // 提供测试数据
      userOption.value = [
        { id: 1, name: '张三', phone: '13511351111' },
        { id: 2, name: '李四', phone: '13522352222' },
        { id: 3, name: '王五', phone: '13533353333' },
        { id: 4, name: '李华', phone: '13533353333' },
      ].filter((item) => item.name.includes(query))
    } finally {
      searchingReceivers.value = false
    }
  } else {
    userOption.value = []
  }
}
function formatDateTime(date: Date) {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 发送消息
const submitSendMessage = async () => {
  if (!sendMessageFormRef.value) return

  await sendMessageFormRef.value.validate(async (valid) => {
    if (valid) {
      console.log('发送的类型：', sendMessageForm.value.type)
      sending.value = true
      try {
        let res
        // 根据消息类型调用不同API
        if (sendMessageForm.value.type === '系统公告') {
          const data = {
            title: sendMessageForm.value.title,
            content: sendMessageForm.value.content,
            sendTime: formatDate(new Date()).toString(),
            senderID: userInfo.id,
            type: '系统管理员',
          }
          // 系统公告
          res = await sendAnnouncement(data)
        } else {
          // 构建请求数据
          console.log('商家发送用户消息', userInfo)

          const data = {
            msgType: sendMessageForm.value.type as '商家消息' | '用户消息',
            title: sendMessageForm.value.title,
            recipient: {
              type: sendMessageForm.value.receiver.type,
              id: sendMessageForm.value.receiver.id,
            },
            content: sendMessageForm.value.content,
            sendTime: formatDate(new Date()).toString(),
            senderID: userInfo.id,
            type:
              userStore.userInfo.role === '超级管理员' || userStore.userInfo.role === '管理员'
                ? '系统管理员'
                : userInfo.accountName,
            sendToAllUsers: sendMessageForm.value.sendToAllUsers,
            sendToAllMerchants: sendMessageForm.value.sendToAllMerchants,
          }
          // 向指定用户或者商家发送站内信
          res = await sendMessage(data)
        }

        if (res.code === 1) {
          ElMessage.success('消息发送成功')
          sendMessageDialogVisible.value = false
          sendMessageForm.value.receiver={type: '',id: 0,}
          loadMessageList() // 重新加载消息列表
        } else {
          ElMessage.error(res.msg || '消息发送失败')
        }
      } catch (error) {
        console.error('发送消息失败:', error)
        ElMessage.error('发送消息失败，请稍后重试')

        // 模拟发送成功
        ElMessage.success('消息发送成功')
        sendMessageDialogVisible.value = false
      } finally {
        sending.value = false
      }
    }
  })
}

// 获取当前登录用户的身份信息
const getUser = async () => {
  const res = await getSellerById(JSON.parse(localStorage.getItem('user_info') as 'string').id)
  // console.log('用户信息测试：',res);
  userInfo = res.data
  console.log('当前登录的用户信息：', userInfo)
}

// 监听视图模式变化
watch(viewMode, () => {
  currentPage.value = 1
  loadMessageList()
})

// 监听发送对象类型变化，在选择不同类型时清空接收者列表
watch(
  () => sendMessageForm.value.type,
  () => {
    sendMessageForm.value.receiver = { type: '', id: -1 }
    sendMessageForm.value.sendToAllUsers = false
    sendMessageForm.value.sendToAllMerchants = false
  },
)

// 监听全部发送勾选变化，勾选时清空接收者列表
watch(
  () => sendMessageForm.value.sendToAllUsers,
  (val) => {
    if (val) {
      sendMessageForm.value.receiver = { type: '', id: -1 }
    }
  },
)
watch(
  () => sendMessageForm.value.sendToAllMerchants,
  (val) => {
    if (val) {
      sendMessageForm.value.receiver = { type: '', id: -1 }
    }
  },
)

// 初始化
onMounted(async () => {
  
  await getUser()
  loadMessageList()
  startPollingTime()
  startOperationTime.value = Date.now()
  window.addEventListener('mousemove', resetStopOperation)
  window.addEventListener('mousedown', resetStopOperation)
  window.addEventListener('keydown', resetStopOperation)
})
onUnmounted(() => {
  window.removeEventListener('mousemove', resetStopOperation)
  window.removeEventListener('mousedown', resetStopOperation)
  window.removeEventListener('keydown', resetStopOperation)
  if (pollingTimer.value) {
    clearTimeout(pollingTimer.value)
  }
})
</script>

<style scoped lang="scss">
.message-list-container {
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #eef2f5 100%);
  border-radius: 12px;
  min-height: calc(100vh - 40px);

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    .header-left {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        margin: 0 0 8px 0;
        background: linear-gradient(120deg, #3a7bd5, #2c5499);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .page-subtitle {
        font-size: 14px;
        color: #606266;
      }
    }

    .header-right {
      display: flex;
      gap: 16px;
    }
  }

  .toolbar {
    display: flex;
    justify-content: space-between;
    margin: 20px 0;

    .search-box {
      width: 300px;
    }

    .action-box {
      display: flex;
      gap: 12px;
    }
  }

  .message-list-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    margin-bottom: 20px;

    &:hover {
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .message-title {
      font-weight: normal;

      &.is-unread {
        font-weight: bold;
      }
    }

    .pagination-container {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }

  .unread-badge {
    margin-left: 8px;
  }

  :deep(.el-tabs__item) {
    font-size: 16px;
  }

  // 消息详情样式
  .message-detail-header {
    margin-bottom: 20px;

    .message-type {
      margin-bottom: 8px;
    }

    .message-meta {
      display: flex;
      justify-content: space-between;
      font-size: 14px;
      color: #909399;
    }
  }

  .message-detail-content {
    padding: 20px;
    background-color: #f7f9fc;
    border-radius: 8px;
    font-size: 14px;
    line-height: 1.6;
    color: #303133;
  }

  // 接收者选择框
  .receiver-option {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .receiver-info {
      font-size: 12px;
      color: #909399;
    }
  }

  .form-tip {
    margin-top: 8px;
    font-size: 12px;
    color: #909399;
  }
}

@media screen and (max-width: 768px) {
  .message-list-container {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-right {
        width: 100%;
        flex-direction: column;
      }
    }

    .toolbar {
      flex-direction: column;
      gap: 16px;

      .search-box {
        width: 100%;
      }

      .action-box {
        width: 100%;
        justify-content: space-between;
      }
    }

    .message-meta {
      flex-direction: column;
      gap: 4px;
    }
  }
}
</style>
