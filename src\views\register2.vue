<template>
  <div class="register-container">
    <div class="register-content">
      <div class="register-box">
        <div class="register-header">
          <h1 class="register-title">sharewharf <span>商城</span></h1>
          <p class="register-subtitle">请完善您的商户信息</p>
        </div>

        <div class="step-form">
          <el-form ref="formRef" :model="form" :rules="rules" label-position="top" @submit.prevent>
            <!-- 店铺/商户名称 -->
            <el-form-item label="店铺/商户名称" prop="shopName">
              <el-input
                v-model="form.shopName"
                placeholder="请输入店铺或商户名称"
                :prefix-icon="ShoppingBag"
              />
            </el-form-item>

            <!-- 公司全称 -->
            <el-form-item label="公司全称" prop="companyName">
              <el-input
                v-model="form.companyName"
                placeholder="请输入公司全称"
                :prefix-icon="OfficeBuilding"
              />
            </el-form-item>

            <!-- 营业执照注册编号 -->
            <el-form-item label="统一社会信用代码" prop="businessLicense">
              <div class="credit-code-container">
                <el-input
                  v-model="form.businessLicense"
                  placeholder="请输入18位统一社会信用代码（不含I、O、Z、S、V字母）"
                  :prefix-icon="Document"
                />
                <el-tooltip
                  effect="light"
                  content="统一社会信用代码为18位，由阿拉伯数字或大写英文字母组成，但不能包含I、O、Z、S、V这五个字母。第1位为登记管理部门代码、第2位为机构类别代码、第3-8位为登记管理机关行政区划码、第9-17位为组织机构代码、第18位为校验码。"
                  placement="top"
                  :show-after="300"
                >
                  <el-icon class="info-icon"><InfoFilled /></el-icon>
                </el-tooltip>
              </div>
            </el-form-item>

            <!-- 身份证号码 -->
            <el-form-item label="身份证号码" prop="idCardNumber">
              <el-input
                v-model="form.idCardNumber"
                placeholder="请输入身份证号码"
                :prefix-icon="CreditCard"
              />
            </el-form-item>

            <!-- 营业执照有效期截止日期 -->
            <el-form-item label="统一社会信用代码有效期截止日期" prop="licenseValidity">
              <div class="license-validity-container">
                <el-date-picker
                  v-model="form.licenseValidity"
                  type="date"
                  placeholder="请选择有效期截止日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                  :disabled="form.isLongTermValidity"
                />
                <div class="long-term-option">
                  <el-checkbox v-model="form.isLongTermValidity" @change="handleLongTermChange">
                    长期有效
                  </el-checkbox>
                  <span class="tip-text">（执照有效期大于10年可选择长期有效）</span>
                </div>
              </div>
            </el-form-item>

            <!-- 企业描述/公司简介 -->
            <el-form-item label="企业描述/公司简介" prop="companyIntro">
              <el-input
                v-model="form.companyIntro"
                type="textarea"
                :rows="3"
                placeholder="请简要描述您的企业或公司"
                resize="none"
              />
            </el-form-item>

            <!-- 业务联系人姓名 -->
            <el-form-item label="业务联系人姓名" prop="contactPerson">
              <el-input
                v-model="form.contactPerson"
                placeholder="请输入业务联系人姓名"
                :prefix-icon="User"
              />
            </el-form-item>

            <!-- 联系人电话号码 -->
            <el-form-item label="联系人电话号码" prop="contactPhone">
              <el-input
                v-model="form.contactPhone"
                placeholder="请输入联系人电话号码"
                :prefix-icon="Phone"
              />
            </el-form-item>

            <!-- 地址信息 -->
            <div class="address-section">
              <h3 class="section-title">地址信息</h3>

              <div class="address-row">
                <!-- 省份 -->
                <el-form-item label="省份" prop="province" class="address-item">
                  <el-input
                    v-model="form.province"
                    placeholder="请输入省份"
                    :prefix-icon="Location"
                  />
                </el-form-item>

                <!-- 城市 -->
                <el-form-item label="城市" prop="city" class="address-item">
                  <el-input v-model="form.city" placeholder="请输入城市" :prefix-icon="Location" />
                </el-form-item>

                <!-- 区/县 -->
                <el-form-item label="区/县" prop="district" class="address-item">
                  <el-input
                    v-model="form.district"
                    placeholder="请输入区/县"
                    :prefix-icon="Location"
                  />
                </el-form-item>
              </div>

              <!-- 详细地址 -->
              <el-form-item label="详细地址" prop="addressDetail">
                <el-input
                  v-model="form.addressDetail"
                  placeholder="请输入详细地址（街道、门牌号等）"
                  :prefix-icon="Location"
                />
              </el-form-item>
            </div>

            <!-- 图片上传区域 -->
            <div class="upload-section">
              <h3 class="section-title">资质证明</h3>

              <!-- 营业执照图片上传 -->
              <el-form-item label="营业执照图片" prop="businessLicenseImage">
                <el-upload
                  class="upload-container"
                  action="#"
                  :auto-upload="false"
                  :limit="1"
                  :on-change="handleLicenseImageChange"
                  :on-remove="handleLicenseImageRemove"
                  :file-list="licenseFileList"
                  list-type="picture-card"
                  :show-upload-button="!form.businessLicenseImage"
                >
                  <el-icon><Plus /></el-icon>
                  <template #tip>
                    <div class="upload-tip">
                      <template v-if="!form.businessLicenseImage">
                        请上传<span class="highlight-text">一张</span
                        >清晰的营业执照扫描件或照片（需包含统一社会信用代码），支持JPG/PNG格式，大小不超过5MB
                      </template>
                      <template v-else>
                        <span class="success-tip"
                          ><el-icon><Check /></el-icon> 已上传营业执照图片</span
                        >
                      </template>
                    </div>
                  </template>
                </el-upload>
              </el-form-item>

              <!-- 身份证正面照片上传 -->
              <el-form-item label="身份证正面照片" prop="idCardFrontImage">
                <el-upload
                  class="upload-container"
                  action="#"
                  :auto-upload="false"
                  :limit="1"
                  :on-change="handleIdCardFrontChange"
                  :on-remove="handleIdCardFrontRemove"
                  :file-list="idCardFrontFileList"
                  list-type="picture-card"
                  :show-upload-button="!form.idCardFrontImage"
                >
                  <el-icon><Plus /></el-icon>
                  <template #tip>
                    <div class="upload-tip">
                      <template v-if="!form.idCardFrontImage">
                        请上传<span class="highlight-text">一张</span
                        >清晰的身份证正面照片，支持JPG/PNG格式，大小不超过5MB
                      </template>
                      <template v-else>
                        <span class="success-tip"
                          ><el-icon><Check /></el-icon> 已上传身份证正面照片</span
                        >
                      </template>
                    </div>
                  </template>
                </el-upload>
              </el-form-item>

              <!-- 身份证背面照片上传 -->
              <el-form-item label="身份证背面照片" prop="idCardBackImage">
                <el-upload
                  class="upload-container"
                  action="#"
                  :auto-upload="false"
                  :limit="1"
                  :on-change="handleIdCardBackChange"
                  :on-remove="handleIdCardBackRemove"
                  :file-list="idCardBackFileList"
                  list-type="picture-card"
                  :show-upload-button="!form.idCardBackImage"
                >
                  <el-icon><Plus /></el-icon>
                  <template #tip>
                    <div class="upload-tip">
                      <template v-if="!form.idCardBackImage">
                        请上传<span class="highlight-text">一张</span
                        >清晰的身份证背面照片，支持JPG/PNG格式，大小不超过5MB
                      </template>
                      <template v-else>
                        <span class="success-tip"
                          ><el-icon><Check /></el-icon> 已上传身份证背面照片</span
                        >
                      </template>
                    </div>
                  </template>
                </el-upload>
              </el-form-item>

              <!-- 发货仓库图片上传 -->
              <el-form-item label="发货仓库图片" prop="warehouseImage">
                <el-upload
                  class="upload-container"
                  action="#"
                  :auto-upload="false"
                  :limit="3"
                  :on-exceed="handleExceed"
                  :on-change="handleWarehouseImageChange"
                  :on-remove="handleWarehouseImageRemove"
                  :file-list="warehouseFileList"
                  list-type="picture-card"
                  multiple
                >
                  <el-icon><Plus /></el-icon>
                  <template #tip>
                    <div class="upload-tip">
                      请上传发货仓库照片，<span class="highlight-text">必须上传3张</span
                      >，支持JPG/PNG格式，大小不超过5MB/张
                    </div>
                  </template>
                </el-upload>
              </el-form-item>
            </div>

            <!-- 同意协议 -->
            <el-form-item prop="agreement">
              <el-checkbox v-model="form.agreement">
                我已阅读并同意
                <el-link type="primary" :underline="false" @click="showAgreement"
                  >《用户协议》</el-link
                >
                和
                <el-link type="primary" :underline="false" @click="showPrivacy"
                  >《隐私政策》</el-link
                >
              </el-checkbox>
            </el-form-item>

            <!-- 按钮组 -->
            <el-form-item class="form-buttons">
              <el-button @click="prevStep">返回上一步</el-button>
              <el-button type="primary" class="submit-btn" @click="submitForm" :loading="loading">
                完成注册
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 协议对话框 -->
        <el-dialog v-model="dialogVisible" :title="dialogTitle" width="60%" destroy-on-close>
          <div class="agreement-content">
            <p v-if="dialogType === 'agreement'">
              欢迎您使用我们的服务。本协议是您与我们之间关于使用我们服务的法律协议。通过注册或使用我们的服务，您确认您已阅读、理解并同意受本协议的约束。
            </p>
            <p v-if="dialogType === 'privacy'">
              我们非常重视您的隐私保护。本隐私政策描述了我们如何收集、使用、存储和共享您的个人信息，以及您对这些信息的控制权。
            </p>
            <div v-if="dialogType === 'agreement'" class="agreement-details">
              <h3>1. 服务内容</h3>
              <p>我们提供电子商务平台服务，允许用户浏览、购买商品及服务。</p>

              <h3>2. 用户账户</h3>
              <p>您需要创建账户才能使用我们的某些服务。您有责任维护您的账户安全。</p>

              <h3>3. 用户行为</h3>
              <p>您同意不会使用我们的服务进行任何违法或未经授权的活动。</p>
            </div>
            <div v-if="dialogType === 'privacy'" class="agreement-details">
              <h3>1. 信息收集</h3>
              <p>我们收集您提供给我们的信息，如注册信息、交易信息等。</p>

              <h3>2. 信息使用</h3>
              <p>我们使用收集的信息来提供、维护和改进我们的服务。</p>

              <h3>3. 信息共享</h3>
              <p>除非本政策另有规定，我们不会与第三方共享您的个人信息。</p>
            </div>
          </div>
          <template #footer>
            <span class="dialog-footer">
              <el-button @click="dialogVisible = false">关闭</el-button>
              <el-button type="primary" @click="agreeTerms"> 同意 </el-button>
            </span>
          </template>
        </el-dialog>
      </div>

      <!-- 装饰元素 -->
      <div class="decoration-circles">
        <div class="circle circle-1"></div>
        <div class="circle circle-2"></div>
        <div class="circle circle-3"></div>
      </div>
    </div>
  </div>

  <!-- 注册成功提示弹窗 -->
  <RegisterSuccessDialog :visible="successDialogVisible" @confirm="handleSuccessConfirm" />
</template>

<script setup lang="ts">
import { register } from '@/api/seller'
import RegisterSuccessDialog from '@/components/RegisterSuccessDialog.vue'
import { base64ToBlob, processImageForUpload } from '@/utils/imageHelpers'
import {
  Check,
  CreditCard,
  Document,
  InfoFilled,
  Location,
  OfficeBuilding,
  Phone,
  Plus,
  ShoppingBag,
  User,
} from '@element-plus/icons-vue'
import type { FormInstance, FormRules, UploadFile, UploadUserFile } from 'element-plus'
import { ElMessage } from 'element-plus'
import { onMounted, onUnmounted, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const formRef = ref<FormInstance>()
const dialogVisible = ref(false)
const dialogTitle = ref('')
const dialogType = ref('')
const loading = ref(false)
const successDialogVisible = ref(false)

// 文件列表
const licenseFileList = ref<UploadUserFile[]>([])
const warehouseFileList = ref<UploadUserFile[]>([])
const idCardFrontFileList = ref<UploadUserFile[]>([])
const idCardBackFileList = ref<UploadUserFile[]>([])

// 表单数据
const form = reactive({
  shopName: '',
  companyName: '',
  businessLicense: '',
  licenseValidity: '' as string | Date,
  isLongTermValidity: false,
  businessLicenseImage: null as UploadFile | null,
  companyIntro: '',
  contactPerson: '',
  contactPhone: '',
  province: '',
  city: '',
  district: '',
  addressDetail: '',
  warehouseImage: [] as UploadFile[],
  idCardNumber: '',
  idCardFrontImage: null as UploadFile | null,
  idCardBackImage: null as UploadFile | null,
  agreement: false,
  gender: '未知',
})

// 省份选项（示例数据）
// const provinceOptions = [
//   { value: '北京市', label: '北京市' },
//   { value: '上海市', label: '上海市' },
//   { value: '广东省', label: '广东省' },
//   { value: '江苏省', label: '江苏省' },
//   { value: '浙江省', label: '浙江省' },
//   { value: '四川省', label: '四川省' },
// ]

// 城市选项（示例数据，实际应根据选择的省份动态加载）
// const cityOptions = [
//   { value: '北京市', label: '北京市' },
//   { value: '上海市', label: '上海市' },
//   { value: '广州市', label: '广州市' },
//   { value: '深圳市', label: '深圳市' },
//   { value: '杭州市', label: '杭州市' },
//   { value: '南京市', label: '南京市' },
//   { value: '成都市', label: '成都市' },
// ]

// 区县选项（示例数据，实际应根据选择的城市动态加载）
// const districtOptions = [
//   { value: '朝阳区', label: '朝阳区' },
//   { value: '海淀区', label: '海淀区' },
//   { value: '浦东新区', label: '浦东新区' },
//   { value: '天河区', label: '天河区' },
//   { value: '福田区', label: '福田区' },
//   { value: '西湖区', label: '西湖区' },
// ]

// 验证规则
const validatePhone = (rule: any, value: string, callback: any) => {
  // 简单的电话号码验证，可根据需要调整
  const phoneRegex = /^1[3-9]\d{9}$/
  if (value === '') {
    callback(new Error('请输入电话号码'))
  } else if (!phoneRegex.test(value)) {
    callback(new Error('请输入有效的手机号码'))
  } else {
    callback()
  }
}

// 验证营业执照图片
const validateLicenseImage = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback(new Error('请上传营业执照图片'))
  } else {
    callback()
  }
}

// 验证仓库图片
const validateWarehouseImages = (rule: any, value: string[], callback: any) => {
  if (!value || value.length === 0) {
    callback(new Error('请上传发货仓库图片'))
  } else if (value.length < 3) {
    callback(new Error('必须上传3张发货仓库图片'))
  } else if (value.length > 3) {
    callback(new Error('发货仓库图片不能超过3张'))
  } else {
    callback()
  }
}

// 验证身份证号码
const validateIdCard = (rule: any, value: string, callback: any) => {
  // 简单的身份证号码验证，支持15位和18位
  const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
  if (value === '') {
    callback(new Error('请输入身份证号码'))
  } else if (!idCardRegex.test(value)) {
    callback(new Error('请输入有效的身份证号码'))
  } else {
    callback()
  }
}

// 验证身份证正面照片
const validateIdCardFront = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback(new Error('请上传身份证正面照片'))
  } else {
    callback()
  }
}

// 验证身份证背面照片
const validateIdCardBack = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback(new Error('请上传身份证背面照片'))
  } else {
    callback()
  }
}

const validateLicenseValidity = (rule: any, value: string, callback: any) => {
  if (form.isLongTermValidity) {
    // 长期有效已自动设置为10年后的日期，无需额外验证
    callback()
  } else if (!value) {
    callback(new Error('请选择统一社会信用代码有效期截止日期'))
  } else {
    // 如果用户手动选择了日期，确保日期是有效的
    try {
      const selectedDate = new Date(value)
      const today = new Date()

      if (selectedDate <= today) {
        callback(new Error('有效期截止日期必须晚于今天'))
      } else {
        callback()
      }
    } catch (e) {
      callback(new Error('请选择有效的日期格式'))
    }
  }
}

const rules = reactive<FormRules>({
  shopName: [
    { required: true, message: '请输入店铺/商户名称', trigger: 'blur' },
    { min: 2, max: 50, message: '店铺名称长度在2-50个字符之间', trigger: 'blur' },
  ],
  companyName: [
    { required: true, message: '请输入公司全称', trigger: 'blur' },
    { min: 2, max: 100, message: '公司全称长度在2-100个字符之间', trigger: 'blur' },
  ],
  businessLicense: [
    { required: true, message: '请输入统一社会信用代码', trigger: 'blur' },
    { len: 18, message: '统一社会信用代码必须为18位', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (!value) {
          callback()
          return
        }

        // 检查是否只包含允许的字符（阿拉伯数字或大写英文字母，不含I、O、Z、S、V）
        if (!/^[0-9A-HJ-NP-RT-UW-Y]{18}$/.test(value)) {
          callback(
            new Error('统一社会信用代码必须由阿拉伯数字或大写英文字母组成，不能使用I、O、Z、S、V'),
          )
          return
        }

        callback()
      },
      trigger: 'blur',
    },
  ],
  licenseValidity: [{ validator: validateLicenseValidity, trigger: 'change' }],
  businessLicenseImage: [{ required: true, validator: validateLicenseImage, trigger: 'change' }],
  companyIntro: [{ max: 500, message: '公司简介不能超过500个字符', trigger: 'blur' }],
  contactPerson: [
    { required: true, message: '请输入业务联系人姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '联系人姓名长度在2-20个字符之间', trigger: 'blur' },
  ],
  contactPhone: [{ required: true, validator: validatePhone, trigger: 'blur' }],
  province: [
    { required: true, message: '请输入省份', trigger: 'blur' },
    { max: 20, message: '省份名称不能超过20个字符', trigger: 'blur' },
  ],
  city: [
    { required: true, message: '请输入城市', trigger: 'blur' },
    { max: 20, message: '城市名称不能超过20个字符', trigger: 'blur' },
  ],
  district: [
    { required: true, message: '请输入区/县', trigger: 'blur' },
    { max: 20, message: '区/县名称不能超过20个字符', trigger: 'blur' },
  ],
  addressDetail: [
    { required: true, message: '请输入详细地址', trigger: 'blur' },
    { max: 100, message: '详细地址不能超过100个字符', trigger: 'blur' },
  ],
  warehouseImage: [{ required: true, validator: validateWarehouseImages, trigger: 'change' }],
  agreement: [
    {
      validator: (rule: any, value: boolean, callback: any) => {
        if (!value) {
          callback(new Error('请阅读并同意用户协议和隐私政策'))
        } else {
          callback()
        }
      },
      trigger: 'change',
    },
  ],
  idCardNumber: [{ required: true, validator: validateIdCard, trigger: 'blur' }],
  idCardFrontImage: [{ required: true, validator: validateIdCardFront, trigger: 'change' }],
  idCardBackImage: [{ required: true, validator: validateIdCardBack, trigger: 'change' }],
})

// 显示协议
const showAgreement = () => {
  dialogTitle.value = '用户协议'
  dialogType.value = 'agreement'
  dialogVisible.value = true
}

// 显示隐私政策
const showPrivacy = () => {
  dialogTitle.value = '隐私政策'
  dialogType.value = 'privacy'
  dialogVisible.value = true
}

// 同意条款
const agreeTerms = () => {
  form.agreement = true
  dialogVisible.value = false
}
// 序列化file对象
const serializeFile = async (file: File) => {
  return {
    name: file.name,
    lastModified: file.lastModified,
    size: file.size,
    type: file.type,
    data: await processImageForUpload(file),
  }
}

// 上一步
const prevStep = () => {
  // 保存数据到本地存储
  const saveData = async () => {
    // const warehouseImages = await Promise.all(
    //   form.warehouseImage.map(async (file) => ({
    //     uid: file.uid,
    //     name: file.name,
    //     size: file.size,
    //     base64: await processImageForUpload(file.raw),
    //   })),
    // )

    // const businessLicenseImage = form.businessLicenseImage
    //   ? {
    //       uid: form.businessLicenseImage.uid,
    //       name: form.businessLicenseImage.name,
    //       size: form.businessLicenseImage.size,
    //       base64: await processImageForUpload(form.businessLicenseImage.raw),
    //     }
    //   : null

    // const idCardFront = form.idCardFrontImage
    //   ? {
    //       uid: form.idCardFrontImage.uid,
    //       name: form.idCardFrontImage.name,
    //       size: form.idCardFrontImage.size,
    //       base64: await processImageForUpload(form.idCardFrontImage.raw),
    //     }
    //   : null

    // const idCardBack = form.idCardBackImage
    //   ? {
    //       uid: form.idCardBackImage.uid,
    //       name: form.idCardBackImage.name,
    //       size: form.idCardBackImage.size,
    //       base64: await processImageForUpload(form.idCardBackImage.raw),
    //     }
    //   : null

    localStorage.setItem(
      'registerMerchantInfo',
      JSON.stringify({
        ...form,
        // warehouseImages,
        // businessLicenseImage,
        // idCardFront,
        // idCardBack,
      }),
    )
  }

  saveData().then(() => {
    router.push('/register')
  })

  // localStorage.setItem(
  //   'registerMerchantInfo',
  //   JSON.stringify({
  //     ...form,

  //     // 由于文件对象无法直接序列化，保存文件信息供恢复使用
  //     warehouseFileInfo: form.warehouseImage.map(file => ({
  //       uid: file.uid,
  //       name: file.name,
  //       size: file.size,
  //       url: file.url || '',
  //     })),
  //     businessLicenseFileInfo: form.businessLicenseImage ? {
  //       uid: form.businessLicenseImage.uid,
  //       name: form.businessLicenseImage.name,
  //       size: form.businessLicenseImage.size,
  //       url: form.businessLicenseImage.url || '',
  //     } : null,
  //     idCardFrontFileInfo: form.idCardFrontImage ? {
  //       uid: form.idCardFrontImage.uid,
  //       name: form.idCardFrontImage.name,
  //       size: form.idCardFrontImage.size,
  //       url: form.idCardFrontImage.url || '',
  //     } : null,
  //     idCardBackFileInfo: form.idCardBackImage ? {
  //       uid: form.idCardBackImage.uid,
  //       name: form.idCardBackImage.name,
  //       size: form.idCardBackImage.size,
  //       url: form.idCardBackImage.url || '',
  //     } : null,
  //   })
  // );
  // 返回上一步
  // router.push('/register')
}

// 重置表单及所有上传状态
const resetForm = () => {
  // 重置表单数据
  form.shopName = ''
  form.companyName = ''
  form.businessLicense = ''
  form.licenseValidity = ''
  form.isLongTermValidity = false
  form.businessLicenseImage = null
  form.companyIntro = ''
  form.contactPerson = ''
  form.contactPhone = ''
  form.province = ''
  form.city = ''
  form.district = ''
  form.addressDetail = ''
  form.warehouseImage = []
  form.idCardNumber = ''
  form.idCardFrontImage = null
  form.idCardBackImage = null
  form.agreement = false
  form.gender = '未知'

  // 清空文件列表
  licenseFileList.value = []
  warehouseFileList.value = []
  idCardFrontFileList.value = []
  idCardBackFileList.value = []

  // 清除本地存储
  localStorage.removeItem('registerMerchantInfo')
}

// 提交表单前检查图片数量
const checkImagesBeforeSubmit = (): boolean => {
  if (!form.businessLicenseImage) {
    ElMessage.error('请上传营业执照图片')
    return false
  }

  if (!form.idCardFrontImage) {
    ElMessage.error('请上传身份证正面照片')
    return false
  }

  if (!form.idCardBackImage) {
    ElMessage.error('请上传身份证背面照片')
    return false
  }

  if (form.warehouseImage.length < 3) {
    ElMessage.error('必须上传3张发货仓库图片')
    return false
  }

  if (form.warehouseImage.length > 3) {
    ElMessage.error('发货仓库图片不能超过3张')
    return false
  }

  return true
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return

  // 先检查图片数量
  if (!checkImagesBeforeSubmit()) {
    return
  }

  await formRef.value.validate(async (valid, fields) => {
    if (valid) {
      loading.value = true

      // 获取第一步的账号信息
      const accountInfo = localStorage.getItem('registerAccountInfo')
      let accountData: any = {}

      if (accountInfo) {
        try {
          accountData = JSON.parse(accountInfo)
        } catch (e) {
          console.error('解析账号数据失败', e)
        }
      }

      // 检查是否有原始文件
      if (
        (form.warehouseImage.length > 0 && !form.warehouseImage[0].raw) ||
        (form.businessLicenseImage && !form.businessLicenseImage.raw) ||
        (form.idCardFrontImage && !form.idCardFrontImage.raw) ||
        (form.idCardBackImage && !form.idCardBackImage.raw)
      ) {
        ElMessage.error('请重新上传图片文件')
        loading.value = false
        return
      }

      // 准备FormData对象，用于发送包含文件的请求
      const formData = new FormData()

      // 创建与后端DTO对应的JSON字符串并转换为blob
      const sellerRegisterDTO = {
        email: accountData.email || '',
        verificationCode: accountData.verifyCode || '',
        password: accountData.password || '',
        shopName: form.shopName,
        companyName: form.companyName,
        businessLicense: form.businessLicense,
        // 注意：后端API要求licenseValidity必须是标准日期格式
        // 当用户选择"长期有效"时，我们使用2099-12-31作为固定的远期日期
        // 不能发送"长期有效"字符串，否则会导致后端解析错误
        licenseValidity:
          typeof form.licenseValidity === 'string'
            ? form.licenseValidity
            : form.licenseValidity instanceof Date
              ? form.licenseValidity.toISOString().split('T')[0]
              : '',
        companyIntro: form.companyIntro || '',
        contactPerson: form.contactPerson,
        contactPhone: form.contactPhone,
        province: form.province,
        city: form.city,
        district: form.district,
        addressDetail: form.addressDetail,
        accountName: accountData.accountName || '',
        phone: form.contactPhone, // 使用联系电话作为手机号
        gender: 'Male', // 默认性别
        accountStatus: '0', // 默认待审核状态
        IDCard: form.idCardNumber, // 添加身份证号字段
      }
      console.log('sellerRegisterDTO:', sellerRegisterDTO)
      // 创建与后端DTO对应的JSON字符串并转换为blob
      const sellerRegisterDTOBlob = new Blob([JSON.stringify(sellerRegisterDTO)], {
        type: 'application/json',
      })
      // 添加JSON数据到FormData
      formData.append('sellerRegisterDTO', sellerRegisterDTOBlob)
      // 添加营业执照图片
      if (form.businessLicenseImage && form.businessLicenseImage.raw instanceof Blob) {
        formData.append('license', form.businessLicenseImage.raw)
      } else {
        ElMessage.error('请上传营业执照')
        loading.value = false
        return
      }
      // 添加身份证正面照片
      if (form.idCardFrontImage && form.idCardFrontImage.raw instanceof Blob) {
        formData.append('IDCard1', form.idCardFrontImage.raw)
      } else {
        ElMessage.error('请上传身份证正面照片')
        loading.value = false
        return
      }
      // 添加身份证背面照片
      if (form.idCardBackImage && form.idCardBackImage.raw instanceof Blob) {
        formData.append('IDCard2', form.idCardBackImage.raw)
      } else {
        ElMessage.error('请上传身份证背面照片')
        loading.value = false
        return
      }
      // 添加仓库照片（必须要3张）
      const warehouseImagesCount = form.warehouseImage.length
      // 检查是否有足够的仓库图片
      if (warehouseImagesCount < 3) {
        ElMessage.error('必须上传3张发货仓库图片')
        loading.value = false
        return
      }

      // 添加第一张仓库图片
      if (form.warehouseImage[0].raw instanceof Blob) {
        formData.append('warehouseFiles1', form.warehouseImage[0].raw)
      } else {
        ElMessage.error('仓库图片1无效，请重新上传')
        loading.value = false
        return
      }

      // 添加第二张仓库图片
      if (form.warehouseImage[1].raw instanceof Blob) {
        formData.append('warehouseFiles2', form.warehouseImage[1].raw)
      } else {
        ElMessage.error('仓库图片2无效，请重新上传')
        loading.value = false
        return
      }

      // 添加第三张仓库图片
      if (form.warehouseImage[2].raw instanceof Blob) {
        formData.append('warehouseFiles3', form.warehouseImage[2].raw)
      } else {
        ElMessage.error('仓库图片3无效，请重新上传')
        loading.value = false
        return
      }

      // 调试信息
      console.log('发送注册请求，表单数据:', sellerRegisterDTO)
      console.log('上传文件数量:', {
        license: form.businessLicenseImage ? 1 : 0,
        idCardFront: form.idCardFrontImage ? 1 : 0,
        idCardBack: form.idCardBackImage ? 1 : 0,
        warehouseImages: form.warehouseImage.length,
      })

      try {
        console.log('传递的数据：', formData)

        // 使用封装的API接口
        const result = await register(formData)
        // const result = await register(sellerRegisterDTO)

        console.log('注册请求响应:', result)

        // 判断返回的状态码
        if (result.code === 1) {
          // 注册成功
          ElMessage.success('注册成功，平台将在24小时内完成审核')

          // 清除本地存储的注册数据
          localStorage.removeItem('registerAccountInfo')
          localStorage.removeItem('registerMerchantInfo')

          // 显示注册成功弹窗，而不是立即跳转
          successDialogVisible.value = true
        } else {
          // 注册失败，显示后端返回的错误消息
          ElMessage.error(result.msg || '注册失败')
          console.error('注册失败:', result)
        }
      } catch (error: any) {
        console.error('注册请求出错:', error)

        // 显示错误信息，优先使用响应中的错误信息
        if (error.response?.data?.msg) {
          ElMessage.error(error.response.data.msg)
        } else {
          ElMessage.error(error.message || '注册失败，请稍后重试')
        }

        /* if (import.meta.env.DEV) {
          console.log('开发环境：模拟注册成功流程')
          ElMessage({
            type: 'warning',
            message: '开发环境：模拟注册成功流程（实际请求失败）',
            duration: 3000,
          })

          // 延迟一秒后模拟成功
          setTimeout(() => {
            // 清除本地存储的注册数据
            localStorage.removeItem('registerAccountInfo')
            localStorage.removeItem('registerMerchantInfo')

            // 显示注册成功弹窗，而不是立即跳转
            successDialogVisible.value = true
          }, 1000)
        } else {
          ElMessage.error(error.message || '注册失败，请稍后重试')
        } */
      } finally {
        loading.value = false
      }
    } else {
      console.log('验证失败', fields)

      // 检查是否有图片相关的错误
      if (fields?.businessLicenseImage) {
        ElMessage.error('请上传营业执照图片')
      }

      if (fields?.idCardFrontImage) {
        ElMessage.error('请上传身份证正面照片')
      }

      if (fields?.idCardBackImage) {
        ElMessage.error('请上传身份证背面照片')
      }

      if (fields?.warehouseImage) {
        if (form.warehouseImage.length < 3) {
          ElMessage.error('必须上传3张发货仓库图片')
        } else if (form.warehouseImage.length > 3) {
          ElMessage.error('发货仓库图片不能超过3张')
        }
      }
    }
  })
}

// 处理营业执照图片上传
const handleLicenseImageChange = (file: UploadFile) => {
  console.log('传入图片：', file)

  console.log(JSON.stringify(file))

  // 检查文件类型
  const isImage = file.raw?.type === 'image/jpeg' || file.raw?.type === 'image/png'
  if (!isImage) {
    ElMessage.error('营业执照图片只能是JPG或PNG格式!')
    // 清空文件列表，确保不显示在页面上
    licenseFileList.value = licenseFileList.value.filter((item) => item.uid !== file.uid)
    return false
  }

  // 检查文件大小
  const isLt5M = file.size! / 1024 / 1024 < 5
  if (!isLt5M) {
    ElMessage.error('营业执照图片大小不能超过5MB!')
    // 清空文件列表，确保不显示在页面上
    licenseFileList.value = licenseFileList.value.filter((item) => item.uid !== file.uid)
    return false
  }

  // 更新表单数据
  form.businessLicenseImage = file
  ElMessage.success('营业执照图片上传成功，只需上传一张')
  return true
}

// 处理营业执照图片移除
const handleLicenseImageRemove = () => {
  form.businessLicenseImage = null
  ElMessage.warning('已移除营业执照图片，请重新上传')
}

// 处理身份证正面照片上传
const handleIdCardFrontChange = (file: UploadFile) => {
  // 检查文件类型
  const isImage = file.raw?.type === 'image/jpeg' || file.raw?.type === 'image/png'
  if (!isImage) {
    ElMessage.error('身份证正面照片只能是JPG或PNG格式!')
    // 清空文件列表，确保不显示在页面上
    idCardFrontFileList.value = []
    form.idCardFrontImage = null
    return false
  }

  // 检查文件大小
  const isLt5M = file.size! / 1024 / 1024 < 5
  if (!isLt5M) {
    ElMessage.error('身份证正面照片大小不能超过5MB!')
    // 清空文件列表，确保不显示在页面上
    idCardFrontFileList.value = []
    form.idCardFrontImage = null
    return false
  }

  // 更新表单数据
  form.idCardFrontImage = file
  return true
}

// 处理身份证正面照片移除
const handleIdCardFrontRemove = () => {
  form.idCardFrontImage = null
  ElMessage.warning('已移除身份证正面照片，请重新上传')
}

// 处理身份证背面照片上传
const handleIdCardBackChange = (file: UploadFile) => {
  // 检查文件类型
  const isImage = file.raw?.type === 'image/jpeg' || file.raw?.type === 'image/png'
  if (!isImage) {
    ElMessage.error('身份证背面照片只能是JPG或PNG格式!')
    // 从文件列表中移除不符合要求的文件
    idCardBackFileList.value = idCardBackFileList.value.filter((item) => item.uid !== file.uid)
    return false
  }

  // 检查文件大小
  const isLt5M = file.size! / 1024 / 1024 < 5
  if (!isLt5M) {
    ElMessage.error('身份证背面照片大小不能超过5MB!')
    // 从文件列表中移除不符合要求的文件
    idCardBackFileList.value = idCardBackFileList.value.filter((item) => item.uid !== file.uid)
    return false
  }

  // 更新表单数据
  form.idCardBackImage = file
  return true
}

// 处理身份证背面照片移除
const handleIdCardBackRemove = () => {
  form.idCardBackImage = null
  ElMessage.warning('已移除身份证背面照片，请重新上传')
}
const getFileFromLocalStorage = (filename: string) => {
  const fileData = JSON.parse(localStorage.getItem(`file_${filename}`) as string)
  // 将 Base64 转回 Blob
  const byteString = atob(fileData.data.split(',')[1])
  const arrayBuffer = new ArrayBuffer(byteString.length)
  const uint8Array = new Uint8Array(arrayBuffer)
  for (let i = 0; i < byteString.length; i++) {
    uint8Array[i] = byteString.charCodeAt(i)
  }
  return new Blob([arrayBuffer], { type: fileData.type })
}
// 处理仓库图片上传
const handleWarehouseImageChange = async (file: UploadFile) => {
  console.log('添加了', file)
  // console.log(JSON.stringify(file.raw));
  const base64 = await processImageForUpload(file.raw)

  console.log('存入本地的原始图片：', base64)
  const fileData = {
    uid: file.raw?.uid,
    name: file.raw?.name,
    type: file.raw?.type,
    size: file.raw?.size,
    data: base64, // Base64 字符串
    // lastModified: file.raw?.lastModified
  }
  console.log('包装后的图片', fileData)
  console.log('原始图片：', file.raw)

  console.log('解析后：', base64ToBlob(fileData.data))

  // localStorage.setItem(`file_${file.name}`, JSON.stringify(fileData));
  // console.log('存入本地的图片',JSON.stringify(fileData));
  // console.log('取出来的图片',JSON.parse(localStorage.getItem(`file_${file.name}`) as string));

  // 检查是否已经有3张图片了
  if (form.warehouseImage.length >= 3 && !form.warehouseImage.some((img) => img.uid === file.uid)) {
    ElMessage.error('最多只能上传3张发货仓库图片')
    // 从文件列表中移除超出数量的文件
    warehouseFileList.value = warehouseFileList.value.filter(
      (item) => form.warehouseImage.some((img) => img.uid === item.uid) || item.uid === file.uid,
    )
    // 只保留前3张图片
    warehouseFileList.value = warehouseFileList.value.slice(0, 3)

    return false
  }

  // 检查文件类型
  const isImage = file.raw?.type === 'image/jpeg' || file.raw?.type === 'image/png'
  if (!isImage) {
    ElMessage.error('仓库图片只能是JPG或PNG格式!')
    // 从文件列表中移除不符合要求的文件
    warehouseFileList.value = warehouseFileList.value.filter((item) => item.uid !== file.uid)
    return false
  }

  // 检查文件大小
  const isLt5M = file.size! / 1024 / 1024 < 5
  if (!isLt5M) {
    ElMessage.error('仓库图片大小不能超过5MB!')
    // 从文件列表中移除不符合要求的文件
    warehouseFileList.value = warehouseFileList.value.filter((item) => item.uid !== file.uid)
    return false
  }

  // 更新表单数据 - 确保不重复添加
  if (!form.warehouseImage.some((img) => img.uid === file.uid)) {
    // 如果已经有3张，则替换最后一张
    if (form.warehouseImage.length >= 3) {
      form.warehouseImage.pop()
    }
    form.warehouseImage.push(file)

    // 显示当前上传数量的提示
    const count = form.warehouseImage.length
    if (count === 1) {
      ElMessage.info('已上传1张仓库图片，还需要2张')
    } else if (count === 2) {
      ElMessage.info('已上传2张仓库图片，还需要1张')
    } else if (count === 3) {
      ElMessage.success('已上传3张仓库图片，数量已满足要求')
    }
  }
  // 确保fileList和form中的数据一致
  warehouseFileList.value = [...form.warehouseImage]
  form.warehouseImage = [...form.warehouseImage]
  return true
}

// 处理仓库图片移除
const handleWarehouseImageRemove = (file: UploadFile) => {
  const index = form.warehouseImage.findIndex((img) => img.uid === file.uid)
  if (index !== -1) {
    form.warehouseImage.splice(index, 1)
    console.log(`删除了第${index + 1}张图片`, file)

    // 显示当前剩余数量的提示
    const count = form.warehouseImage.length
    if (count === 0) {
      ElMessage.warning('已移除所有仓库图片，请上传3张仓库图片')
    } else if (count < 3) {
      ElMessage.warning(`已移除1张仓库图片，还剩${count}张，需要上传${3 - count}张`)
    } else {
      ElMessage.info(`已移除1张仓库图片，还剩${count}张`)
    }
  }
  // 确保fileList和form中的数据一致
  warehouseFileList.value = [...form.warehouseImage]
}

// 处理超出限制
const handleExceed = () => {
  ElMessage.warning('最多只能上传3张发货仓库图片')
}

// 处理长期有效选项变更
const handleLongTermChange = (val: boolean) => {
  if (val) {
    // 使用固定的远期日期表示"长期有效"
    form.licenseValidity = '2099-12-31'
  } else {
    form.licenseValidity = ''
  }
}

// 处理注册成功弹窗确认
const handleSuccessConfirm = () => {
  // 跳转到登录页
  router.push('/')
}

onMounted(() => {
  // 检查是否有第一步的数据，如果没有则返回第一步
  const accountInfo = localStorage.getItem('registerAccountInfo')
  if (!accountInfo) {
    ElMessage.warning('请先完成账号信息填写')
    router.push('/register')
    return
  }

  const merchantInfo = localStorage.getItem('registerMerchantInfo')
  if (merchantInfo) {
    try {
      const savedData = JSON.parse(merchantInfo)

      // 恢复基本表单数据
      form.shopName = savedData.shopName
      form.companyName = savedData.companyName
      form.businessLicense = savedData.businessLicense
      form.licenseValidity = savedData.licenseValidity
      form.isLongTermValidity = savedData.isLongTermValidity
      form.companyIntro = savedData.companyIntro
      form.contactPerson = savedData.contactPerson
      form.contactPhone = savedData.contactPhone
      form.province = savedData.province
      form.city = savedData.city
      form.district = savedData.district
      form.addressDetail = savedData.addressDetail
      form.idCardNumber = savedData.idCardNumber
      form.agreement = savedData.agreement
      form.gender = savedData.gender

      if (savedData.warehouseImages) {
        warehouseFileList.value = savedData.warehouseImages.map((fileInfo) => ({
          ...fileInfo,
          url: fileInfo.base64, // 直接使用Base64作为预览
          status: 'success',
        }))

        form.warehouseImage = savedData.warehouseImages.map((fileInfo) => ({
          ...fileInfo,
          status: 'success',
          raw: base64ToBlob(fileInfo.base64),
        }))
      }

      console.log(warehouseFileList.value)

      if (savedData.businessLicenseImage) {
        form.businessLicenseImage = {
          ...savedData.businessLicenseImage,
          url: savedData.businessLicenseImage.base64,
          status: 'success',
          raw: base64ToBlob(savedData.businessLicense.base64),
        }
        licenseFileList.value = [
          {
            ...savedData.businessLicenseImage,
            url: savedData.businessLicenseImage.base64,
            status: 'success',
          },
        ]
      }

      if (savedData.idCardFront) {
        idCardFrontFileList.value = [
          {
            ...savedData.idCardFront,
            status: 'success',
            url: savedData.idCardFront.base64,
          },
        ]

        form.idCardFrontImage = {
          ...savedData.idCardFront,
          status: 'success',
          raw: base64ToBlob(savedData.idCardFront.base64),
        }
      }

      if (savedData.idCardBack) {
        idCardBackFileList.value.push({
          ...savedData.idCardBack,
          status: 'success',
          url: savedData.idCardBack.base64,
        })

        form.idCardBackImage = {
          ...savedData.idCardBack,
          status: 'success',
          raw: base64ToBlob(savedData.idCardBack.base64),
        }
      }
    } catch (e) {
      console.error('解析保存的商户数据失败', e)
      // 可以选择清空错误数据
      localStorage.removeItem('registerMerchantInfo')
    }
  }
})

// 在组件卸载时清理本地存储
onUnmounted(() => {})
</script>

<style scoped lang="scss">
.register-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #eef2f7 100%);
  padding: 0;
  margin: 0;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  overflow: hidden;
}

.register-content {
  position: relative;
  width: 100%;
  max-width: 1200px;
  display: flex;
  justify-content: center;
}

.register-box {
  width: 700px;
  padding: 50px 80px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.08);
  position: relative;
  z-index: 10;
  animation: fadeIn 0.8s ease-out;
  max-height: 90vh;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #dcdfe6;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background-color: #f5f5f5;
  }

  .register-header {
    margin-bottom: 40px;
    text-align: center;
  }

  .register-title {
    font-size: 36px;
    font-weight: 600;
    color: #333;
    margin-bottom: 12px;
    background: linear-gradient(90deg, var(--el-color-primary), #409eff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;

    span {
      font-weight: 400;
      font-size: 32px;
    }
  }

  .register-subtitle {
    font-size: 16px;
    color: #909399;
    margin-top: 10px;
    margin-bottom: 25px;
  }
}

.step-form {
  width: 100%;
  animation: fadeIn 0.5s ease-in-out;

  :deep(.el-form-item__label) {
    font-size: 15px;
    font-weight: 500;
    color: #333;
    padding-bottom: 8px;
  }

  :deep(.el-input__wrapper) {
    padding: 0 15px;
    height: 48px;
    box-shadow: 0 0 0 1px #dcdfe6;
    transition: all 0.3s;

    &:hover {
      box-shadow: 0 0 0 1px var(--el-color-primary);
    }

    &.is-focus {
      box-shadow: 0 0 0 1px var(--el-color-primary);
    }
  }

  :deep(.el-textarea__inner) {
    padding: 12px 15px;
    font-size: 15px;
    box-shadow: 0 0 0 1px #dcdfe6;
    transition: all 0.3s;

    &:hover {
      box-shadow: 0 0 0 1px var(--el-color-primary);
    }

    &:focus {
      box-shadow: 0 0 0 1px var(--el-color-primary);
    }
  }

  :deep(.el-input__inner) {
    font-size: 15px;
  }

  :deep(.el-input__icon) {
    font-size: 18px;
    color: #909399;
  }

  :deep(.el-select) {
    width: 100%;
  }

  :deep(.el-checkbox__label) {
    font-size: 15px;
  }

  :deep(.el-date-editor) {
    --el-date-editor-width: 100%;
  }
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 30px 0 20px;
  padding-left: 12px;
  border-left: 4px solid var(--el-color-primary);
}

.address-row {
  display: flex;
  gap: 20px;

  .address-item {
    flex: 1;
  }
}

.form-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 30px;

  .el-button {
    width: 48%;
    height: 48px;
    font-size: 16px;
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.3s;

    &:hover {
      transform: translateY(-2px);
    }
  }

  .submit-btn {
    letter-spacing: 1px;

    &:hover {
      box-shadow: 0 8px 15px rgba(var(--el-color-primary-rgb), 0.3);
    }
  }
}

.agreement-content {
  max-height: 400px;
  overflow-y: auto;

  .agreement-details {
    margin-top: 20px;

    h3 {
      margin-top: 15px;
      margin-bottom: 10px;
      font-size: 16px;
    }

    p {
      margin-bottom: 10px;
      line-height: 1.6;
    }
  }
}

// 装饰元素
.decoration-circles {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;

  .circle {
    position: absolute;
    border-radius: 50%;
    opacity: 0.6;
  }

  .circle-1 {
    width: 300px;
    height: 300px;
    background: linear-gradient(
      135deg,
      rgba(var(--el-color-primary-rgb), 0.2),
      rgba(var(--el-color-primary-rgb), 0.05)
    );
    top: -100px;
    right: 10%;
    animation: float 8s ease-in-out infinite;
  }

  .circle-2 {
    width: 200px;
    height: 200px;
    background: linear-gradient(
      135deg,
      rgba(var(--el-color-primary-rgb), 0.15),
      rgba(var(--el-color-primary-rgb), 0.05)
    );
    bottom: -50px;
    left: 10%;
    animation: float 6s ease-in-out infinite 1s;
  }

  .circle-3 {
    width: 150px;
    height: 150px;
    background: linear-gradient(
      135deg,
      rgba(var(--el-color-primary-rgb), 0.1),
      rgba(var(--el-color-primary-rgb), 0.03)
    );
    top: 40%;
    left: 20%;
    animation: float 7s ease-in-out infinite 0.5s;
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 768px) {
  .register-box {
    width: 95% !important;
    padding: 30px !important;
    margin: 15px;
    max-height: 85vh;
  }

  .register-title {
    font-size: 28px !important;

    span {
      font-size: 24px !important;
    }
  }

  .register-subtitle {
    font-size: 14px !important;
  }

  .address-row {
    flex-direction: column;
    gap: 0;
  }

  .form-buttons {
    flex-direction: column;
    gap: 15px;

    .el-button {
      width: 100%;
    }
  }

  .section-title {
    font-size: 16px;
    margin: 20px 0 15px;
  }
}

.upload-section {
  margin-top: 30px;
  margin-bottom: 30px;
  padding: 25px;
  background-color: #f9fafc;
  border-radius: 12px;
  border: 1px solid #ebeef5;
}

.upload-container {
  width: 100%;

  :deep(.el-upload--picture-card) {
    width: 150px;
    height: 150px;
    border-radius: 8px;
    border: 1px dashed #d9d9d9;
    margin-right: 15px;
    margin-bottom: 15px;

    &:hover {
      border-color: var(--el-color-primary);
      background-color: rgba(var(--el-color-primary-rgb), 0.05);
    }

    .el-icon {
      font-size: 28px;
      color: #909399;
    }
  }

  :deep(.el-upload-list--picture-card .el-upload-list__item) {
    width: 150px;
    height: 150px;
    border-radius: 8px;
    margin-right: 15px;
    margin-bottom: 15px;
  }

  .upload-tip {
    font-size: 13px;
    color: #909399;
    margin-top: 12px;
    line-height: 1.5;

    .highlight-text {
      color: var(--el-color-primary);
      font-weight: 500;
    }

    .success-tip {
      color: #67c23a;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 5px;

      .el-icon {
        font-size: 16px;
      }
    }
  }
}

// 移除之前的上传样式
:deep(.el-upload--picture-card) {
  --el-upload-picture-card-size: 150px;
}

// 添加营业执照有效期相关样式
.license-validity-container {
  display: flex;
  flex-direction: column;
  gap: 10px;

  .long-term-option {
    display: flex;
    align-items: center;
    margin-top: 5px;

    .el-checkbox {
      font-size: 14px;
      color: #606266;
    }

    .tip-text {
      font-size: 12px;
      color: #909399;
      margin-left: 5px;
    }
  }
}

.credit-code-container {
  position: relative;
}

.info-icon {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
}
</style>
