import request from '@/utils/request'

// 获取分类树
export function getCategoryTree() {
  return request({
    url: '/categories/tree',
    method: 'get',
  })
}

// 创建分类
export function createCategory(data: any) {
  return request({
    url: '/categories/create',
    method: 'post',
    data,
  })
}

// 添加分类
export function addCategory(data: any) {
  return request({
    url: '/categories',
    method: 'post',
    data,
  })
}

// 更新分类
export function updateCategory(id: number, data: any) {
  return request({
    url: `/categories/${id}`,
    method: 'put',
    data,
  })
}

// 删除分类
export function deleteCategory(id: number) {
  return request({
    url: `/categories/${id}`,
    method: 'delete',
  })
}
