import './assets/main.scss'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

import App from './App.vue'
import router from './router'
// 导入组件注册模块
import GlobalComponents from './components'
// 导入语言服务
import { applyTranslation, LANGUAGE_STORAGE_KEY } from './utils/language'
// 导入自定义指令
import directives from './utils/directives'

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(ElementPlus)
// 注册全局组件
app.use(GlobalComponents)

// 注册自定义指令
app.directive('translate-when', directives.translateWhen)

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 设置路由导航守卫，在页面切换时重新应用语言设置
router.afterEach(() => {
  // 因为afterRouteChange函数已经处理了翻译，这里不再重复处理
  // 只在translate未加载时才设置标志
  if (!window.translate) {
    document.documentElement.setAttribute('data-translate-pending', 'true')
  }
})

// 挂载应用
app.mount('#app')

// 初始化多语言支持
document.addEventListener('DOMContentLoaded', () => {
  // 动态加载 translate.js
  const script = document.createElement('script')
  script.src = 'https://cdn.staticfile.net/translate.js/3.12.0/translate.js'
  script.onload = () => {
    // 配置翻译库
    window.translate.selectLanguageTag.show = false // 不显示语言选择下拉框
    window.translate.service.use('client.edge') // 设置翻译服务通道
    window.translate.ignore.class.push('no-translate') // 添加不需要翻译的类
    window.translate.ignore.class.push('translate-src') // 忽略翻译节点自身
    window.translate.ignore.tag.push('svg') // 忽略SVG元素
    window.translate.ignore.tag.push('style') // 忽略样式标签
    window.translate.ignore.tag.push('script') // 忽略脚本标签

    // 保存原始的changeLanguage方法
    const originalChangeLanguage = window.translate.changeLanguage

    // 翻译锁，防止重复翻译
    let isTranslating = false
    
    // 增强changeLanguage方法，确保在切换语言时清理已有翻译节点
    window.translate.changeLanguage = (language: string) => {
      // 如果已经在翻译中，则跳过
      if (isTranslating) return
      
      // 设置翻译锁
      isTranslating = true
      
      // 清理已有翻译节点，避免重复翻译
      const nodes = document.querySelectorAll('.translate-src')
      for (let i = 0; i < nodes.length; i++) {
        if (nodes[i].parentNode) {
          nodes[i].parentNode.removeChild(nodes[i])
        }
      }
      
      // 应用翻译
      originalChangeLanguage(language)
      
      // 统一存储键名
      localStorage.setItem(LANGUAGE_STORAGE_KEY, language)
      
      // 设置HTML属性
      document.documentElement.setAttribute('data-language', language)
      
      // 移除等待翻译标记
      document.documentElement.removeAttribute('data-translate-pending')
      
      // 延迟释放翻译锁
      setTimeout(() => {
        isTranslating = false
      }, 300)
    }

    // 初始化时加载保存的语言设置
    const savedLanguage = localStorage.getItem(LANGUAGE_STORAGE_KEY)
    if (savedLanguage) {
      window.translate.changeLanguage(savedLanguage)
    }
    
    // 创建一个节流函数，限制调用频率
    const throttle = (fn: Function, delay: number) => {
      let lastCall = 0
      return function(...args: any[]) {
        const now = new Date().getTime()
        if (now - lastCall < delay) return
        lastCall = now
        return fn(...args)
      }
    }
    
    // 翻译刷新函数
    const refreshTranslation = () => {
      const currentLang = localStorage.getItem(LANGUAGE_STORAGE_KEY)
      if (currentLang && !isTranslating) {
        window.translate.changeLanguage(currentLang)
      }
    }
    
    // 节流版翻译刷新，限制每2秒最多执行一次
    const throttledRefresh = throttle(refreshTranslation, 2000)
    
    // 添加DOM变化观察器
    let observer: MutationObserver | null = null
    
    // 路由变化后应用翻译的函数
    const applyTranslationAfterRouteChange = () => {
      // 断开观察器，避免触发循环
      if (observer) {
        observer.disconnect()
      }
      
      // 延迟执行翻译
      setTimeout(() => {
        if (!isTranslating) {
          const currentLang = localStorage.getItem(LANGUAGE_STORAGE_KEY)
          if (currentLang) {
            window.translate.changeLanguage(currentLang)
          }
        }
        
        // 重新连接观察器
        setTimeout(() => {
          if (observer) {
            observer.observe(document.body, {
              childList: true,
              subtree: true,
              attributes: false, // 不监听属性变化
              characterData: false // 不监听文本变化
            })
          }
        }, 500)
      }, 300)
    }
    
    // 设置路由导航守卫，在页面切换时重新应用语言设置
    router.afterEach(applyTranslationAfterRouteChange)
    
    // 标记是否是首次加载
    let isFirstLoad = true
    
    // 初始化观察器
    observer = new MutationObserver((mutations) => {
      // 首次加载时不触发刷新，避免重复翻译
      if (isFirstLoad) {
        isFirstLoad = false
        return
      }
      
      // 如果正在翻译中，忽略所有mutations
      if (isTranslating) return
      
      // 检查是否有重要的DOM变化（新增或删除节点）
      let hasSignificantChanges = false
      let hasDynamicContent = false
      
      for (const mutation of mutations) {
        // 忽略特定元素的变化
        const target = mutation.target as Element
        if (target.classList && 
            (target.classList.contains('translate-src') || 
             target.classList.contains('no-translate') ||
             target.classList.contains('el-menu') ||
             target.classList.contains('el-sub-menu') ||
             target.classList.contains('el-menu-item'))) {
          continue
        }
        
        // 检查添加的节点是否包含我们需要翻译的内容
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          // 检查是否是动态内容（如分页、列表加载等）
          for (let i = 0; i < mutation.addedNodes.length; i++) {
            const node = mutation.addedNodes[i]
            if (node.nodeType === 1) { // 元素节点
              const element = node as Element
              // 忽略菜单相关元素
              if (element.classList && (
                  element.classList.contains('el-menu') ||
                  element.classList.contains('el-sub-menu') ||
                  element.classList.contains('el-menu-item'))) {
                continue
              }
              
              // 忽略不需要翻译的元素
              if (element.tagName === 'SCRIPT' || 
                  element.tagName === 'STYLE' || 
                  element.tagName === 'SVG') {
                continue
              }
              
              // 检查是否是动态内容
              if (element.tagName === 'DIV' || element.tagName === 'LI' || 
                  element.tagName === 'TR' || element.tagName === 'TD') {
                hasDynamicContent = true
              }
              
              hasSignificantChanges = true
            }
          }
        }
      }
      
      // 只有在有重要DOM变化并且不在翻译中时，才触发翻译刷新
      if (hasSignificantChanges && !isTranslating && document.body.contains(document.querySelector('.page-content'))) {
        if (hasDynamicContent) {
          // 对于动态内容变化，使用节流版刷新
          throttledRefresh()
        }
      }
    })
    
    // 启动观察器，只监听节点添加/删除，不监听属性和文本变化
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: false,
      characterData: false
    })
  }
  
  document.head.appendChild(script)
})
