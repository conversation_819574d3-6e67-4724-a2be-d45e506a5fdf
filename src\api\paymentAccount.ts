import request from '@/utils/request'
import type {
  PaymentAccountDTO,
  PaymentAccountQueryDTO,
  PaymentAccountVO,
  AccountTypeStatistics,
  VerificationStatistics,
  PageResponse,
  ApiResponse
} from '@/types/paymentAccount'

// ==================== 商家端接口 (/merchant/payment-account) ====================

/**
 * 添加收账账户
 */
export const addPaymentAccount = (data: PaymentAccountDTO): Promise<ApiResponse<void>> => {
  return request.post('/merchant/payment-account', data)
}

/**
 * 更新收账账户
 */
export const updatePaymentAccount = (data: PaymentAccountDTO): Promise<ApiResponse<void>> => {
  return request.put('/merchant/payment-account', data)
}

/**
 * 删除收账账户
 */
export const deletePaymentAccount = (id: number): Promise<ApiResponse<void>> => {
  return request.delete(`/merchant/payment-account/${id}`)
}

/**
 * 查询收账账户详情
 */
export const getPaymentAccountDetail = (id: number): Promise<ApiResponse<PaymentAccountVO>> => {
  return request.get(`/merchant/payment-account/${id}`)
}

/**
 * 分页查询收账账户
 */
export const getPaymentAccountPage = (params: PaymentAccountQueryDTO): Promise<ApiResponse<PageResponse<PaymentAccountVO>>> => {
  return request.get('/merchant/payment-account/page', { params })
}

/**
 * 查询所有收账账户
 */
export const getPaymentAccountList = (): Promise<ApiResponse<PaymentAccountVO[]>> => {
  return request.get('/merchant/payment-account/list')
}

/**
 * 查询默认收账账户
 */
export const getDefaultPaymentAccount = (): Promise<ApiResponse<PaymentAccountVO>> => {
  return request.get('/merchant/payment-account/default')
}

/**
 * 设置默认收账账户
 */
export const setDefaultPaymentAccount = (id: number): Promise<ApiResponse<void>> => {
  return request.put(`/merchant/payment-account/${id}/default`)
}

/**
 * 启用收账账户
 */
export const enablePaymentAccount = (id: number): Promise<ApiResponse<void>> => {
  return request.put(`/merchant/payment-account/${id}/enable`)
}

/**
 * 禁用收账账户
 */
export const disablePaymentAccount = (id: number): Promise<ApiResponse<void>> => {
  return request.put(`/merchant/payment-account/${id}/disable`)
}

// ==================== 管理员端接口 (/admin/payment-account) ====================

/**
 * 分页查询所有收账账户（管理员）
 */
export const getAdminPaymentAccountPage = (params: PaymentAccountQueryDTO): Promise<ApiResponse<PageResponse<PaymentAccountVO>>> => {
  return request.get('/admin/payment-account/page', { params })
}

/**
 * 查询收账账户详情（管理员）
 */
export const getAdminPaymentAccountDetail = (id: number): Promise<ApiResponse<PaymentAccountVO>> => {
  return request.get(`/admin/payment-account/${id}`)
}

/**
 * 查询指定商家的收账账户（管理员）
 */
export const getSellerPaymentAccounts = (
  sellerId: number,
  params?: PaymentAccountQueryDTO
): Promise<ApiResponse<PageResponse<PaymentAccountVO>>> => {
  return request.get(`/admin/payment-account/seller/${sellerId}`, { params })
}

/**
 * 验证收账账户（管理员）
 */
export const verifyPaymentAccount = (id: number): Promise<ApiResponse<void>> => {
  return request.put(`/admin/payment-account/${id}/verify`)
}

/**
 * 启用收账账户（管理员）
 */
export const adminEnablePaymentAccount = (id: number): Promise<ApiResponse<void>> => {
  return request.put(`/admin/payment-account/${id}/enable`)
}

/**
 * 禁用收账账户（管理员）
 */
export const adminDisablePaymentAccount = (id: number): Promise<ApiResponse<void>> => {
  return request.put(`/admin/payment-account/${id}/disable`)
}

/**
 * 按账户类型统计（管理员）
 */
export const getAccountTypeStatistics = (): Promise<ApiResponse<AccountTypeStatistics[]>> => {
  return request.get('/admin/payment-account/statistics/type')
}

/**
 * 按验证状态统计（管理员）
 */
export const getVerificationStatistics = (): Promise<ApiResponse<VerificationStatistics[]>> => {
  return request.get('/admin/payment-account/statistics/verification')
}

// ==================== 工具函数 ====================

/**
 * 数据脱敏 - 账户号码
 */
export const maskAccountNumber = (accountNumber: string): string => {
  if (!accountNumber || accountNumber.length < 8) {
    return accountNumber
  }
  const start = accountNumber.substring(0, 4)
  const end = accountNumber.substring(accountNumber.length - 4)
  const middle = '*'.repeat(accountNumber.length - 8)
  return `${start}${middle}${end}`
}

/**
 * 数据脱敏 - 身份证号码
 */
export const maskIdCardNumber = (idCardNumber: string): string => {
  if (!idCardNumber || idCardNumber.length < 8) {
    return idCardNumber
  }
  const start = idCardNumber.substring(0, 4)
  const end = idCardNumber.substring(idCardNumber.length - 4)
  const middle = '*'.repeat(idCardNumber.length - 8)
  return `${start}${middle}${end}`
}

/**
 * 数据脱敏 - 手机号码
 */
export const maskPhoneNumber = (phoneNumber: string): string => {
  if (!phoneNumber || phoneNumber.length < 7) {
    return phoneNumber
  }
  const start = phoneNumber.substring(0, 3)
  const end = phoneNumber.substring(phoneNumber.length - 4)
  const middle = '*'.repeat(phoneNumber.length - 7)
  return `${start}${middle}${end}`
}

/**
 * 验证银行卡号格式
 */
export const validateBankCardNumber = (cardNumber: string): boolean => {
  // 银行卡号通常为16-19位数字
  const bankCardRegex = /^\d{16,19}$/
  return bankCardRegex.test(cardNumber)
}

/**
 * 验证身份证号格式
 */
export const validateIdCardNumber = (idCardNumber: string): boolean => {
  // 18位身份证号码格式
  const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/
  return idCardRegex.test(idCardNumber)
}

/**
 * 验证手机号格式
 */
export const validatePhoneNumber = (phoneNumber: string): boolean => {
  // 中国大陆手机号格式
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phoneNumber)
}
