<template>
  <div class="tracking-list">
    <el-card class="search-card">
      <template #header>
        <div class="card-header">
          <span>物流管理</span>
          <div class="header-actions">
            <el-button type="primary" @click="handleRegister">
              <el-icon><Plus /></el-icon>
              注册物流
            </el-button>
            <el-button @click="handleBatchSync" :loading="syncing">
              <el-icon><Refresh /></el-icon>
              批量同步
            </el-button>
            <el-button @click="handleExport" :loading="exporting">
              <el-icon><Download /></el-icon>
              导出
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="物流单号">
          <el-input
            v-model="searchForm.trackingNumber"
            placeholder="请输入物流单号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="运输商">
          <el-select
            v-model="searchForm.carrierCode"
            placeholder="请选择运输商"
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="carrier in carriers"
              :key="carrier.code"
              :label="carrier.name"
              :value="carrier.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="订单ID">
          <el-input
            v-model="searchForm.orderId"
            placeholder="请输入订单ID"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="物流状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择物流状态"
            clearable
            style="width: 150px"
          >
            <el-option label="待处理" value="Pending" />
            <el-option label="信息已录入" value="InfoReceived" />
            <el-option label="运输中" value="InTransit" />
            <el-option label="派送中" value="OutForDelivery" />
            <el-option label="已签收" value="Delivered" />
            <el-option label="异常" value="Exception" />
            <el-option label="已过期" value="Expired" />
          </el-select>
        </el-form-item>
        <el-form-item label="跟踪状态">
          <el-select
            v-model="searchForm.trackingStatus"
            placeholder="请选择跟踪状态"
            clearable
            style="width: 150px"
          >
            <el-option label="跟踪中" value="Tracking" />
            <el-option label="已停止" value="Stopped" />
          </el-select>
        </el-form-item>
        <el-form-item label="注册时间">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 350px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.total || 0 }}</div>
              <div class="stat-label">总跟踪数</div>
            </div>
            <el-icon class="stat-icon"><Box /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card tracking">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.tracking || 0 }}</div>
              <div class="stat-label">跟踪中</div>
            </div>
            <el-icon class="stat-icon"><Clock /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card delivered">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.delivered || 0 }}</div>
              <div class="stat-label">已签收</div>
            </div>
            <el-icon class="stat-icon"><CircleCheck /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card exception">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.exception || 0 }}</div>
              <div class="stat-label">异常</div>
            </div>
            <el-icon class="stat-icon"><Warning /></el-icon>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <template #header>
        <div class="table-header">
          <span>物流列表</span>
          <div class="batch-actions" v-if="selectedTrackings.length > 0">
            <el-button type="primary" size="small" @click="handleBatchSync">
              批量同步
            </el-button>
            <el-button type="warning" size="small" @click="handleBatchStop">
              批量停止
            </el-button>
            <el-button type="danger" size="small" @click="handleBatchDelete">
              批量删除
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="trackingList"
        @selection-change="handleSelectionChange"
        stripe
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="trackingNumber" label="物流单号" width="180" />
        <el-table-column prop="carrierName" label="运输商" width="120" />
        <el-table-column prop="orderNumber" label="订单号" width="180" />
        <el-table-column label="物流状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getLogisticsStatusType(row.status)" size="small">
              {{ getLogisticsStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="跟踪状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getTrackingStatusType(row.trackingStatus)" size="small">
              {{ getTrackingStatusText(row.trackingStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="latestEventInfo" label="最新事件" width="200" show-overflow-tooltip />
        <el-table-column prop="registerTime" label="注册时间" width="160" />
        <el-table-column prop="trackTime" label="最后跟踪" width="160" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleViewDetail(row)">
              详情
            </el-button>
            <el-button
              v-if="row.trackingStatus === 'Tracking'"
              type="warning"
              size="small"
              @click="handleSync(row)"
            >
              同步
            </el-button>
            <el-button
              v-if="row.trackingStatus === 'Tracking'"
              type="danger"
              size="small"
              @click="handleStop(row)"
            >
              停止
            </el-button>
            <el-button
              v-if="row.trackingStatus === 'Stopped'"
              type="success"
              size="small"
              @click="handleRestart(row)"
            >
              重启
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 物流详情对话框 -->
    <TrackingDetailDialog
      v-model="detailDialogVisible"
      :tracking="selectedTracking"
      @refresh="handleRefresh"
    />

    <!-- 注册物流对话框 -->
    <RegisterTrackingDialog
      v-model="registerDialogVisible"
      @refresh="handleRefresh"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Plus,
  Refresh,
  Download,
  Box,
  Clock,
  CircleCheck,
  Warning
} from '@element-plus/icons-vue'
import {
  getTrackingList,
  getCarriers,
  syncTrackingStatus,
  batchSyncTrackingStatus,
  stopTracking,
  restartTracking,
  batchDeleteTracking,
  exportTrackingData,
  getTrackingStatistics
} from '@/api/tracking'
import type {
  TrackingQueryDTO,
  TrackingRecord,
  Carrier
} from '@/types/tracking'
import {
  getLogisticsStatusText,
  getLogisticsStatusType,
  getTrackingStatusText,
  getTrackingStatusType
} from '@/types/tracking'
import TrackingDetailDialog from './components/TrackingDetailDialog.vue'
import RegisterTrackingDialog from './components/RegisterTrackingDialog.vue'

// 搜索表单
const searchForm = reactive<TrackingQueryDTO>({
  trackingNumber: '',
  carrierCode: undefined,
  orderId: undefined,
  status: '',
  trackingStatus: '',
  page: 1,
  pageSize: 20
})

// 日期范围
const dateRange = ref<string[]>([])

// 数据状态
const loading = ref(false)
const syncing = ref(false)
const exporting = ref(false)
const trackingList = ref<TrackingRecord[]>([])
const selectedTrackings = ref<TrackingRecord[]>([])
const carriers = ref<Carrier[]>([])

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 统计信息
const statistics = ref<any>({
  total: 0,
  tracking: 0,
  delivered: 0,
  exception: 0
})

// 对话框状态
const detailDialogVisible = ref(false)
const registerDialogVisible = ref(false)
const selectedTracking = ref<TrackingRecord | null>(null)

// 监听日期范围变化
watch(dateRange, (newVal) => {
  if (newVal && newVal.length === 2) {
    searchForm.beginTime = newVal[0]
    searchForm.endTime = newVal[1]
  } else {
    searchForm.beginTime = undefined
    searchForm.endTime = undefined
  }
})

// 获取物流列表
const fetchTrackingList = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      page: pagination.page,
      pageSize: pagination.pageSize
    }
    
    const response = await getTrackingList(params)
    if (response.code === 1) {
      trackingList.value = response.data || []
      // 注意：这里假设后端返回的是数组，如果是分页对象需要调整
      pagination.total = response.data?.length || 0
    } else {
      ElMessage.error(response.msg || '获取物流列表失败')
    }
  } catch (error) {
    console.error('获取物流列表失败:', error)
    ElMessage.error('获取物流列表失败')
  } finally {
    loading.value = false
  }
}

// 获取运输商列表
const fetchCarriers = async () => {
  try {
    const response = await getCarriers()
    if (response.code === 1) {
      carriers.value = response.data || []
    }
  } catch (error) {
    console.error('获取运输商列表失败:', error)
  }
}

// 获取统计信息
const fetchStatistics = async () => {
  try {
    const response = await getTrackingStatistics()
    if (response.code === 1) {
      statistics.value = response.data || {}
    }
  } catch (error) {
    console.error('获取统计信息失败:', error)
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchTrackingList()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    trackingNumber: '',
    carrierCode: undefined,
    orderId: undefined,
    status: '',
    trackingStatus: '',
    page: 1,
    pageSize: 20
  })
  dateRange.value = []
  pagination.page = 1
  fetchTrackingList()
}

// 刷新
const handleRefresh = () => {
  fetchTrackingList()
  fetchStatistics()
}

// 分页变化
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
  fetchTrackingList()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchTrackingList()
}

// 选择变化
const handleSelectionChange = (selection: TrackingRecord[]) => {
  selectedTrackings.value = selection
}

// 查看详情
const handleViewDetail = (tracking: TrackingRecord) => {
  selectedTracking.value = tracking
  detailDialogVisible.value = true
}

// 注册物流
const handleRegister = () => {
  registerDialogVisible.value = true
}

// 同步单个
const handleSync = async (tracking: TrackingRecord) => {
  try {
    const response = await syncTrackingStatus(tracking.trackingNumber, tracking.carrierCode)
    if (response.code === 1) {
      ElMessage.success('同步成功')
      handleRefresh()
    } else {
      ElMessage.error(response.msg || '同步失败')
    }
  } catch (error) {
    console.error('同步失败:', error)
    ElMessage.error('同步失败')
  }
}

// 批量同步
const handleBatchSync = async () => {
  if (selectedTrackings.value.length === 0) {
    ElMessage.warning('请选择要同步的物流记录')
    return
  }

  syncing.value = true
  try {
    const trackingNumbers = selectedTrackings.value.map(t => t.trackingNumber)
    const response = await batchSyncTrackingStatus(trackingNumbers)
    
    if (response.code === 1) {
      ElMessage.success('批量同步成功')
      handleRefresh()
    } else {
      ElMessage.error(response.msg || '批量同步失败')
    }
  } catch (error) {
    console.error('批量同步失败:', error)
    ElMessage.error('批量同步失败')
  } finally {
    syncing.value = false
  }
}

// 停止跟踪
const handleStop = async (tracking: TrackingRecord) => {
  try {
    await ElMessageBox.confirm('确认停止跟踪该物流单号？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const response = await stopTracking(tracking.trackingNumber, tracking.carrierCode)
    if (response.code === 1) {
      ElMessage.success('停止跟踪成功')
      handleRefresh()
    } else {
      ElMessage.error(response.msg || '停止跟踪失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('停止跟踪失败:', error)
      ElMessage.error('停止跟踪失败')
    }
  }
}

// 重启跟踪
const handleRestart = async (tracking: TrackingRecord) => {
  try {
    const response = await restartTracking(tracking.trackingNumber, tracking.carrierCode)
    if (response.code === 1) {
      ElMessage.success('重启跟踪成功')
      handleRefresh()
    } else {
      ElMessage.error(response.msg || '重启跟踪失败')
    }
  } catch (error) {
    console.error('重启跟踪失败:', error)
    ElMessage.error('重启跟踪失败')
  }
}

// 批量停止
const handleBatchStop = async () => {
  if (selectedTrackings.value.length === 0) {
    ElMessage.warning('请选择要停止的物流记录')
    return
  }

  try {
    await ElMessageBox.confirm(`确认停止跟踪选中的 ${selectedTrackings.value.length} 个物流单号？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 这里需要逐个停止，因为API不支持批量停止
    const promises = selectedTrackings.value.map(t => 
      stopTracking(t.trackingNumber, t.carrierCode)
    )
    
    await Promise.all(promises)
    ElMessage.success('批量停止成功')
    handleRefresh()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量停止失败:', error)
      ElMessage.error('批量停止失败')
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  if (selectedTrackings.value.length === 0) {
    ElMessage.warning('请选择要删除的物流记录')
    return
  }

  try {
    await ElMessageBox.confirm(`确认删除选中的 ${selectedTrackings.value.length} 个物流记录？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const ids = selectedTrackings.value.map(t => t.id)
    const response = await batchDeleteTracking(ids)
    
    if (response.code === 1) {
      ElMessage.success('批量删除成功')
      handleRefresh()
    } else {
      ElMessage.error(response.msg || '批量删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

// 导出
const handleExport = async () => {
  exporting.value = true
  try {
    await exportTrackingData(searchForm)
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exporting.value = false
  }
}

// 组件挂载
onMounted(() => {
  fetchTrackingList()
  fetchCarriers()
  fetchStatistics()
})
</script>

<style scoped lang="scss">
.tracking-list {
  padding: 20px;

  .search-card {
    margin-bottom: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-actions {
        display: flex;
        gap: 10px;
      }
    }

    .search-form {
      .el-form-item {
        margin-bottom: 15px;
      }
    }
  }

  .stats-cards {
    margin-bottom: 20px;

    .stat-card {
      position: relative;
      overflow: hidden;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .stat-content {
        .stat-number {
          font-size: 28px;
          font-weight: bold;
          color: #303133;
          margin-bottom: 5px;
        }

        .stat-label {
          font-size: 14px;
          color: #909399;
        }
      }

      .stat-icon {
        position: absolute;
        right: 20px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 40px;
        opacity: 0.3;
      }

      &.tracking {
        border-left: 4px solid #409eff;
        .stat-number { color: #409eff; }
        .stat-icon { color: #409eff; }
      }

      &.delivered {
        border-left: 4px solid #67c23a;
        .stat-number { color: #67c23a; }
        .stat-icon { color: #67c23a; }
      }

      &.exception {
        border-left: 4px solid #f56c6c;
        .stat-number { color: #f56c6c; }
        .stat-icon { color: #f56c6c; }
      }
    }
  }

  .table-card {
    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .batch-actions {
        display: flex;
        gap: 10px;
      }
    }

    .pagination-container {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .tracking-list {
    padding: 10px;

    .search-form {
      .el-form-item {
        width: 100%;
        margin-bottom: 10px;
      }
    }

    .stats-cards {
      .el-col {
        margin-bottom: 15px;
      }
    }

    .table-card {
      .el-table {
        font-size: 12px;
      }
    }
  }
}
</style>
