import request from '@/utils/request'

/**
 * 站内信相关 API
 */

interface SendMessageRequest {
  /** 站内信类型 */
  msgType: '系统公告' | '商家消息' | '用户消息';

  /** 
   * 站内信接受者列表 
   * */
  recipient?: {
    type: string;
    id: number;
  };

  /** 是否发送给全体用户 */

  sendToAllUsers?: boolean;

  /** 是否发送给全体商家 */
  sendToAllMerchants?: boolean;

  /** 站内信标题 */
  title: string;

  /** 站内信内容 */
  content: string;

  /** 消息发送时间 */
  sendTime: string;

  /** 发送者ID */
  senderID: number;

  /** 发送者身份 如：系统管理员/商家 */
  type: string;
}

// 获取站内信列表
export const getMessageList = (data: any) => {
  return request({
    url: '/message/list',
    method: 'get',
    params: data,
  })
}

// 获取消息详情
export const getMessageDetail = (msgID: string, data: any) => {
  return request({
    url: `/message/${msgID}`,
    method: 'get',
    params: data
  })
}

// 获取未读消息数量
export const getUnreadCount = (data: any) => {
  return request({
    url: '/message/unread/count',
    method: 'get',
    params: data
  })
}

// 发送站内信
export const sendMessage = (data: SendMessageRequest) => {
  return request({
    url: '/message/send',
    method: 'post',
    data
  })
}

// 发送系统公告
export const sendAnnouncement = (data: any) => {
  return request({
    url: '/message/announcement',
    method: 'post',
    data,
  })
}

// 标记消息为已读
export const markAsRead = (id: string, data: any) => {
  return request({
    url: `/message/${id}/read`,
    method: 'put',
    params:data
  })
}

// 批量标记消息为已读
export const batchMarkAsRead = (data: any) => {
  return request({
    url: '/message/batch/read',
    method: 'put',
    data
  })
}

// 标记全部消息为已读
export const markAllAsRead = (data: any) => {
  return request({
    url: '/message/all/read',
    method: 'put',
    params:data
  })
}

// 删除消息
export const deleteMessage = (id: string, data: any) => {
  return request({
    url: `/message/${id}`,
    method: 'delete',
    params: data
  })
}

// 批量删除消息
export const batchDeleteMessages = (data:any) => {
  return request({
    url: '/message/batch',
    method: 'delete',
    data,
  })
}

// 获取消息接收者列表（商家或用户）
export const getRecipientList = (data: any) => {
  return request({
    url: '/message/recipients',
    method: 'get',
    params: data,
  })
}