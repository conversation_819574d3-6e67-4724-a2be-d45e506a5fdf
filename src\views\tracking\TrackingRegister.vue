<template>
  <div class="tracking-register">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>注册物流单号</span>
          <div class="header-actions">
            <el-button @click="handleReset">重置</el-button>
            <el-button type="primary" @click="handleBatchRegister">
              <el-icon><Upload /></el-icon>
              批量注册
            </el-button>
          </div>
        </div>
      </template>

      <!-- 单个注册表单 -->
      <div class="register-section">
        <h3>单个注册</h3>
        <RegisterTrackingDialog
          v-model="registerDialogVisible"
          @refresh="handleRefresh"
        />
        <el-button type="primary" @click="handleSingleRegister">
          <el-icon><Plus /></el-icon>
          注册单个物流单号
        </el-button>
      </div>

      <!-- 批量注册区域 -->
      <div class="batch-section">
        <h3>批量注册</h3>
        <div class="batch-upload">
          <el-upload
            ref="uploadRef"
            :auto-upload="false"
            :on-change="handleFileChange"
            :before-upload="beforeUpload"
            accept=".xlsx,.xls,.csv"
            drag
          >
            <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                只能上传 xlsx/xls/csv 文件，且不超过 10MB
              </div>
            </template>
          </el-upload>
          
          <div class="batch-actions" v-if="uploadFile">
            <el-button @click="handleClearFile">清除文件</el-button>
            <el-button type="primary" @click="handleBatchUpload" :loading="uploading">
              开始批量注册
            </el-button>
          </div>
        </div>

        <!-- 模板下载 -->
        <div class="template-section">
          <h4>模板下载</h4>
          <p>请下载模板文件，按照模板格式填写物流信息后上传</p>
          <el-button @click="downloadTemplate">
            <el-icon><Download /></el-icon>
            下载Excel模板
          </el-button>
        </div>
      </div>

      <!-- 最近注册记录 -->
      <div class="recent-section">
        <h3>最近注册记录</h3>
        <el-table :data="recentRecords" stripe style="width: 100%">
          <el-table-column prop="trackingNumber" label="物流单号" width="180" />
          <el-table-column prop="carrierName" label="运输商" width="120" />
          <el-table-column prop="orderNumber" label="订单号" width="180" />
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getLogisticsStatusType(row.status)" size="small">
                {{ getLogisticsStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="registerTime" label="注册时间" width="160" />
          <el-table-column label="操作" width="120">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click="handleViewDetail(row)">
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 批量注册结果对话框 -->
    <el-dialog
      v-model="resultDialogVisible"
      title="批量注册结果"
      width="600px"
    >
      <div class="result-content">
        <div class="result-summary">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="summary-item">
                <div class="summary-number">{{ batchResult.total }}</div>
                <div class="summary-label">总数量</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="summary-item success">
                <div class="summary-number">{{ batchResult.success }}</div>
                <div class="summary-label">成功</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="summary-item error">
                <div class="summary-number">{{ batchResult.failed }}</div>
                <div class="summary-label">失败</div>
              </div>
            </el-col>
          </el-row>
        </div>
        
        <div v-if="batchResult.errors.length > 0" class="error-list">
          <h4>失败记录：</h4>
          <el-table :data="batchResult.errors" max-height="300">
            <el-table-column prop="row" label="行号" width="80" />
            <el-table-column prop="trackingNumber" label="物流单号" width="150" />
            <el-table-column prop="error" label="错误原因" />
          </el-table>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="resultDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 物流详情对话框 -->
    <TrackingDetailDialog
      v-model="detailDialogVisible"
      :tracking="selectedTracking"
      @refresh="handleRefresh"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Plus,
  Upload,
  UploadFilled,
  Download
} from '@element-plus/icons-vue'
import {
  batchRegisterTracking,
  getTrackingList
} from '@/api/tracking'
import type {
  RegisterTrackingDTO,
  TrackingRecord
} from '@/types/tracking'
import {
  getLogisticsStatusText,
  getLogisticsStatusType
} from '@/types/tracking'
import RegisterTrackingDialog from './components/RegisterTrackingDialog.vue'
import TrackingDetailDialog from './components/TrackingDetailDialog.vue'

// 对话框状态
const registerDialogVisible = ref(false)
const resultDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const selectedTracking = ref<TrackingRecord | null>(null)

// 上传相关
const uploadRef = ref()
const uploadFile = ref<File | null>(null)
const uploading = ref(false)

// 最近记录
const recentRecords = ref<TrackingRecord[]>([])

// 批量结果
const batchResult = reactive({
  total: 0,
  success: 0,
  failed: 0,
  errors: [] as Array<{ row: number; trackingNumber: string; error: string }>
})

// 单个注册
const handleSingleRegister = () => {
  registerDialogVisible.value = true
}

// 文件变化处理
const handleFileChange = (file: any) => {
  uploadFile.value = file.raw
}

// 上传前验证
const beforeUpload = (file: File) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                  file.type === 'application/vnd.ms-excel' ||
                  file.type === 'text/csv'
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isExcel) {
    ElMessage.error('只能上传 Excel 或 CSV 文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB!')
    return false
  }
  return true
}

// 清除文件
const handleClearFile = () => {
  uploadFile.value = null
  uploadRef.value?.clearFiles()
}

// 批量上传
const handleBatchUpload = async () => {
  if (!uploadFile.value) {
    ElMessage.warning('请先选择文件')
    return
  }

  uploading.value = true
  try {
    // 这里应该解析Excel文件并转换为RegisterTrackingDTO数组
    // 为了演示，我们使用模拟数据
    const mockData: RegisterTrackingDTO[] = [
      {
        trackingNumber: 'TEST001',
        carrierCode: 1,
        orderId: 1001,
        originCountry: 'CN',
        destinationCountry: 'US'
      },
      {
        trackingNumber: 'TEST002',
        carrierCode: 2,
        orderId: 1002,
        originCountry: 'CN',
        destinationCountry: 'GB'
      }
    ]

    const response = await batchRegisterTracking(mockData)
    
    if (response.code === 1) {
      // 处理批量注册结果
      batchResult.total = mockData.length
      batchResult.success = response.data?.length || 0
      batchResult.failed = batchResult.total - batchResult.success
      batchResult.errors = [] // 这里应该从响应中获取错误信息
      
      resultDialogVisible.value = true
      handleClearFile()
      handleRefresh()
      
      ElMessage.success(`批量注册完成，成功 ${batchResult.success} 个，失败 ${batchResult.failed} 个`)
    } else {
      ElMessage.error(response.msg || '批量注册失败')
    }
  } catch (error) {
    console.error('批量注册失败:', error)
    ElMessage.error('批量注册失败')
  } finally {
    uploading.value = false
  }
}

// 下载模板
const downloadTemplate = () => {
  // 创建模板数据
  const templateData = [
    ['物流单号', '运输商代码', '订单ID', '发货国家', '目的地国家', '标签', '备注'],
    ['示例: ABC123456789', '1', '1001', 'CN', 'US', '测试标签', '测试备注'],
    ['', '', '', '', '', '', '']
  ]

  // 创建CSV内容
  const csvContent = templateData.map(row => row.join(',')).join('\n')
  const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
  
  // 创建下载链接
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', '物流注册模板.csv')
  link.style.visibility = 'hidden'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  
  ElMessage.success('模板下载成功')
}

// 查看详情
const handleViewDetail = (tracking: TrackingRecord) => {
  selectedTracking.value = tracking
  detailDialogVisible.value = true
}

// 重置
const handleReset = () => {
  handleClearFile()
  Object.assign(batchResult, {
    total: 0,
    success: 0,
    failed: 0,
    errors: []
  })
}

// 刷新
const handleRefresh = () => {
  fetchRecentRecords()
}

// 获取最近记录
const fetchRecentRecords = async () => {
  try {
    const response = await getTrackingList({
      page: 1,
      pageSize: 10
    })
    if (response.code === 1) {
      recentRecords.value = response.data || []
    }
  } catch (error) {
    console.error('获取最近记录失败:', error)
  }
}

// 批量注册处理
const handleBatchRegister = () => {
  ElMessage.info('请使用下方的批量注册功能')
}

// 组件挂载
onMounted(() => {
  fetchRecentRecords()
})
</script>

<style scoped lang="scss">
.tracking-register {
  padding: 20px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-actions {
      display: flex;
      gap: 10px;
    }
  }

  .register-section,
  .batch-section,
  .recent-section {
    margin-bottom: 40px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;

    h3 {
      margin: 0 0 20px 0;
      color: #303133;
      border-bottom: 2px solid #409eff;
      padding-bottom: 10px;
    }

    h4 {
      margin: 20px 0 10px 0;
      color: #606266;
    }
  }

  .batch-upload {
    margin-bottom: 20px;

    .batch-actions {
      margin-top: 15px;
      display: flex;
      gap: 10px;
      justify-content: center;
    }
  }

  .template-section {
    padding: 15px;
    background: white;
    border-radius: 4px;
    border: 1px solid #dcdfe6;

    p {
      color: #606266;
      margin: 10px 0;
    }
  }

  .result-content {
    .result-summary {
      margin-bottom: 20px;

      .summary-item {
        text-align: center;
        padding: 15px;
        background: #f5f7fa;
        border-radius: 4px;

        .summary-number {
          font-size: 24px;
          font-weight: bold;
          color: #303133;
          margin-bottom: 5px;
        }

        .summary-label {
          font-size: 14px;
          color: #909399;
        }

        &.success {
          .summary-number { color: #67c23a; }
        }

        &.error {
          .summary-number { color: #f56c6c; }
        }
      }
    }

    .error-list {
      h4 {
        margin: 0 0 15px 0;
        color: #f56c6c;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .tracking-register {
    padding: 10px;

    .register-section,
    .batch-section,
    .recent-section {
      padding: 15px;
    }

    .batch-actions {
      flex-direction: column;

      .el-button {
        width: 100%;
      }
    }
  }
}
</style>
