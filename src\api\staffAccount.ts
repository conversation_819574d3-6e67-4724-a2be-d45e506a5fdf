import request from '@/utils/request'

// 商家子账号接口
export interface StaffAccount {
  accountName: string
  email: string
  phone: string
  password?: string
  accountStatus?: number | null
  createTime?: string
  updateTime?: string
  lastLoginTime?: string | null
  permissions?: string[]
  remark?: string
  createdBy?: number
  id?: number
}

// 获取所有商家子账号
export function getAllStaffAccounts(data: {createdBy: number,accountStatus?: number | null}) {
  return request({
    url: '/seller/sub-accounts',
    method: 'get',
    params:{...data}
  })
}

// 获取单个商家子账号
export function getStaffAccountById(id: number,data: {createdBy: number}) {
  return request({
    url: `/seller/sub-accounts/${id}`,
    method: 'get',
    params:{...data}
  })
}

// 创建商家子账号
export function createStaffAccount(data: StaffAccount) {
  return request({
    url: '/seller/sub-accounts',
    method: 'put',
    data,
  })
}

// 更新商家子账号
export function updateStaffAccount(id: number, data: Partial<StaffAccount>) {
  return request({
    url: `/seller/sub-accounts/${id}`,
    method: 'put',
    data,
  })
}

// 删除商家子账号
export function deleteStaffAccount(id: number,data: {createdBy: number}) {
  return request({
    url: `/seller/sub-accounts/${id}`,
    method: 'delete',
    params:{...data}
  })
}

// 更新商家子账号权限
export function updateStaffPermissions(id: number, createdBy: number, permissions: string[]) {
  return request({
    url: `/seller/sub-accounts/${id}/permissions`,
    method: 'put',
    params:{createdBy},
    data: { permissions },
  })
}

// 批量删除商家子账号
export function batchDeleteStaffAccounts(ids: number[],createdBy: number) {
  return request({
    url: '/seller/sub-accounts/batch-delete',
    method: 'delete',
    params:{
      createdBy,
    },
    data:{ids}
  })
}

// 批量更新商家子账号状态
export function batchUpdateStaffStatus(createdBy: number,data:{ids: number[],status: number}) {
  return request({
    url: '/seller/sub-accounts/batch-update-status',
    method: 'post',
    params: { createdBy }, 
    data
  })
}

// 重置商家子账号密码
export function resetStaffPassword(id: number, data: { password: string, createdBy: number}) {
  return request({
    url: `/seller/sub-accounts/${id}/reset-password`,
    method: 'post',
    params: { ...data},
  })
}
