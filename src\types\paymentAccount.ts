// 收账账户功能相关类型定义

// 账户类型枚举
export enum AccountType {
  BANK_CARD = 1,    // 银行卡
  ALIPAY = 2,       // 支付宝
  WECHAT = 3,       // 微信
  OTHER = 4         // 其他
}

// 账户类型描述映射
export const AccountTypeMap = {
  [AccountType.BANK_CARD]: '银行卡',
  [AccountType.ALIPAY]: '支付宝',
  [AccountType.WECHAT]: '微信',
  [AccountType.OTHER]: '其他'
}

// 账户状态枚举
export enum AccountStatus {
  DISABLED = 0,     // 禁用
  ENABLED = 1       // 启用
}

// 账户状态描述映射
export const AccountStatusMap = {
  [AccountStatus.DISABLED]: '禁用',
  [AccountStatus.ENABLED]: '启用'
}

// 验证状态枚举
export enum VerificationStatus {
  UNVERIFIED = 0,   // 未验证
  VERIFIED = 1      // 已验证
}

// 验证状态描述映射
export const VerificationStatusMap = {
  [VerificationStatus.UNVERIFIED]: '未验证',
  [VerificationStatus.VERIFIED]: '已验证'
}

// 是否默认账户枚举
export enum IsDefault {
  NO = 0,           // 非默认
  YES = 1           // 默认
}

// 是否默认账户描述映射
export const IsDefaultMap = {
  [IsDefault.NO]: '否',
  [IsDefault.YES]: '是'
}

// 收账账户实体
export interface SellerPaymentAccount {
  id: number                        // 主键ID
  sellerId: number                  // 商家ID
  accountType: AccountType          // 账户类型
  accountName: string               // 账户名称（持卡人/账户持有人姓名）
  accountNumber: string             // 账户号码（卡号/账号）
  bankName?: string                 // 银行名称（银行卡类型时必填）
  bankCode?: string                 // 银行代码
  branchName?: string               // 开户支行（银行卡类型时必填）
  platformName?: string             // 平台名称（支付宝/微信等）
  platformAccount?: string          // 平台账号（支付宝账号/微信号等）
  isDefault: IsDefault              // 是否默认账户
  accountStatus: AccountStatus      // 账户状态
  verificationStatus: VerificationStatus // 验证状态
  idCardNumber?: string             // 身份证号码
  phone?: string                    // 手机号码
  remark?: string                   // 备注信息
  createTime: string                // 创建时间
  updateTime: string                // 更新时间
}

// 收账账户数据传输对象
export interface PaymentAccountDTO {
  id?: number                       // 主键ID（更新时需要）
  accountType: AccountType          // 账户类型
  accountName: string               // 账户名称
  accountNumber: string             // 账户号码
  bankName?: string                 // 银行名称
  bankCode?: string                 // 银行代码
  branchName?: string               // 开户支行
  platformName?: string             // 平台名称
  platformAccount?: string          // 平台账号
  isDefault?: IsDefault             // 是否默认账户
  idCardNumber?: string             // 身份证号码
  phone?: string                    // 手机号码
  remark?: string                   // 备注信息
}

// 收账账户查询条件对象
export interface PaymentAccountQueryDTO {
  page?: number                     // 页码
  pageSize?: number                 // 每页大小
  sellerId?: number                 // 商家ID
  accountType?: AccountType         // 账户类型
  accountName?: string              // 账户名称
  accountStatus?: AccountStatus     // 账户状态
  verificationStatus?: VerificationStatus // 验证状态
  isDefault?: IsDefault             // 是否默认账户
}

// 收账账户视图对象
export interface PaymentAccountVO {
  id: number                        // 主键ID
  sellerId: number                  // 商家ID
  sellerName: string                // 商家名称
  accountType: AccountType          // 账户类型
  accountTypeDesc: string           // 账户类型描述
  accountName: string               // 账户名称
  accountNumber: string             // 账户号码（脱敏后）
  fullAccountNumber?: string        // 完整账号（仅内部使用）
  bankName?: string                 // 银行名称
  bankCode?: string                 // 银行代码
  branchName?: string               // 开户支行
  platformName?: string             // 平台名称
  platformAccount?: string          // 平台账号
  isDefault: IsDefault              // 是否默认账户
  isDefaultDesc: string             // 是否默认账户描述
  accountStatus: AccountStatus      // 账户状态
  accountStatusDesc: string         // 账户状态描述
  verificationStatus: VerificationStatus // 验证状态
  verificationStatusDesc: string    // 验证状态描述
  idCardNumber?: string             // 身份证号码（脱敏后）
  phone?: string                    // 手机号码（脱敏后）
  remark?: string                   // 备注信息
  createTime: string                // 创建时间
  updateTime: string                // 更新时间
}

// 账户类型统计
export interface AccountTypeStatistics {
  accountType: AccountType          // 账户类型
  accountTypeDesc: string           // 账户类型描述
  count: number                     // 数量
  percentage: number                // 百分比
}

// 验证状态统计
export interface VerificationStatistics {
  verificationStatus: VerificationStatus // 验证状态
  verificationStatusDesc: string    // 验证状态描述
  count: number                     // 数量
  percentage: number                // 百分比
}

// API响应基础结构
export interface ApiResponse<T = any> {
  code: number                      // 响应码
  msg: string                       // 响应消息
  data: T                           // 响应数据
}

// 分页响应
export interface PageResponse<T> {
  list: T[]                         // 数据列表
  total: number                     // 总记录数
  size: number                      // 每页大小
  pageNum: number                   // 当前页
  pages: number                     // 总页数
}
