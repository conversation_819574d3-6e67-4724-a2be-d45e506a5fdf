import request from '@/utils/request'

/**
 * 佣金设置相关 API
 */

// 获取基础佣金设置
export const getCommissionSettings = () => {
  return request({
    url: '/commission/settings',
    method: 'get',
  })
}

// 保存基础佣金设置
export const saveCommissionSettings = (data: any) => {
  return request({
    url: '/commission/settings',
    method: 'post',
    data,
  })
}

// 获取佣金规则
export const getCommissionRules = () => {
  return request({
    url: '/commission/rules',
    method: 'get',
  })
}

// 保存佣金规则
export const saveCommissionRules = (data: any) => {
  return request({
    url: '/commission/rules',
    method: 'post',
    data,
  })
}

/**
 * 团长管理相关 API
 */

// 获取团长列表
export const getLeaderList = (params: any) => {
  return request({
    url: '/commission/leaders',
    method: 'get',
    params,
  })
}

// 获取团长详情
export const getLeaderDetail = (id: string | number) => {
  return request({
    url: `/commission/leaders/${id}`,
    method: 'get',
  })
}

// 添加团长
export const addLeader = (data: any) => {
  return request({
    url: '/commission/leaders',
    method: 'post',
    data,
  })
}

// 更新团长
export const updateLeader = (id: string | number, data: any) => {
  return request({
    url: `/commission/leaders/${id}`,
    method: 'put',
    data,
  })
}

// 删除团长
export const deleteLeader = (id: string | number) => {
  return request({
    url: `/commission/leaders/${id}`,
    method: 'delete',
  })
}

// 更新团长状态
export const updateLeaderStatus = (id: string | number, status: number) => {
  return request({
    url: `/commission/leaders/${id}/status`,
    method: 'put',
    data: { status },
  })
}

/**
 * 邀请码管理相关 API
 */

// 获取邀请码列表
export const getInvitationCodes = (params: any) => {
  return request({
    url: '/commission/invitation-codes',
    method: 'get',
    params,
  })
}

// 生成邀请码
export const generateInvitationCode = (data: any) => {
  return request({
    url: '/commission/invitation-codes',
    method: 'post',
    data,
  })
}

// 更新邀请码状态
export const updateInvitationCodeStatus = (id: string | number, status: number) => {
  return request({
    url: `/commission/invitation-codes/${id}/status`,
    method: 'put',
    data: { status },
  })
}

/**
 * 佣金统计相关 API
 */

// 获取佣金统计数据
export const getCommissionStatistics = (params: any) => {
  return request({
    url: '/commission/statistics',
    method: 'get',
    params,
  })
}

// 获取佣金明细列表
export const getCommissionDetails = (params: any) => {
  return request({
    url: '/commission/details',
    method: 'get',
    params,
  })
}

// 获取某个团长的佣金明细
export const getLeaderCommissionDetails = (leaderId: string | number, params: any) => {
  return request({
    url: `/commission/leaders/${leaderId}/details`,
    method: 'get',
    params,
  })
}

// 结算佣金
export const settleCommission = (data: any) => {
  return request({
    url: '/commission/settle',
    method: 'post',
    data,
  })
}
