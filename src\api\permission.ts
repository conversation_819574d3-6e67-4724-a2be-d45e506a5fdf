import request from '@/utils/request'

// 商家信息接口
export interface Seller {
  id: number
  accountName: string
  gender: number
  phone: string
  email: string
  accountStatus: number
  photoUrl?: string
  createTime: string
  shopName?: string
  companyName?: string
  contactPerson?: string
  contactPhone?: string
  businessLicense?: string
  licenseValidity?: string
  companyIntro?: string
  status?: number
  updateTime?: string
  lastLoginTime?: string
  roles?: string[]
  roleId?: number
}

// 权限规则接口
export interface PermissionRule {
  id: number
  code: string
  name: string
  type: 'menu' | 'function' | 'data'
  description: string
  status: number
  roleId?: number
}

// 权限信息接口
export interface PermissionInfo {
  permissionCode: string
  description: string
}

// 获取所有商家信息
export function getAllSellers() {
  return request({
    url: '/sellers/All',
    method: 'get',
  })
}

// 更新商家权限
export function updateSellerPermission(id: number, permissions: string[]) {
  return request({
    url: `/sellers/permission/${id}`,
    method: 'put',
    data: { permissions },
  })
}

// 移除商家权限
export function removeSellerPermission(sellerId: number, permissionCode: string) {
  return request({
    url: '/SellerPermission/remove',
    method: 'delete',
    data: { sellerId, permissionCode },
  })
}

// 添加商家权限
export interface SellerPermission {
  id?: number
  sellerId: number
  permissionCode: string
  description: string
}

export function addSellerPermission(data: SellerPermission) {
  return request({
    url: '/SellerPermission/add',
    method: 'post',
    data,
  })
}

// 获取单个用户的权限信息
export function getSellerPermission(sellerId: number) {
  return request({
    url: '/SellerPermission/getSellerPermission',
    method: 'get',
    params: { sellerId },
  })
}

// 获取所有权限列表
export function getAllPermissionList() {
  return request({
    url: '/SellerPermission/getAllPermission',
    method: 'get',
  })
}

// 获取所有权限列表 (旧的API，可能会被替换)
export function getAllPermissions() {
  return request({
    url: '/permissions/list',
    method: 'get',
  })
}

// 创建权限
export function createPermission(data: any, roleId: number = 1) {
  return request({
    url: '/seller/permission/create',
    method: 'post',
    data,
    params: { roleId },
  })
}

// 删除权限
export function deletePermission(id: number) {
  return request({
    url: `/permissions/${id}`,
    method: 'delete',
  })
}

// 更新权限
export function updatePermission(data: any) {
  return request({
    url: '/seller/permission/update',
    method: 'post',
    data,
  })
}
