<template>
  <div class="merchant-settlement">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>回款信息</h2>
      <p>查看您的订单回款情况和统计信息</p>
    </div>

    <!-- 收款账户信息 -->
    <div class="payment-account-info" v-if="defaultPaymentAccount">
      <el-card class="account-card">
        <template #header>
          <div class="account-header">
            <div class="header-icon">
              <el-icon><CreditCard /></el-icon>
            </div>
            <div class="header-text">
              <h3>默认收款账户</h3>
              <p>回款将转入以下账户</p>
            </div>
            <el-button type="primary" size="small" @click="goToAccountManagement">
              管理账户
            </el-button>
          </div>
        </template>

        <div class="account-details">
          <div class="account-info-row">
            <span class="label">账户类型：</span>
            <el-tag :type="getAccountTypeTagType(defaultPaymentAccount.accountType)">
              {{ defaultPaymentAccount.accountTypeDesc }}
            </el-tag>
          </div>
          <div class="account-info-row">
            <span class="label">账户名称：</span>
            <span class="value">{{ defaultPaymentAccount.accountName }}</span>
          </div>
          <div class="account-info-row">
            <span class="label">账户号码：</span>
            <span class="value">{{ defaultPaymentAccount.accountNumber }}</span>
          </div>
          <div v-if="defaultPaymentAccount.bankName" class="account-info-row">
            <span class="label">银行名称：</span>
            <span class="value">{{ defaultPaymentAccount.bankName }}</span>
          </div>
          <div v-if="defaultPaymentAccount.branchName" class="account-info-row">
            <span class="label">开户支行：</span>
            <span class="value">{{ defaultPaymentAccount.branchName }}</span>
          </div>
          <div class="account-info-row">
            <span class="label">验证状态：</span>
            <el-tag :type="defaultPaymentAccount.verificationStatus === 1 ? 'success' : 'warning'" size="small">
              {{ defaultPaymentAccount.verificationStatusDesc }}
            </el-tag>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 无收款账户提示 -->
    <div class="no-account-warning" v-else>
      <el-alert
        title="未设置收款账户"
        description="请先设置收款账户，以便接收回款"
        type="warning"
        show-icon
        :closable="false"
      >
        <template #default>
          <div class="alert-content">
            <p>您还没有设置收款账户，回款将无法正常转入。</p>
            <el-button type="primary" size="small" @click="goToAccountManagement">
              立即设置
            </el-button>
          </div>
        </template>
      </el-alert>
    </div>

    <!-- 回款汇总卡片 -->
    <div class="summary-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="summary-card">
            <div class="card-content">
              <div class="card-icon total">
                <el-icon><Money /></el-icon>
              </div>
              <div class="card-info">
                <h3>{{ formatCurrency(summary.totalSettlementAmount) }}</h3>
                <p>总回款金额</p>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="summary-card">
            <div class="card-content">
              <div class="card-icon pending">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="card-info">
                <h3>{{ formatCurrency(summary.pendingAmount) }}</h3>
                <p>待回款金额</p>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="summary-card">
            <div class="card-content">
              <div class="card-icon completed">
                <el-icon><Check /></el-icon>
              </div>
              <div class="card-info">
                <h3>{{ formatCurrency(summary.completedAmount) }}</h3>
                <p>已回款金额</p>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="summary-card">
            <div class="card-content">
              <div class="card-icon orders">
                <el-icon><Document /></el-icon>
              </div>
              <div class="card-info">
                <h3>{{ summary.totalOrders }}</h3>
                <p>总订单数</p>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 查询条件 -->
    <el-card class="search-card">
      <el-form :model="queryForm" :inline="true" class="search-form">
        <el-form-item label="订单号">
          <el-input
            v-model="queryForm.orderNumber"
            placeholder="请输入订单号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="回款状态">
          <el-select
            v-model="queryForm.settlementStatus"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="未到期" :value="0" />
            <el-option label="待回款" :value="1" />
            <el-option label="已回款" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="账单周期">
          <el-select
            v-model="queryForm.billingCycle"
            placeholder="请选择账单周期"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="cycle in billingCycles"
              :key="cycle"
              :label="cycle"
              :value="cycle"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="回款日期">
          <el-date-picker
            v-model="settlementDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 回款订单列表 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>回款订单列表</span>
          <div class="header-actions">
            <el-button type="primary" @click="handleExport" :loading="exportLoading">
              <el-icon><Download /></el-icon>
              导出数据
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        v-loading="tableLoading"
        :data="tableData"
        stripe
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="orderNumber" label="订单号" width="180" />
        <el-table-column prop="orderAmount" label="订单金额" width="120" sortable>
          <template #default="{ row }">
            <span class="amount">{{ formatCurrency(row.orderAmount) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="settlementAmount" label="回款金额" width="120" sortable>
          <template #default="{ row }">
            <span class="amount">{{ formatCurrency(row.settlementAmount) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="payTime" label="支付时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.payTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="billingCycle" label="账单周期" width="100" />
        <el-table-column prop="settlementDate" label="回款日期" width="120">
          <template #default="{ row }">
            {{ formatDate(row.settlementDate) }}
          </template>
        </el-table-column>
        <el-table-column prop="settlementStatus" label="回款状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.settlementStatus)">
              {{ row.settlementStatusDesc }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="daysToSettlement" label="距离回款" width="100">
          <template #default="{ row }">
            <span v-if="row.settlementStatus === 0" class="days-info">
              {{ row.daysToSettlement }}天后
            </span>
            <span v-else-if="row.settlementStatus === 1" class="days-info overdue">
              {{ row.daysToSettlement < 0 ? `逾期${Math.abs(row.daysToSettlement)}天` : `${row.daysToSettlement}天内` }}
            </span>
            <span v-else-if="row.settlementStatus === 2" class="days-info completed">
              已完成
            </span>
            <span v-else class="days-info">
              -
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="settlementTime" label="实际回款时间" width="160">
          <template #default="{ row }">
            {{ row.settlementTime ? formatDateTime(row.settlementTime) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" min-width="120">
          <template #default="{ row }">
            {{ row.remark || '-' }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryForm.page"
          v-model:page-size="queryForm.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Money,
  Clock,
  Check,
  Document,
  Search,
  Refresh,
  Download,
  CreditCard
} from '@element-plus/icons-vue'
import {
  getMerchantSettlementSummary,
  getMerchantSettlementOrders
} from '@/api/settlement'
import type {
  SettlementQueryDTO,
  SettlementInfoVO,
  SettlementSummaryVO
} from '@/types/settlement'
import { getDefaultPaymentAccount } from '@/api/paymentAccount'
import type { PaymentAccountVO, AccountType } from '@/types/paymentAccount'
import { useRouter } from 'vue-router'

// 路由
const router = useRouter()

// 响应式数据
const tableLoading = ref(false)
const defaultPaymentAccount = ref<PaymentAccountVO | null>(null)
const exportLoading = ref(false)
const tableData = ref<SettlementInfoVO[]>([])
const total = ref(0)
const billingCycles = ref<string[]>([])
const settlementDateRange = ref<[string, string] | null>(null)

// 汇总信息
const summary = ref<SettlementSummaryVO>({
  totalOrders: 0,
  totalOrderAmount: 0,
  totalSettlementAmount: 0,
  notDueOrders: 0,
  notDueAmount: 0,
  pendingOrders: 0,
  pendingAmount: 0,
  completedOrders: 0,
  completedAmount: 0
})

// 查询表单
const queryForm = reactive<SettlementQueryDTO>({
  page: 1,
  pageSize: 20,
  orderNumber: '',
  settlementStatus: undefined,
  billingCycle: '',
  settlementStartDate: '',
  settlementEndDate: ''
})

// 监听日期范围变化
watch(settlementDateRange, (newVal) => {
  if (newVal) {
    queryForm.settlementStartDate = newVal[0]
    queryForm.settlementEndDate = newVal[1]
  } else {
    queryForm.settlementStartDate = ''
    queryForm.settlementEndDate = ''
  }
})

// 格式化金额
const formatCurrency = (amount: number): string => {
  return `¥${amount.toFixed(2)}`
}

// 格式化日期时间
const formatDateTime = (dateTime: string): string => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 格式化日期
const formatDate = (date: string): string => {
  return new Date(date).toLocaleDateString('zh-CN')
}

// 获取状态标签类型
const getStatusTagType = (status: number): string => {
  switch (status) {
    case 0: return 'info'     // 未到期
    case 1: return 'warning'  // 待回款
    case 2: return 'success'  // 已回款
    default: return 'info'
  }
}

// 加载汇总信息
const loadSummary = async () => {
  try {
    const response = await getMerchantSettlementSummary()
    if (response.code === 1) {
      summary.value = response.data
    }
  } catch (error) {
    console.error('加载汇总信息失败:', error)
  }
}

// 加载表格数据
const loadTableData = async () => {
  tableLoading.value = true
  try {
    const response = await getMerchantSettlementOrders(queryForm)
    if (response.code === 1) {
      // 后端返回的是 list 而不是 records
      tableData.value = response.data.list || []
      total.value = response.data.total || 0
      // 更新账单周期选项
      loadBillingCycles()
    } else {
      ElMessage.error(response.msg || '加载数据失败')
    }
  } catch (error) {
    console.error('加载表格数据失败:', error)
    ElMessage.error('加载数据失败，请稍后重试')
  } finally {
    tableLoading.value = false
  }
}

// 加载账单周期 - 从现有数据中提取
const loadBillingCycles = () => {
  // 从表格数据中提取唯一的账单周期
  const cycles = [...new Set(tableData.value.map(item => item.billingCycle).filter(Boolean))]
  billingCycles.value = cycles.sort().reverse() // 按时间倒序排列
}

// 搜索
const handleSearch = () => {
  queryForm.page = 1
  loadTableData()
}

// 重置
const handleReset = () => {
  Object.assign(queryForm, {
    page: 1,
    pageSize: 20,
    orderNumber: '',
    settlementStatus: undefined,
    billingCycle: '',
    settlementStartDate: '',
    settlementEndDate: ''
  })
  settlementDateRange.value = null
  loadTableData()
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  queryForm.pageSize = size
  queryForm.page = 1
  loadTableData()
}

// 当前页变化
const handleCurrentChange = (page: number) => {
  queryForm.page = page
  loadTableData()
}

// 排序变化
const handleSortChange = ({ prop, order }: any) => {
  // 这里可以添加排序逻辑
  console.log('排序变化:', prop, order)
}

// 导出数据
const handleExport = async () => {
  exportLoading.value = true
  try {
    // 这里添加导出逻辑
    ElMessage.success('导出功能开发中...')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请稍后重试')
  } finally {
    exportLoading.value = false
  }
}

// 加载默认收款账户
const loadDefaultPaymentAccount = async () => {
  try {
    const response = await getDefaultPaymentAccount()
    if (response.code === 1) {
      defaultPaymentAccount.value = response.data
    } else {
      // 没有默认账户时不显示错误，只是不显示账户信息
      defaultPaymentAccount.value = null
    }
  } catch (error) {
    console.error('加载默认收款账户失败:', error)
    defaultPaymentAccount.value = null
  }
}

// 获取账户类型标签类型
const getAccountTypeTagType = (accountType: AccountType): string => {
  switch (accountType) {
    case 1: return 'primary'  // 银行卡
    case 2: return 'success'  // 支付宝
    case 3: return 'warning'  // 微信
    case 4: return 'info'     // 其他
    default: return 'info'
  }
}

// 跳转到账户管理页面
const goToAccountManagement = () => {
  router.push('/main/user/profile')
}

// 组件挂载时加载数据
onMounted(() => {
  loadSummary()
  loadTableData()
  loadDefaultPaymentAccount()
})
</script>

<style scoped>
.merchant-settlement {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

/* 收款账户信息样式 */
.payment-account-info {
  margin-bottom: 20px;
}

.account-card :deep(.el-card__header) {
  padding: 16px 20px;
}

.account-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  margin-right: 12px;
}

.header-text {
  flex: 1;
}

.header-text h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.header-text p {
  margin: 0;
  font-size: 14px;
  color: #909399;
}

.account-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.account-info-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.account-info-row .label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
}

.account-info-row .value {
  color: #303133;
  flex: 1;
}

.no-account-warning {
  margin-bottom: 20px;
}

.alert-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 8px;
}

.alert-content p {
  margin: 0;
  color: #e6a23c;
}

.summary-cards {
  margin-bottom: 20px;
}

.summary-card {
  height: 120px;
}

.card-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.card-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card-icon.pending {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.card-icon.completed {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.card-icon.orders {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.card-info h3 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.card-info p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 0;
}

.table-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.amount {
  font-weight: bold;
  color: #E6A23C;
}

.days-info {
  color: #409EFF;
}

.overdue {
  color: #F56C6C;
  font-weight: bold;
}

.completed {
  color: #67C23A;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
