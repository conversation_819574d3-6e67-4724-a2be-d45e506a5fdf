import { createRouter, createWebHistory } from 'vue-router'
import { getToken } from '@/utils/auth'
import { useUserStore } from '@/stores/user'

// 简单的token过期检查函数
const isTokenExpired = () => {
  const expiryTime = localStorage.getItem('sharewharf_token_expiry')
  if (!expiryTime) return true
  const expiry = parseInt(expiryTime)
  return isNaN(expiry) || Date.now() > expiry
}

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'login',
      component: () => import('../views/login.vue'),
    },
    {
      path: '/login',
      redirect: '/',
    },
    {
      path: '/declaration',
      name: 'declaration',
      component: () => import('../views/declaration.vue'),
    },
    {
      path: '/register',
      name: 'register',
      redirect: '/register/step1',
      children: [
        {
          path: 'step1',
          name: 'register-step1',
          component: () => import('../views/register1.vue'),
        },
        {
          path: 'step2',
          name: 'register-step2',
          component: () => import('../views/register2.vue'),
        },
      ],
    },
    {
      path: '/main',
      name: 'main',
      component: () => import('../views/main.vue'),
      redirect: '/main/welcome',
      meta: { requiresAuth: true },
      children: [
        {
          path: 'welcome',
          name: 'welcome',
          component: () => import('../views/welcome.vue'),
          meta: { title: '欢迎页面' },
        },
        {
          path: 'dashboard',
          name: 'dashboard',
          component: () => import('../views/dashboard.vue'),
        },
        {
          path: 'merchant-sales',
          name: 'merchant-sales',
          component: () => import('../views/MerchantSales.vue'),
          meta: { title: '商家营业额分析' },
        },
        {
          path: 'refund-analysis',
          name: 'refund-analysis',
          component: () => import('../views/RefundAnalysis.vue'),
          meta: { title: '退货情况分析' },
        },
        {
          path: 'hot-products',
          name: 'hot-products',
          component: () => import('../views/HotProducts.vue'),
          meta: { title: '高销售商品分析' },
        },
        {
          path: 'product',
          name: 'Product',
          component: () => import('../views/product/index.vue'),
          children: [
            {
              path: 'list',
              name: 'ProductList',
              component: () => import('../views/product/list.vue'),
            },
            {
              path: 'add',
              name: 'ProductAdd',
              component: () => import('../views/product/add.vue'),
            },
            {
              path: 'edit/:id',
              name: 'ProductEdit',
              component: () => import('../views/product/edit.vue'),
            },
            {
              path: 'audit',
              name: 'ProductAudit',
              component: () => import('../views/product/audit.vue'),
            },
          ],
        },
        {
          path: 'user-management',
          name: 'UserManagement',
          component: () => import('../views/user-management/index.vue'),
          meta: { title: '用户管理', adminOnly: true },
          children: [
            {
              path: 'user-list',
              name: 'UserList',
              component: () => import('../views/user-management/UserList.vue'),
              meta: { title: '用户列表', adminOnly: true },
            },
            {
              path: 'user-detail/:id',
              name: 'UserDetail',
              component: () => import('../views/user-management/UserDetail.vue'),
              meta: { title: '用户详情', adminOnly: true, hidden: true },
            },
            {
              path: 'purchase-analysis',
              name: 'PurchaseAnalysis',
              component: () => import('../views/user-management/PurchaseAnalysis.vue'),
              meta: { title: '购买分析', adminOnly: true },
            },
          ],
        },
        {
          path: 'staff-accounts',
          name: 'StaffAccount',
          component: () => import('../views/seller/index.vue'),
          meta: { title: '子账号管理', icon: 'User' },
          children: [
            {
              path: 'staff-account',
              name: 'ManageStaff',
              component: () => import('../views/seller/staff-account.vue'),
              meta: { title: '店铺子账号', activeMenu: '/staff-accounts' },
            },
          ],
        },
        {
          path: 'admin-accounts',
          name: 'AdminAccount',
          component: () => import('../views/admin-account/index.vue'),
          meta: { title: '平台管理员账号', icon: 'UserFilled', adminOnly: true },
          children: [
            {
              path: 'list',
              name: 'AdminAccountList',
              component: () => import('../views/admin-account/AdminList.vue'),
              meta: { title: '管理员账号管理', activeMenu: '/admin-accounts', adminOnly: true },
            },
          ],
        },
        {
          path: 'commission',
          name: 'Commission',
          component: () => import('../views/commission/index.vue'),
          meta: { title: '佣金模式', adminOnly: true },
          children: [
            {
              path: 'leader-list',
              name: 'LeaderList',
              component: () => import('../views/commission/LeaderList.vue'),
              meta: { title: '团长管理', adminOnly: true },
            },
            {
              path: 'add-leader',
              name: 'AddLeader',
              component: () => import('../views/commission/AddLeader.vue'),
              meta: { title: '添加团长', adminOnly: true },
            },
            {
              path: 'edit-leader/:id',
              name: 'EditLeader',
              component: () => import('../views/commission/EditLeader.vue'),
              meta: { title: '编辑团长', adminOnly: true, hidden: true },
            },
            {
              path: 'commission-settings',
              name: 'CommissionSettings',
              component: () => import('../views/commission/CommissionSettings.vue'),
              meta: { title: '佣金设置', adminOnly: true },
            },
            {
              path: 'invitation-codes',
              name: 'InvitationCodes',
              component: () => import('../views/commission/InvitationCodes.vue'),
              meta: { title: '邀请码管理', adminOnly: true },
            },
            {
              path: 'commission-statistics',
              name: 'CommissionStatistics',
              component: () => import('../views/commission/CommissionStatistics.vue'),
              meta: { title: '佣金统计', adminOnly: true },
            },
          ],
        },
        {
          path: 'order',
          name: 'Order',
          component: () => import('../views/order/index.vue'),
          meta: { title: '订单管理' },
          children: [
            {
              path: 'list',
              name: 'OrderList',
              component: () => import('../views/order/OrderList.vue'),
              meta: { title: '订单列表' },
            },
            {
              path: 'processing',
              name: 'OrderProcessing',
              component: () => import('../views/order/OrderProcessing.vue'),
              meta: { title: '处理中订单' },
            },
            {
              path: 'completed',
              name: 'OrderCompleted',
              component: () => import('../views/order/OrderCompleted.vue'),
              meta: { title: '已完成订单' },
            },
            {
              path: 'refund',
              name: 'OrderRefund',
              component: () => import('../views/order/OrderRefund.vue'),
              meta: { title: '退款处理' },
            },
            {
              path: 'statistics',
              name: 'OrderStatistics',
              component: () => import('../views/order/OrderStatistics.vue'),
              meta: { title: '订单统计' },
            },
            {
              path: 'statistics-test',
              name: 'StatisticsTest',
              component: () => import('../views/order/StatisticsTest.vue'),
              meta: { title: '统计功能测试' },
            },
            {
              path: 'test',
              name: 'OrderTest',
              component: () => import('../views/order/OrderTest.vue'),
              meta: { title: '订单功能测试' },
            },
            {
              path: 'courier-fix',
              name: 'CourierSelectFix',
              component: () => import('../views/order/components/CourierSelectFix.vue'),
              meta: { title: '快递公司选择修复测试' },
            },
            {
              path: 'ship-test',
              name: 'ShipOrderTest',
              component: () => import('../views/order/components/ShipOrderTest.vue'),
              meta: { title: '发货功能测试' },
            },
            {
              path: 'courier-test',
              name: 'CourierSelectTest',
              component: () => import('../views/order/components/CourierSelectTest.vue'),
              meta: { title: '快递公司选择测试' },
            },
          ],
        },
        {
          path: 'refund',
          name: 'Refund',
          component: () => import('../views/refund/index.vue'),
          meta: { title: '退款管理' },
          children: [
            {
              path: 'list',
              name: 'RefundList',
              component: () => import('../views/refund/RefundList.vue'),
              meta: { title: '退款列表' },
            },
            {
              path: 'pending',
              name: 'RefundPending',
              component: () => import('../views/refund/RefundPending.vue'),
              meta: { title: '待审核退款' },
            },
            {
              path: 'test',
              name: 'RefundTest',
              component: () => import('../views/refund/RefundTest.vue'),
              meta: { title: '退款功能测试' },
            },
          ],
        },
        {
          path: 'tracking',
          name: 'Tracking',
          component: () => import('../views/tracking/index.vue'),
          meta: { title: '物流管理' },
          children: [
            {
              path: 'list',
              name: 'TrackingList',
              component: () => import('../views/tracking/TrackingList.vue'),
              meta: { title: '物流列表' },
            },
            {
              path: 'register',
              name: 'TrackingRegister',
              component: () => import('../views/tracking/TrackingRegister.vue'),
              meta: { title: '注册物流' },
            },
          ],
        },
        {
          path: 'settlement',
          name: 'Settlement',
          component: () => import('../views/settlement/index.vue'),
          meta: { title: '回款管理' },
          children: [
            {
              path: 'merchant',
              name: 'MerchantSettlement',
              component: () => import('../views/settlement/MerchantSettlement.vue'),
              meta: { title: '回款信息', roles: ['merchant'] },
            },
            {
              path: 'admin',
              name: 'AdminSettlement',
              component: () => import('../views/settlement/AdminSettlement.vue'),
              meta: { title: '回款管理', roles: ['admin'] },
            },
          ],
        },
        {
          path: 'message',
          name: 'Message',
          component: () => import('../views/message/index.vue'),
          meta: { title: '站内信' },
          children: [
            {
              path: 'list',
              name: 'MessageList',
              component: () => import('../views/message/MessageList.vue'),
              meta: { title: '消息管理' },
            },
          ],
        },
        {
          path: 'seller',
          name: 'Seller',
          component: () => import('../views/seller/index.vue'),
          meta: { title: '商家管理', icon: 'Shop' },
          children: [
            {
              path: 'audit',
              name: 'SellerAudit',
              component: () => import('../views/seller/audit.vue'),
              meta: { title: '商家审核', activeMenu: '/seller' },
            },
            {
              path: 'detail/:id',
              name: 'SellerDetail',
              component: () => import('../views/seller/detail.vue'),
              meta: { title: '商家详情', activeMenu: '/seller/audit', hidden: true },
            },
          ],
        },
        {
          path: 'permission',
          name: 'Permission',
          component: () => import('../views/permission/index.vue'),
          meta: { title: '权限管理' },
          children: [
            {
              path: 'seller',
              name: 'SellerPermission',
              component: () => import('../views/permission/SellerPermission.vue'),
              meta: { title: '商家权限' },
            },
            {
              path: 'rules',
              name: 'RuleManagement',
              component: () => import('../views/permission/RuleManagement.vue'),
              meta: { title: '权限规则' },
            },
          ],
        },
        {
          path: 'category',
          name: 'Category',
          component: () => import('../views/category/index.vue'),
          children: [
            {
              path: 'list',
              name: 'CategoryList',
              component: () => import('../views/category/list.vue'),
            },
          ],
        },
        {
          path: 'customer',
          name: 'customer',
          redirect: '/main/customer/list',
          children: [
            {
              path: 'list',
              name: 'customer-list',
              component: () => import('../views/customer/list.vue'),
            },
          ],
        },
        {
          path: 'marketing',
          name: 'marketing',
          redirect: '/main/marketing/coupons',
          children: [
            {
              path: 'coupons',
              name: 'marketing-coupons',
              component: () => import('../views/marketing/coupons.vue'),
            },
            {
              path: 'discounts',
              name: 'marketing-discounts',
              component: () => import('../views/marketing/discounts.vue'),
            },
          ],
        },
        {
          path: 'user',
          name: 'User',
          component: () => import('../views/user/index.vue'),
          children: [
            {
              path: 'profile',
              name: 'UserProfile',
              component: () => import('../views/user/profile.vue'),
              meta: { title: '个人信息' },
            },
            {
              path: 'settings',
              name: 'UserSettings',
              component: () => import('../views/user/settings.vue'),
              meta: { title: '账户设置' },
            },
          ],
        },
        {
          path: 'help',
          name: 'help',
          component: () => import('../views/help/index.vue'),
          children: [
            {
              path: 'docs',
              name: 'help-docs',
              component: () => import('../views/help/docs.vue'),
              meta: { title: '帮助文档' },
            },
          ],
        },
        {
          path: 'commission-test',
          name: 'CommissionTest',
          component: () => import('../views/commission-test.vue'),
          meta: { title: '佣金模式测试页面' },
        },
      ],
    },
  ],
})

// 添加全局前置守卫检查是否需要登录
router.beforeEach((to, from, next) => {
  const token = getToken()
  const isValidToken = token && !isTokenExpired()

  console.log('路由守卫: ', to.path, from.path)

  // 如果路由需要认证
  if (to.meta.requiresAuth) {
    if (!isValidToken) {
      // 没有有效token，重定向到登录页
      next({ name: 'login' })
    } else {
      // 检查是否需要超级管理员权限
      if (to.meta.adminOnly) {
        const userStore = useUserStore()
        console.log('检查管理员权限:', userStore.userInfo?.role)

      }

      // 从本地存储获取默认页面设置
      const defaultPage = localStorage.getItem('defaultPage')

      // 如果是从根路径进入系统（首次登录后的跳转）
      if (from.path === '/' && to.path === '/main') {
        // 根据用户设置的默认页面进行重定向
        if (defaultPage && defaultPage !== 'welcome') {
          next({ path: `/main/${defaultPage}` })
        } else {
          // 默认进入欢迎页面
          next()
        }
      } else {
        next()
      }
    }
  } else if (to.name === 'login' && isValidToken) {
    // 已登录用户尝试访问登录页，重定向到主页
    next({ path: '/main/welcome' })
  } else {
    next()
  }
})

export default router
