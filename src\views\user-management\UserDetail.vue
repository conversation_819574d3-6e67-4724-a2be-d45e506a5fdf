<template>
  <div class="user-detail-container" v-loading="loading">
    <div class="page-header">
      <div class="header-left">
        <el-button @click="goBack" icon="ArrowLeft">返回</el-button>
        <h2>用户详情</h2>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="refreshData">刷新数据</el-button>
      </div>
    </div>

    <div class="user-detail-content" v-if="userDetail">
      <el-card class="user-info-card">
        <div class="user-profile">
          <div class="avatar-section">
            <el-avatar
              :size="80"
              :src="
                userDetail.avatar ||
                'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'
              "
            ></el-avatar>
            <div class="user-status" :class="{ active: userDetail.status === '活跃' }">
              {{ userDetail.status }}
            </div>
          </div>

          <div class="user-basic-info">
            <h3>{{ userDetail.username }}</h3>
            <div class="user-id">ID: {{ userDetail.id }}</div>
            <div class="user-contact">
              <p>
                <el-icon><Phone /></el-icon> {{ userDetail.phone }}
              </p>
              <p>
                <el-icon><Message /></el-icon> {{ userDetail.email }}
              </p>
            </div>
          </div>

          <div class="user-stats">
            <div class="stat-item">
              <div class="stat-value">{{ userDetail.orderCount }}</div>
              <div class="stat-label">总订单</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">¥{{ userDetail.totalSpent.toFixed(2) }}</div>
              <div class="stat-label">总消费</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">¥{{ userDetail.avgOrderValue.toFixed(2) }}</div>
              <div class="stat-label">平均订单</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ userDetail.lastOrderDays }}天</div>
              <div class="stat-label">上次购买</div>
            </div>
          </div>
        </div>

        <el-divider></el-divider>

        <div class="detail-section">
          <div class="section-header">
            <h4>用户资料</h4>
          </div>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="注册时间">{{
              userDetail.registerTime
            }}</el-descriptions-item>
            <el-descriptions-item label="最近登录">{{
              userDetail.lastLoginTime
            }}</el-descriptions-item>
            <el-descriptions-item label="注册来源">{{
              userDetail.registerSource
            }}</el-descriptions-item>
            <el-descriptions-item label="用户等级">{{ userDetail.userLevel }}</el-descriptions-item>
            <el-descriptions-item label="性别">{{ userDetail.gender }}</el-descriptions-item>
            <el-descriptions-item label="年龄">{{ userDetail.age }}</el-descriptions-item>
            <el-descriptions-item label="地区">{{ userDetail.region }}</el-descriptions-item>
            <el-descriptions-item label="用户类型">{{ userDetail.userType }}</el-descriptions-item>
          </el-descriptions>
        </div>
      </el-card>

      <el-tabs v-model="activeTab" class="detail-tabs">
        <el-tab-pane label="购买记录" name="purchases">
          <el-card class="tab-card">
            <template #header>
              <div class="card-header">
                <span>购买历史</span>
                <div class="filter-section">
                  <el-select
                    v-model="purchaseFilter"
                    placeholder="筛选"
                    style="width: 150px"
                    @change="filterPurchases"
                  >
                    <el-option label="全部订单" value="all"></el-option>
                    <el-option label="最近30天" value="last30days"></el-option>
                    <el-option label="最近90天" value="last90days"></el-option>
                    <el-option label="今年内" value="thisYear"></el-option>
                  </el-select>
                </div>
              </div>
            </template>

            <el-table :data="filteredPurchases" border stripe>
              <el-table-column prop="orderId" label="订单编号" width="180"></el-table-column>
              <el-table-column prop="orderTime" label="下单时间" width="180"></el-table-column>
              <el-table-column prop="productName" label="商品名称"></el-table-column>
              <el-table-column prop="merchantName" label="商家"></el-table-column>
              <el-table-column prop="quantity" label="数量" width="80"></el-table-column>
              <el-table-column prop="price" label="金额" width="100">
                <template #default="scope"> ¥{{ scope.row.price.toFixed(2) }} </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="100">
                <template #default="scope">
                  <el-tag :type="getStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
                </template>
              </el-table-column>
            </el-table>

            <div class="purchase-summary">
              <div>
                总订单: <strong>{{ filteredPurchases.length }}</strong> 笔
              </div>
              <div>
                总金额: <strong>¥{{ getTotalAmount() }}</strong>
              </div>
            </div>
          </el-card>
        </el-tab-pane>

        <el-tab-pane label="购买分析" name="analysis">
          <el-card class="tab-card">
            <template #header>
              <div class="card-header">
                <span>购买趋势分析</span>
                <div class="time-period">最近12个月</div>
              </div>
            </template>
            <div class="chart-container" ref="trendChartRef"></div>
          </el-card>

          <div class="chart-grid">
            <el-card class="tab-card">
              <template #header>
                <div class="card-header">
                  <span>商家购买分布</span>
                </div>
              </template>
              <div class="chart-container" ref="merchantChartRef"></div>
            </el-card>

            <el-card class="tab-card">
              <template #header>
                <div class="card-header">
                  <span>商品类别偏好</span>
                </div>
              </template>
              <div class="chart-container" ref="categoryChartRef"></div>
            </el-card>
          </div>
        </el-tab-pane>

        <el-tab-pane label="浏览记录" name="browsing">
          <el-card class="tab-card">
            <el-empty description="暂无浏览记录数据"></el-empty>
          </el-card>
        </el-tab-pane>
      </el-tabs>
    </div>

    <el-empty v-else description="未找到用户数据"></el-empty>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, watch, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Phone, Message } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

defineOptions({
  name: 'UserDetail',
})

const route = useRoute()
const router = useRouter()
const userId = ref(route.params.id)
const loading = ref(true)
const userDetail = ref(null)
const activeTab = ref('purchases')
const purchaseFilter = ref('all')

// 图表相关
const trendChartRef = ref(null)
const merchantChartRef = ref(null)
const categoryChartRef = ref(null)
let trendChart = null
let merchantChart = null
let categoryChart = null

// 原始购买数据
const purchaseData = ref([])
// 过滤后的购买数据
const filteredPurchases = ref([])

// 获取用户详情
const loadUserDetail = async () => {
  loading.value = true
  try {
    // 在实际项目中会调用API
    // const res = await getUserDetail(userId.value)

    // 模拟数据加载延迟
    await new Promise((resolve) => setTimeout(resolve, 800))

    // 模拟用户详情数据
    userDetail.value = {
      id: userId.value,
      username: '张三',
      phone: '13800138001',
      email: '<EMAIL>',
      avatar: '',
      status: '活跃', // '活跃', '非活跃', '新用户'
      orderCount: 12,
      totalSpent: 2845.5,
      avgOrderValue: 237.13,
      lastOrderDays: 5,
      registerTime: '2023-01-15 10:32:41',
      lastLoginTime: '2023-04-08 08:45:22',
      registerSource: '官网注册',
      userLevel: '黄金会员',
      gender: '男',
      age: 32,
      region: '广东省广州市',
      userType: '普通用户',
    }

    // 模拟购买记录数据
    purchaseData.value = [
      {
        orderId: 'ORDER20230405001',
        orderTime: '2023-04-05 14:22:18',
        productName: '高品质纯棉T恤',
        merchantName: '优品服饰旗舰店',
        quantity: 2,
        price: 199.0,
        status: '已完成',
      },
      {
        orderId: 'ORDER20230328002',
        orderTime: '2023-03-28 09:15:37',
        productName: '智能手表',
        merchantName: '电子科技专卖店',
        quantity: 1,
        price: 599.0,
        status: '已完成',
      },
      {
        orderId: 'ORDER20230315003',
        orderTime: '2023-03-15 16:48:22',
        productName: '家用加湿器',
        merchantName: '家居日用生活馆',
        quantity: 1,
        price: 149.9,
        status: '已完成',
      },
      {
        orderId: 'ORDER20230302004',
        orderTime: '2023-03-02 11:27:59',
        productName: '护肤套装',
        merchantName: '美妆护肤旗舰店',
        quantity: 1,
        price: 328.0,
        status: '已完成',
      },
      {
        orderId: 'ORDER20230220005',
        orderTime: '2023-02-20 13:41:05',
        productName: '儿童益智玩具',
        merchantName: '母婴用品专营店',
        quantity: 3,
        price: 245.7,
        status: '已完成',
      },
      {
        orderId: 'ORDER20230208006',
        orderTime: '2023-02-08 09:08:45',
        productName: '坚果零食大礼包',
        merchantName: '零食小吃旗舰店',
        quantity: 2,
        price: 158.0,
        status: '已完成',
      },
      {
        orderId: 'ORDER20230120007',
        orderTime: '2023-01-20 15:33:27',
        productName: '运动跑鞋',
        merchantName: '运动户外专营店',
        quantity: 1,
        price: 399.0,
        status: '已完成',
      },
      {
        orderId: 'ORDER20230110008',
        orderTime: '2023-01-10 16:22:18',
        productName: '畅销小说集',
        merchantName: '图书文具专卖店',
        quantity: 4,
        price: 156.8,
        status: '已完成',
      },
      {
        orderId: 'ORDER20221222009',
        orderTime: '2022-12-22 14:15:37',
        productName: '高端蓝牙耳机',
        merchantName: '电子科技专卖店',
        quantity: 1,
        price: 299.0,
        status: '已完成',
      },
      {
        orderId: 'ORDER20221205010',
        orderTime: '2022-12-05 09:42:09',
        productName: '冬季保暖外套',
        merchantName: '优品服饰旗舰店',
        quantity: 1,
        price: 458.0,
        status: '已完成',
      },
      {
        orderId: 'ORDER20221115011',
        orderTime: '2022-11-15 18:28:51',
        productName: '厨房收纳架',
        merchantName: '家居日用生活馆',
        quantity: 2,
        price: 128.0,
        status: '已完成',
      },
      {
        orderId: 'ORDER20221028012',
        orderTime: '2022-10-28 10:15:22',
        productName: '化妆刷套装',
        merchantName: '美妆护肤旗舰店',
        quantity: 1,
        price: 125.0,
        status: '已完成',
      },
    ]

    // 初始化过滤后的数据
    filterPurchases()

    // 初始化图表
    nextTick(() => {
      if (activeTab.value === 'analysis') {
        initCharts()
      }
    })
  } catch (error) {
    console.error('获取用户详情失败:', error)
    ElMessage.error('获取用户详情失败')
  } finally {
    loading.value = false
  }
}

// 根据订单状态获取标签类型
const getStatusType = (status) => {
  switch (status) {
    case '已完成':
      return 'success'
    case '进行中':
      return 'primary'
    case '已取消':
      return 'danger'
    case '待付款':
      return 'warning'
    default:
      return 'info'
  }
}

// 过滤购买记录
const filterPurchases = () => {
  if (purchaseFilter.value === 'all') {
    filteredPurchases.value = [...purchaseData.value]
    return
  }

  const now = new Date()
  let startDate

  switch (purchaseFilter.value) {
    case 'last30days':
      startDate = new Date()
      startDate.setDate(now.getDate() - 30)
      break
    case 'last90days':
      startDate = new Date()
      startDate.setDate(now.getDate() - 90)
      break
    case 'thisYear':
      startDate = new Date(now.getFullYear(), 0, 1) // 1月1日
      break
    default:
      startDate = new Date(0) // 1970年
  }

  filteredPurchases.value = purchaseData.value.filter((order) => {
    const orderDate = new Date(order.orderTime)
    return orderDate >= startDate
  })
}

// 计算总金额
const getTotalAmount = () => {
  return filteredPurchases.value.reduce((total, order) => total + order.price, 0).toFixed(2)
}

// 初始化图表
const initCharts = () => {
  // 趋势图表
  if (trendChartRef.value) {
    trendChart = echarts.init(trendChartRef.value)
    const months = getLast12Months()

    const trendOption = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      legend: {
        data: ['订单数', '消费金额'],
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          data: months,
          axisTick: {
            alignWithLabel: true,
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          name: '订单数',
          position: 'left',
        },
        {
          type: 'value',
          name: '金额',
          position: 'right',
          axisLabel: {
            formatter: '¥{value}',
          },
        },
      ],
      series: [
        {
          name: '订单数',
          type: 'bar',
          data: [1, 2, 0, 1, 3, 0, 1, 2, 0, 1, 0, 1],
        },
        {
          name: '消费金额',
          type: 'line',
          yAxisIndex: 1,
          data: [299, 657, 0, 399, 731.7, 0, 158, 245.7, 0, 328, 0, 199],
        },
      ],
    }

    trendChart.setOption(trendOption)
  }

  // 商家购买分布图表
  if (merchantChartRef.value) {
    merchantChart = echarts.init(merchantChartRef.value)

    // 统计每个商家的订单数
    const merchantStats = {}
    purchaseData.value.forEach((order) => {
      if (!merchantStats[order.merchantName]) {
        merchantStats[order.merchantName] = 0
      }
      merchantStats[order.merchantName]++
    })

    const merchantData = Object.entries(merchantStats).map(([name, value]) => ({
      name,
      value,
    }))

    const merchantOption = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)',
      },
      legend: {
        orient: 'vertical',
        left: 10,
        data: merchantData.map((item) => item.name),
      },
      series: [
        {
          name: '商家分布',
          type: 'pie',
          radius: ['50%', '70%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center',
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 16,
              fontWeight: 'bold',
            },
          },
          labelLine: {
            show: false,
          },
          data: merchantData,
        },
      ],
    }

    merchantChart.setOption(merchantOption)
  }

  // 商品类别偏好图表
  if (categoryChartRef.value) {
    categoryChart = echarts.init(categoryChartRef.value)

    // 模拟商品类别数据
    const categoryData = [
      { value: 4, name: '服装鞋帽' },
      { value: 2, name: '电子产品' },
      { value: 2, name: '家居用品' },
      { value: 2, name: '美妆护肤' },
      { value: 1, name: '母婴用品' },
      { value: 1, name: '零食饮品' },
    ]

    const categoryOption = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)',
      },
      legend: {
        orient: 'vertical',
        right: 10,
        data: categoryData.map((item) => item.name),
      },
      series: [
        {
          name: '商品类别',
          type: 'pie',
          radius: ['30%', '50%'],
          center: ['40%', '50%'],
          data: categoryData.sort((a, b) => a.value - b.value),
          roseType: 'radius',
          label: {
            formatter: '{b}: {d}%',
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
        },
      ],
    }

    categoryChart.setOption(categoryOption)
  }
}

// 获取过去12个月的月份标签
const getLast12Months = () => {
  const months = []
  const now = new Date()
  const currentMonth = now.getMonth()

  for (let i = 11; i >= 0; i--) {
    const monthIndex = (currentMonth - i + 12) % 12
    months.push(monthIndex + 1 + '月')
  }

  return months
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 刷新数据
const refreshData = () => {
  loadUserDetail()
}

// 监听tab变化，初始化对应图表
watch(activeTab, (newVal) => {
  if (newVal === 'analysis') {
    nextTick(() => {
      initCharts()
    })
  }
})

// 监听路由参数变化
watch(
  () => route.params.id,
  (newId) => {
    if (newId !== userId.value) {
      userId.value = newId
      loadUserDetail()
    }
  },
)

// 监听查询参数变化
watch(
  () => route.query.tab,
  (newTab) => {
    if (newTab) {
      activeTab.value = newTab
    }
  },
)

// 处理窗口大小变化
const handleResize = () => {
  trendChart?.resize()
  merchantChart?.resize()
  categoryChart?.resize()
}

// 生命周期钩子
onMounted(() => {
  if (route.query.tab) {
    activeTab.value = route.query.tab
  }

  loadUserDetail()
  window.addEventListener('resize', handleResize)
})

// 组件卸载前清理
const onBeforeUnmount = () => {
  window.removeEventListener('resize', handleResize)
  trendChart?.dispose()
  merchantChart?.dispose()
  categoryChart?.dispose()
}
</script>

<style scoped lang="scss">
.user-detail-container {
  padding: 20px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .header-left {
      display: flex;
      align-items: center;
      gap: 15px;

      h2 {
        margin: 0;
      }
    }
  }

  .user-detail-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .user-info-card {
    .user-profile {
      display: flex;
      gap: 20px;
      margin-bottom: 20px;

      @media (max-width: 768px) {
        flex-direction: column;
      }

      .avatar-section {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px;

        .user-status {
          font-size: 12px;
          padding: 3px 8px;
          border-radius: 10px;
          background-color: #909399;
          color: white;

          &.active {
            background-color: #67c23a;
          }
        }
      }

      .user-basic-info {
        flex: 1;

        h3 {
          margin-top: 0;
          margin-bottom: 5px;
          font-size: 22px;
        }

        .user-id {
          color: #909399;
          margin-bottom: 15px;
          font-size: 14px;
        }

        .user-contact {
          color: #606266;

          p {
            margin: 5px 0;
            display: flex;
            align-items: center;
            gap: 10px;
          }
        }
      }

      .user-stats {
        display: flex;
        gap: 15px;
        margin-left: auto;

        @media (max-width: 768px) {
          margin-left: 0;
          width: 100%;
          justify-content: space-between;
        }

        .stat-item {
          text-align: center;
          min-width: 80px;

          .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #409eff;
          }

          .stat-label {
            font-size: 12px;
            color: #909399;
          }

          &:nth-child(2) .stat-value {
            color: #67c23a;
          }

          &:nth-child(3) .stat-value {
            color: #e6a23c;
          }

          &:nth-child(4) .stat-value {
            color: #f56c6c;
          }
        }
      }
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;

      h4 {
        margin: 0;
        font-size: 16px;
      }
    }
  }

  .detail-tabs {
    margin-top: 10px;

    .tab-card {
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .filter-section {
        display: flex;
        gap: 10px;
      }

      .purchase-summary {
        display: flex;
        justify-content: flex-end;
        margin-top: 15px;
        gap: 20px;
        font-size: 14px;
      }
    }
  }

  .chart-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 20px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  .chart-container {
    height: 350px;
    width: 100%;
  }

  .time-period {
    font-size: 14px;
    color: #909399;
  }
}
</style>
