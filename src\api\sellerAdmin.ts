import request from '@/utils/request'

// 商家基本信息接口
export interface Seller {
  id: number
  accountName: string
  gender: number
  phone: string
  email: string
  accountStatus: number
  photoUrl?: string
  createTime: string
  shopName?: string
  companyName?: string
  contactPerson?: string
  contactPhone?: string
  businessLicense?: string
  licenseValidity?: string
  companyIntro?: string
  status?: number
  updateTime?: string
  password?: string
  verificationCode?: string
  lastLoginTime?: string
  roles?: any[]
  // 其他可能的字段
}

// 企业认证信息接口
export interface EVPI {
  id: number
  shopName: string
  companyName: string
  businessLicense: string
  licenseValidity: string
  companyIntro: string
  contactPerson: string
  contactPhone: string
  province: string
  city: string
  district: string
  addressDetail: string
  createdUser?: string
  createdTime: string
  lastUpdated: string
  businessImgUrl: string
  warehouseImgUrl1: string
  warehouseImgUrl2: string
  warehouseImgUrl3: string
  sellerId: number
  idcard1: string
  idcard2: string
  idcard?: string
}

// 商家详情响应接口
export interface SellerDetailResponse {
  id?: number
  seller: Seller
  evpi: EVPI
}

// 获取所有卖家列表
export function getAllSellers() {
  return request({
    url: '/sellers/All',
    method: 'get',
  })
}

// 获取商家基本信息（轻量版，用于常规显示）
export function getSellerBasicInfo(id: number) {
  return request({
    url: `/seller/${id}`,
    method: 'get',
  }) as Promise<{
    code: number
    msg: string | null
    data: Seller
  }>
}

// 获取指定ID的商家详细信息（包含认证信息，用于审核）
export function getSellerById(id: number) {
  return request({
    url: `/sellers/${id}`,
    method: 'get',
  }) as Promise<{
    code: number
    msg: string | null
    data: SellerDetailResponse
  }>
}

// 更新卖家状态
export function updateSellerStatus(id: number, status: number, remarks: string) {
  return request({
    url: `/sellers/update`,
    method: 'put',
    data: { id, accountStatus: status, remarks },
  })
}

// 删除指定ID的卖家
export function deleteSellerById(id: number) {
  return request({
    url: `/sellers/delete/${id}`,
    method: 'delete',
  })
}

// 批量删除卖家
export function batchDeleteSellers(ids: number[]) {
  const promises = ids.map((id) => deleteSellerById(id))
  return Promise.all(promises)
}
