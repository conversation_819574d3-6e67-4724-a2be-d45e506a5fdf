/**
 * 物流跟踪管理API接口
 */

import request from '@/utils/request'
import type {
  RegisterTrackingDTO,
  TrackingQueryDTO,
  TrackingRecord,
  TrackingDetailVO,
  Carrier
} from '@/types/tracking'

// ==================== 后台物流管理接口 ====================

/**
 * 注册物流单号
 */
export const registerTracking = (data: RegisterTrackingDTO): Promise<{ code: number; data: TrackingRecord; msg?: string }> => {
  return request.post('/admin/tracking/register', data)
}

/**
 * 批量注册物流单号
 */
export const batchRegisterTracking = (data: RegisterTrackingDTO[]): Promise<{ code: number; data: TrackingRecord[]; msg?: string }> => {
  return request.post('/admin/tracking/batch-register', data)
}

/**
 * 查询物流详情
 */
export const getTrackingDetail = (trackingNumber: string, carrierCode: string): Promise<{ code: number; data: TrackingDetailVO; msg?: string }> => {
  return request.get('/admin/tracking/detail', {
    params: { trackingNumber, carrierCode }
  })
}

/**
 * 根据订单ID查询物流信息
 */
export const getTrackingByOrderId = (orderId: number): Promise<{ code: number; data: TrackingDetailVO[]; msg?: string }> => {
  return request.get(`/admin/tracking/order/${orderId}`)
}

/**
 * 查询跟踪列表
 */
export const getTrackingList = (data: TrackingQueryDTO): Promise<{ code: number; data: TrackingRecord[]; msg?: string }> => {
  return request.post('/admin/tracking/list', data)
}

/**
 * 获取运输商列表
 */
export const getCarriers = (): Promise<{ code: number; data: Carrier[]; msg?: string }> => {
  return request.get('/admin/tracking/carriers')
}

// ==================== 前台物流查询接口 ====================

/**
 * 用户查询物流详情
 */
export const getUserTrackingDetail = (trackingNumber: string, carrierCode: string): Promise<{ code: number; data: TrackingDetailVO; msg?: string }> => {
  return request.get('/user/tracking/detail', {
    params: { trackingNumber, carrierCode }
  })
}

/**
 * 用户根据订单ID查询物流信息
 */
export const getUserTrackingByOrderId = (orderId: number): Promise<{ code: number; data: TrackingDetailVO[]; msg?: string }> => {
  return request.get(`/user/tracking/order/${orderId}`)
}

/**
 * 获取常用运输商列表
 */
export const getUserCarriers = (): Promise<{ code: number; data: Carrier[]; msg?: string }> => {
  return request.get('/user/tracking/carriers')
}

// ==================== 辅助功能 ====================

/**
 * 同步物流状态
 */
export const syncTrackingStatus = (trackingNumber: string, carrierCode: string): Promise<{ code: number; data: any; msg?: string }> => {
  return request.post('/admin/tracking/sync', {
    trackingNumber,
    carrierCode
  })
}

/**
 * 批量同步物流状态
 */
export const batchSyncTrackingStatus = (trackingNumbers: string[]): Promise<{ code: number; data: any; msg?: string }> => {
  return request.post('/admin/tracking/batch-sync', {
    trackingNumbers
  })
}

/**
 * 停止跟踪
 */
export const stopTracking = (trackingNumber: string, carrierCode: string, reason?: string): Promise<{ code: number; data: any; msg?: string }> => {
  return request.post('/admin/tracking/stop', {
    trackingNumber,
    carrierCode,
    reason
  })
}

/**
 * 重新开始跟踪
 */
export const restartTracking = (trackingNumber: string, carrierCode: string): Promise<{ code: number; data: any; msg?: string }> => {
  return request.post('/admin/tracking/restart', {
    trackingNumber,
    carrierCode
  })
}

/**
 * 删除跟踪记录
 */
export const deleteTracking = (id: number): Promise<{ code: number; data: any; msg?: string }> => {
  return request.delete(`/admin/tracking/${id}`)
}

/**
 * 批量删除跟踪记录
 */
export const batchDeleteTracking = (ids: number[]): Promise<{ code: number; data: any; msg?: string }> => {
  return request.post('/admin/tracking/batch-delete', { ids })
}

// ==================== 数据处理函数 ====================

/**
 * 格式化物流查询参数
 */
export const formatTrackingQuery = (query: Partial<TrackingQueryDTO>): TrackingQueryDTO => {
  const formatted: TrackingQueryDTO = {
    page: query.page || 1,
    pageSize: query.pageSize || 20
  }

  // 只添加有值的参数
  if (query.trackingNumber) formatted.trackingNumber = query.trackingNumber
  if (query.carrierCode) formatted.carrierCode = query.carrierCode
  if (query.orderId) formatted.orderId = query.orderId
  if (query.status) formatted.status = query.status
  if (query.trackingStatus) formatted.trackingStatus = query.trackingStatus
  if (query.beginTime) formatted.beginTime = query.beginTime
  if (query.endTime) formatted.endTime = query.endTime

  return formatted
}

/**
 * 验证物流单号格式
 */
export const validateTrackingNumber = (trackingNumber: string, carrierCode: string): boolean => {
  if (!trackingNumber || !carrierCode) return false
  
  // 基本格式验证
  if (trackingNumber.length < 6 || trackingNumber.length > 50) return false
  
  // 只允许字母数字和部分特殊字符
  const regex = /^[A-Za-z0-9\-_]+$/
  return regex.test(trackingNumber)
}

/**
 * 获取物流状态颜色
 */
export const getTrackingStatusColor = (status: string): string => {
  const colorMap: Record<string, string> = {
    'Pending': '#909399',
    'InfoReceived': '#409eff',
    'InTransit': '#e6a23c',
    'OutForDelivery': '#f56c6c',
    'Delivered': '#67c23a',
    'Exception': '#f56c6c',
    'Expired': '#909399'
  }
  return colorMap[status] || '#909399'
}

/**
 * 导出物流数据
 */
export const exportTrackingData = (query: TrackingQueryDTO, format: string = 'excel'): Promise<{ code: number; data: string; msg?: string }> => {
  return request.post('/admin/tracking/export', query, {
    params: { format },
    responseType: 'blob'
  }).then(response => {
    // 处理文件下载
    const blob = new Blob([response.data])
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `物流数据_${new Date().toISOString().slice(0, 10)}.${format}`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    return {
      code: 1,
      data: '导出成功',
      msg: '文件已开始下载'
    }
  })
}

/**
 * 获取物流统计信息
 */
export const getTrackingStatistics = (startTime?: string, endTime?: string): Promise<{ code: number; data: any; msg?: string }> => {
  const params: any = {}
  if (startTime) params.startTime = startTime
  if (endTime) params.endTime = endTime
  
  return request.get('/admin/tracking/statistics', { params })
}

/**
 * 获取物流趋势数据
 */
export const getTrackingTrend = (days: number = 7): Promise<{ code: number; data: any; msg?: string }> => {
  return request.get('/admin/tracking/trend', {
    params: { days }
  })
}
