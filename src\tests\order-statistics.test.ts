/**
 * 订单统计功能测试
 * 测试所有API调用是否正确对应后端接口
 */

import { describe, it, expect, vi } from 'vitest'
import {
  getOrderStatisticsData,
  getOrderStatisticsByRange,
  getTodayStatistics,
  getMonthStatistics,
  getStatusDistribution,
  getEnhancedStatistics,
  getTimeDimensionStatistics,
  getPaymentMethodStatistics,
  getProductStatistics,
  getUserStatistics,
  getConversionRateStatistics,
  getRealtimeStatistics,
  getComparisonStatistics,
  getTrendStatistics
} from '@/api/order'

// Mock request module
vi.mock('@/utils/request', () => ({
  default: {
    get: vi.fn()
  }
}))

import request from '@/utils/request'

describe('订单统计API测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('应该调用正确的统计数据接口', async () => {
    const mockResponse = {
      code: 1,
      data: {
        totalOrders: 100,
        totalSales: 50000,
        todaySales: 1000,
        monthSales: 15000
      }
    }
    
    vi.mocked(request.get).mockResolvedValue(mockResponse)
    
    await getOrderStatisticsData()
    
    expect(request.get).toHaveBeenCalledWith('/admin/orders/statistics')
  })

  it('应该调用正确的日期范围统计接口', async () => {
    const mockResponse = {
      code: 1,
      data: {
        dailyStatistics: [
          { date: '2024-01-01', orderCount: 10, sales: 1000 },
          { date: '2024-01-02', orderCount: 15, sales: 1500 }
        ]
      }
    }
    
    vi.mocked(request.get).mockResolvedValue(mockResponse)
    
    await getOrderStatisticsByRange('2024-01-01', '2024-01-02')
    
    expect(request.get).toHaveBeenCalledWith('/admin/orders/statistics/range', {
      params: { beginDate: '2024-01-01', endDate: '2024-01-02' }
    })
  })

  it('应该调用正确的今日统计接口', async () => {
    const mockResponse = {
      code: 1,
      data: {
        orderCount: 25,
        sales: 2500
      }
    }
    
    vi.mocked(request.get).mockResolvedValue(mockResponse)
    
    await getTodayStatistics()
    
    expect(request.get).toHaveBeenCalledWith('/admin/orders/statistics/today')
  })

  it('应该调用正确的本月统计接口', async () => {
    const mockResponse = {
      code: 1,
      data: {
        orderCount: 300,
        sales: 30000
      }
    }
    
    vi.mocked(request.get).mockResolvedValue(mockResponse)
    
    await getMonthStatistics()
    
    expect(request.get).toHaveBeenCalledWith('/admin/orders/statistics/month')
  })

  it('应该调用正确的状态分布接口', async () => {
    const mockResponse = {
      code: 1,
      data: {
        pendingPayment: 10,
        paid: 20,
        processing: 15,
        shipped: 25,
        completed: 100,
        cancelled: 5,
        refunded: 2
      }
    }
    
    vi.mocked(request.get).mockResolvedValue(mockResponse)
    
    await getStatusDistribution()
    
    expect(request.get).toHaveBeenCalledWith('/admin/orders/statistics/status-distribution')
  })
})

describe('增强统计API测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('应该调用正确的增强统计接口', async () => {
    const mockResponse = {
      code: 1,
      data: {
        totalOrders: 188,
        totalSales: 15680.50,
        avgOrderAmount: 83.41,
        paymentRate: 85.5,
        completionRate: 92.3,
        cancellationRate: 4.3
      }
    }

    vi.mocked(request.get).mockResolvedValue(mockResponse)

    await getEnhancedStatistics('2024-01-01', '2024-01-31')

    expect(request.get).toHaveBeenCalledWith('/admin/orders/statistics/enhanced', {
      params: { beginDate: '2024-01-01', endDate: '2024-01-31' }
    })
  })

  it('应该调用正确的时间维度统计接口', async () => {
    const mockResponse = {
      code: 1,
      data: {
        today: { totalOrders: 12, totalSales: 980.50 },
        yesterday: { totalOrders: 15, totalSales: 1250.00 }
      }
    }

    vi.mocked(request.get).mockResolvedValue(mockResponse)

    await getTimeDimensionStatistics()

    expect(request.get).toHaveBeenCalledWith('/admin/orders/statistics/time-dimension')
  })

  it('应该调用正确的支付方式统计接口', async () => {
    const mockResponse = {
      code: 1,
      data: {
        paymentMethodStats: [
          { pay_method: 1, methodName: '微信支付', orderCount: 120, totalAmount: 10250.50 }
        ]
      }
    }

    vi.mocked(request.get).mockResolvedValue(mockResponse)

    await getPaymentMethodStatistics('2024-01-01', '2024-01-31')

    expect(request.get).toHaveBeenCalledWith('/admin/orders/statistics/payment-method', {
      params: { beginDate: '2024-01-01', endDate: '2024-01-31' }
    })
  })

  it('应该调用正确的商品统计接口', async () => {
    const mockResponse = {
      code: 1,
      data: {
        topProducts: [
          { product_name: '商品A', totalQuantity: 150, totalAmount: 3750.00, orderCount: 45 }
        ]
      }
    }

    vi.mocked(request.get).mockResolvedValue(mockResponse)

    await getProductStatistics('2024-01-01', '2024-01-31', 10)

    expect(request.get).toHaveBeenCalledWith('/admin/orders/statistics/product', {
      params: { beginDate: '2024-01-01', endDate: '2024-01-31', topLimit: 10 }
    })
  })

  it('应该调用正确的用户统计接口', async () => {
    const mockResponse = {
      code: 1,
      data: {
        newOldUserStats: [
          { userType: 'new', orderCount: 25, totalAmount: 2150.50 }
        ],
        userOrderFrequency: [
          { frequency_range: '1次', userCount: 45 }
        ]
      }
    }

    vi.mocked(request.get).mockResolvedValue(mockResponse)

    await getUserStatistics('2024-01-01', '2024-01-31')

    expect(request.get).toHaveBeenCalledWith('/admin/orders/statistics/user', {
      params: { beginDate: '2024-01-01', endDate: '2024-01-31' }
    })
  })

  it('应该调用正确的转化率统计接口', async () => {
    const mockResponse = {
      code: 1,
      data: {
        totalOrders: 188,
        paidOrders: 163,
        completedOrders: 150,
        cancelledOrders: 8,
        paymentRate: 86.70,
        completionRate: 92.02,
        cancellationRate: 4.26
      }
    }

    vi.mocked(request.get).mockResolvedValue(mockResponse)

    await getConversionRateStatistics('2024-01-01', '2024-01-31')

    expect(request.get).toHaveBeenCalledWith('/admin/orders/statistics/conversion-rate', {
      params: { beginDate: '2024-01-01', endDate: '2024-01-31' }
    })
  })

  it('应该调用正确的实时统计接口', async () => {
    const mockResponse = {
      code: 1,
      data: {
        totalOrders: 5,
        totalSales: 420.50,
        pendingOrders: 2,
        completedOrders: 3,
        timestamp: '2024-01-15 14:30:00'
      }
    }

    vi.mocked(request.get).mockResolvedValue(mockResponse)

    await getRealtimeStatistics()

    expect(request.get).toHaveBeenCalledWith('/admin/orders/statistics/realtime')
  })

  it('应该调用正确的对比统计接口', async () => {
    const mockResponse = {
      code: 1,
      data: {
        currentPeriod: { totalOrders: 188, totalSales: 15680.50 },
        lastYearPeriod: { totalOrders: 156, totalSales: 12850.00 },
        yearOverYearGrowth: { salesGrowthRate: 22.02, orderGrowthRate: 20.51 }
      }
    }

    vi.mocked(request.get).mockResolvedValue(mockResponse)

    await getComparisonStatistics('2024-01-01', '2024-01-31')

    expect(request.get).toHaveBeenCalledWith('/admin/orders/statistics/comparison', {
      params: { beginDate: '2024-01-01', endDate: '2024-01-31' }
    })
  })

  it('应该调用正确的趋势统计接口', async () => {
    const mockResponse = {
      code: 1,
      data: {
        dailyStatistics: [
          { date: '2024-01-01', amount: 1250.00, orderCount: 15 }
        ]
      }
    }

    vi.mocked(request.get).mockResolvedValue(mockResponse)

    await getTrendStatistics('2024-01-01', '2024-01-31', 'day')

    expect(request.get).toHaveBeenCalledWith('/admin/orders/statistics/trend', {
      params: { beginDate: '2024-01-01', endDate: '2024-01-31', granularity: 'day' }
    })
  })
})

describe('订单统计数据结构测试', () => {
  it('应该正确处理OrderStatisticsVO数据结构', () => {
    const mockData = {
      // 兼容性字段
      toBeConfirmed: 5,
      confirmed: 10,
      deliveryInProgress: 8,
      
      // 各状态订单数量
      pendingPayment: 12,
      paid: 25,
      processing: 18,
      shipped: 30,
      completed: 150,
      cancelled: 8,
      refunded: 3,
      
      // 总订单数和销售额
      totalOrders: 246,
      totalSales: 125000,
      todaySales: 3500,
      monthSales: 45000,
      
      // 状态分布
      statusDistribution: [
        { name: '待付款', value: 12 },
        { name: '已付款', value: 25 },
        { name: '处理中', value: 18 },
        { name: '已发货', value: 30 },
        { name: '已完成', value: 150 },
        { name: '已取消', value: 8 },
        { name: '已退款', value: 3 }
      ],
      
      // 时间段统计
      dailyStatistics: [
        { date: '2024-01-01', orderCount: 20, sales: 2000 },
        { date: '2024-01-02', orderCount: 25, sales: 2500 },
        { date: '2024-01-03', orderCount: 18, sales: 1800 }
      ],
      monthlyStatistics: [
        { month: '2024-01', orderCount: 500, sales: 50000 },
        { month: '2024-02', orderCount: 600, sales: 60000 }
      ]
    }
    
    // 验证数据结构完整性
    expect(mockData.totalOrders).toBe(246)
    expect(mockData.statusDistribution).toHaveLength(7)
    expect(mockData.dailyStatistics).toHaveLength(3)
    expect(mockData.monthlyStatistics).toHaveLength(2)
    
    // 验证状态数据
    const statusSum = mockData.pendingPayment + mockData.paid + 
                     mockData.processing + mockData.shipped + 
                     mockData.completed + mockData.cancelled + 
                     mockData.refunded
    expect(statusSum).toBe(246) // 应该等于总订单数
  })

  it('应该正确处理增强统计数据结构', () => {
    const mockEnhancedData = {
      totalOrders: 188,
      totalSales: 15680.50,
      avgOrderAmount: 83.41,
      paymentRate: 85.5,
      completionRate: 92.3,
      cancellationRate: 4.3,
      paymentMethodStats: [
        { pay_method: 1, methodName: '微信支付', orderCount: 120, totalAmount: 10250.50 }
      ],
      topProducts: [
        { product_name: '商品A', totalQuantity: 150, totalAmount: 3750.00, orderCount: 45 }
      ],
      newUserOrders: 25,
      oldUserOrders: 163,
      userOrderFrequency: [
        { frequency_range: '1次', userCount: 45 }
      ]
    }

    // 验证核心指标
    expect(mockEnhancedData.totalOrders).toBe(188)
    expect(mockEnhancedData.avgOrderAmount).toBeCloseTo(83.41)
    expect(mockEnhancedData.paymentRate).toBeCloseTo(85.5)

    // 验证数组数据
    expect(mockEnhancedData.paymentMethodStats).toHaveLength(1)
    expect(mockEnhancedData.topProducts).toHaveLength(1)
    expect(mockEnhancedData.userOrderFrequency).toHaveLength(1)

    // 验证用户分布
    expect(mockEnhancedData.newUserOrders + mockEnhancedData.oldUserOrders).toBe(188)
  })

  it('应该正确处理时间维度统计数据', () => {
    const mockTimeDimensionData = {
      today: { totalOrders: 12, validOrders: 10, totalSales: 980.50, avgOrderAmount: 81.71 },
      yesterday: { totalOrders: 15, validOrders: 14, totalSales: 1250.00, avgOrderAmount: 83.33 },
      thisWeek: { totalOrders: 85, validOrders: 80, totalSales: 7120.50, avgOrderAmount: 89.01 },
      lastWeek: { totalOrders: 78, validOrders: 72, totalSales: 6850.00, avgOrderAmount: 95.14 }
    }

    // 验证数据完整性
    expect(mockTimeDimensionData.today.totalOrders).toBe(12)
    expect(mockTimeDimensionData.yesterday.totalOrders).toBe(15)
    expect(mockTimeDimensionData.thisWeek.totalOrders).toBe(85)
    expect(mockTimeDimensionData.lastWeek.totalOrders).toBe(78)

    // 验证平均订单金额计算
    expect(mockTimeDimensionData.today.avgOrderAmount).toBeCloseTo(81.71)
    expect(mockTimeDimensionData.yesterday.avgOrderAmount).toBeCloseTo(83.33)
  })
})

describe('数据验证和错误处理测试', () => {
  it('应该正确处理API错误响应', async () => {
    const errorResponse = {
      code: 0,
      msg: 'error',
      data: null
    }

    vi.mocked(request.get).mockResolvedValue(errorResponse)

    const result = await getOrderStatisticsData()

    expect(result.code).toBe(0)
    expect(result.data).toBeNull()
  })

  it('应该正确处理网络错误', async () => {
    vi.mocked(request.get).mockRejectedValue(new Error('Network Error'))

    try {
      await getOrderStatisticsData()
    } catch (error) {
      expect(error).toBeInstanceOf(Error)
      expect((error as Error).message).toBe('Network Error')
    }
  })

  it('应该正确验证日期参数', () => {
    const validDate = '2024-01-01'
    const invalidDate = 'invalid-date'

    // 验证日期格式
    expect(/^\d{4}-\d{2}-\d{2}$/.test(validDate)).toBe(true)
    expect(/^\d{4}-\d{2}-\d{2}$/.test(invalidDate)).toBe(false)
  })

  it('应该正确处理空数据', () => {
    const emptyData = {
      totalOrders: 0,
      totalSales: 0,
      statusDistribution: [],
      dailyStatistics: []
    }

    expect(emptyData.totalOrders).toBe(0)
    expect(emptyData.statusDistribution).toHaveLength(0)
    expect(emptyData.dailyStatistics).toHaveLength(0)
  })
})

describe('工具函数测试', () => {
  it('应该正确格式化金额', () => {
    // 模拟formatAmount函数
    const formatAmount = (amount: number | undefined): string => {
      if (amount === undefined || amount === null || isNaN(amount)) return '0.00'
      return amount.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
    }

    expect(formatAmount(1234.56)).toBe('1,234.56')
    expect(formatAmount(0)).toBe('0.00')
    expect(formatAmount(undefined)).toBe('0.00')
    expect(formatAmount(NaN)).toBe('0.00')
  })

  it('应该正确计算百分比', () => {
    const calculatePercentage = (value: number, total: number): number => {
      return total > 0 ? (value / total) * 100 : 0
    }

    expect(calculatePercentage(25, 100)).toBe(25)
    expect(calculatePercentage(0, 100)).toBe(0)
    expect(calculatePercentage(50, 0)).toBe(0)
  })

  it('应该正确验证数据完整性', () => {
    const validateApiResponse = (response: any, dataType: string): boolean => {
      if (!response || response.code !== 1) return false
      if (!response.data) return false
      return true
    }

    expect(validateApiResponse({ code: 1, data: {} }, 'test')).toBe(true)
    expect(validateApiResponse({ code: 0, data: {} }, 'test')).toBe(false)
    expect(validateApiResponse({ code: 1, data: null }, 'test')).toBe(false)
    expect(validateApiResponse(null, 'test')).toBe(false)
  })
})
