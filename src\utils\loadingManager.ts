/**
 * 加载状态管理工具
 */

import { ref, type Ref } from 'vue'
import { ElLoading } from 'element-plus'

// 全局加载状态
const globalLoading = ref(false)
const loadingTasks = new Set<string>()

/**
 * 创建加载状态管理器
 */
export function useLoading(initialState = false) {
  const loading = ref(initialState)
  
  const setLoading = (state: boolean) => {
    loading.value = state
  }
  
  const withLoading = async <T>(
    asyncFn: () => Promise<T>,
    options: {
      showGlobalLoading?: boolean
      loadingText?: string
    } = {}
  ): Promise<T> => {
    const { showGlobalLoading = false, loadingText } = options
    
    try {
      loading.value = true
      
      if (showGlobalLoading) {
        setGlobalLoading(true, loadingText)
      }
      
      const result = await asyncFn()
      return result
    } finally {
      loading.value = false
      
      if (showGlobalLoading) {
        setGlobalLoading(false)
      }
    }
  }
  
  return {
    loading: loading as Readonly<Ref<boolean>>,
    setLoading,
    withLoading
  }
}

/**
 * 设置全局加载状态
 */
export function setGlobalLoading(state: boolean, text?: string) {
  globalLoading.value = state
  
  if (state && text) {
    ElLoading.service({
      lock: true,
      text,
      background: 'rgba(0, 0, 0, 0.7)'
    })
  } else if (!state) {
    const loadingInstance = ElLoading.service()
    loadingInstance?.close()
  }
}

/**
 * 获取全局加载状态
 */
export function getGlobalLoading() {
  return globalLoading.value
}

/**
 * 任务加载管理器
 */
export class TaskLoadingManager {
  private tasks = new Map<string, boolean>()
  private loading = ref(false)
  
  /**
   * 开始任务
   */
  startTask(taskId: string) {
    this.tasks.set(taskId, true)
    this.updateLoadingState()
  }
  
  /**
   * 完成任务
   */
  finishTask(taskId: string) {
    this.tasks.delete(taskId)
    this.updateLoadingState()
  }
  
  /**
   * 检查任务是否正在执行
   */
  isTaskRunning(taskId: string): boolean {
    return this.tasks.has(taskId)
  }
  
  /**
   * 获取正在执行的任务数量
   */
  getRunningTaskCount(): number {
    return this.tasks.size
  }
  
  /**
   * 获取加载状态
   */
  get isLoading(): Readonly<Ref<boolean>> {
    return this.loading
  }
  
  /**
   * 清除所有任务
   */
  clearAllTasks() {
    this.tasks.clear()
    this.updateLoadingState()
  }
  
  /**
   * 使用任务包装异步操作
   */
  async withTask<T>(
    taskId: string,
    asyncFn: () => Promise<T>,
    options: {
      showGlobalLoading?: boolean
      loadingText?: string
    } = {}
  ): Promise<T> {
    const { showGlobalLoading = false, loadingText } = options
    
    try {
      this.startTask(taskId)
      
      if (showGlobalLoading) {
        setGlobalLoading(true, loadingText)
      }
      
      const result = await asyncFn()
      return result
    } finally {
      this.finishTask(taskId)
      
      if (showGlobalLoading) {
        setGlobalLoading(false)
      }
    }
  }
  
  private updateLoadingState() {
    this.loading.value = this.tasks.size > 0
  }
}

/**
 * 创建任务加载管理器实例
 */
export function createTaskLoadingManager() {
  return new TaskLoadingManager()
}

/**
 * 全局任务加载管理器
 */
export const globalTaskManager = createTaskLoadingManager()

/**
 * 页面级加载状态管理
 */
export function usePageLoading() {
  const pageLoading = ref(false)
  const loadingTasks = ref(new Set<string>())
  
  const startPageTask = (taskId: string) => {
    loadingTasks.value.add(taskId)
    pageLoading.value = true
  }
  
  const finishPageTask = (taskId: string) => {
    loadingTasks.value.delete(taskId)
    pageLoading.value = loadingTasks.value.size > 0
  }
  
  const withPageLoading = async <T>(
    taskId: string,
    asyncFn: () => Promise<T>
  ): Promise<T> => {
    try {
      startPageTask(taskId)
      const result = await asyncFn()
      return result
    } finally {
      finishPageTask(taskId)
    }
  }
  
  return {
    pageLoading: pageLoading as Readonly<Ref<boolean>>,
    startPageTask,
    finishPageTask,
    withPageLoading
  }
}

/**
 * 按钮加载状态管理
 */
export function useButtonLoading() {
  const buttonStates = ref(new Map<string, boolean>())
  
  const setButtonLoading = (buttonId: string, loading: boolean) => {
    if (loading) {
      buttonStates.value.set(buttonId, true)
    } else {
      buttonStates.value.delete(buttonId)
    }
  }
  
  const isButtonLoading = (buttonId: string): boolean => {
    return buttonStates.value.has(buttonId)
  }
  
  const withButtonLoading = async <T>(
    buttonId: string,
    asyncFn: () => Promise<T>
  ): Promise<T> => {
    try {
      setButtonLoading(buttonId, true)
      const result = await asyncFn()
      return result
    } finally {
      setButtonLoading(buttonId, false)
    }
  }
  
  return {
    buttonStates: buttonStates as Readonly<Ref<Map<string, boolean>>>,
    setButtonLoading,
    isButtonLoading,
    withButtonLoading
  }
}

/**
 * 表格加载状态管理
 */
export function useTableLoading() {
  const tableLoading = ref(false)
  const refreshing = ref(false)
  const loadingMore = ref(false)
  
  const setTableLoading = (loading: boolean) => {
    tableLoading.value = loading
  }
  
  const setRefreshing = (loading: boolean) => {
    refreshing.value = loading
  }
  
  const setLoadingMore = (loading: boolean) => {
    loadingMore.value = loading
  }
  
  const withTableLoading = async <T>(
    asyncFn: () => Promise<T>,
    type: 'load' | 'refresh' | 'loadMore' = 'load'
  ): Promise<T> => {
    try {
      switch (type) {
        case 'load':
          setTableLoading(true)
          break
        case 'refresh':
          setRefreshing(true)
          break
        case 'loadMore':
          setLoadingMore(true)
          break
      }
      
      const result = await asyncFn()
      return result
    } finally {
      switch (type) {
        case 'load':
          setTableLoading(false)
          break
        case 'refresh':
          setRefreshing(false)
          break
        case 'loadMore':
          setLoadingMore(false)
          break
      }
    }
  }
  
  return {
    tableLoading: tableLoading as Readonly<Ref<boolean>>,
    refreshing: refreshing as Readonly<Ref<boolean>>,
    loadingMore: loadingMore as Readonly<Ref<boolean>>,
    setTableLoading,
    setRefreshing,
    setLoadingMore,
    withTableLoading
  }
}

/**
 * 防抖加载（避免快速连续操作）
 */
export function useDebouncedLoading(delay = 300) {
  const loading = ref(false)
  let timeoutId: NodeJS.Timeout | null = null
  
  const setLoading = (state: boolean) => {
    if (timeoutId) {
      clearTimeout(timeoutId)
      timeoutId = null
    }
    
    if (state) {
      loading.value = true
    } else {
      timeoutId = setTimeout(() => {
        loading.value = false
        timeoutId = null
      }, delay)
    }
  }
  
  const withDebouncedLoading = async <T>(
    asyncFn: () => Promise<T>
  ): Promise<T> => {
    try {
      setLoading(true)
      const result = await asyncFn()
      return result
    } finally {
      setLoading(false)
    }
  }
  
  return {
    loading: loading as Readonly<Ref<boolean>>,
    setLoading,
    withDebouncedLoading
  }
}
