<template>
  <el-dialog
    v-model="visible"
    width="800px"
    :before-close="handleClose"
    class="refund-approval-dialog"
    :show-close="false"
    align-center
  >
    <template #header>
      <div class="dialog-header">
        <div class="header-content">
          <el-icon class="header-icon"><CircleCheck /></el-icon>
          <div class="header-text">
            <h3>退款审核</h3>
            <p>请仔细审核退款申请信息</p>
          </div>
        </div>
      </div>
    </template>
    <div v-if="refund" class="approval-content">
      <!-- 退款信息概览 -->
      <div class="refund-overview">
        <div class="overview-header">
          <el-icon class="overview-icon"><Document /></el-icon>
          <h4>退款申请信息</h4>
        </div>

        <div class="overview-cards">
          <el-row :gutter="16">
            <el-col :span="12">
              <div class="info-card">
                <div class="card-icon refund-icon">
                  <el-icon><Document /></el-icon>
                </div>
                <div class="card-content">
                  <div class="card-label">退款单号</div>
                  <div class="card-value">{{ refund.refundNo }}</div>
                </div>
              </div>
            </el-col>

            <el-col :span="12">
              <div class="info-card">
                <div class="card-icon order-icon">
                  <el-icon><Document /></el-icon>
                </div>
                <div class="card-content">
                  <div class="card-label">订单号</div>
                  <div class="card-value">{{ refund.orderNumber }}</div>
                </div>
              </div>
            </el-col>

            <el-col :span="12">
              <div class="info-card">
                <div class="card-icon user-icon">
                  <el-icon><User /></el-icon>
                </div>
                <div class="card-content">
                  <div class="card-label">申请人</div>
                  <div class="card-value">{{ refund.buyerName || '未知用户' }}</div>
                  <div class="card-extra" v-if="refund.buyerPhone">{{ refund.buyerPhone }}</div>
                </div>
              </div>
            </el-col>

            <el-col :span="12">
              <div class="info-card amount-card">
                <div class="card-icon amount-icon">
                  <el-icon><Money /></el-icon>
                </div>
                <div class="card-content">
                  <div class="card-label">申请金额</div>
                  <div class="card-value amount">¥{{ refund.refundAmount.toFixed(2) }}</div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <div class="reason-section">
          <div class="reason-header">
            <el-icon><Document /></el-icon>
            <span>退款原因</span>
          </div>
          <div class="reason-content">
            {{ refund.refundReason }}
          </div>
        </div>
      </div>

      <!-- 审核表单 -->
      <div class="approval-section">
        <div class="approval-header">
          <el-icon class="approval-icon"><CircleCheck /></el-icon>
          <h4>审核决定</h4>
        </div>

        <el-form
          ref="formRef"
          :model="approvalForm"
          :rules="rules"
          label-width="120px"
          class="approval-form"
        >
          <el-form-item label="审核结果" prop="approvalResult" required>
            <div class="approval-options">
              <el-radio-group v-model="approvalForm.approvalResult" class="approval-radio-group">
                <div class="radio-option success-option">
                  <el-radio :label="1" size="large">
                    <div class="radio-content">
                      <el-icon class="radio-icon"><CircleCheck /></el-icon>
                      <div class="radio-text">
                        <div class="radio-title">审核通过</div>
                        <div class="radio-desc">同意此次退款申请</div>
                      </div>
                    </div>
                  </el-radio>
                </div>

                <div class="radio-option danger-option">
                  <el-radio :label="2" size="large">
                    <div class="radio-content">
                      <el-icon class="radio-icon"><CircleCheck /></el-icon>
                      <div class="radio-text">
                        <div class="radio-title">审核拒绝</div>
                        <div class="radio-desc">拒绝此次退款申请</div>
                      </div>
                    </div>
                  </el-radio>
                </div>
              </el-radio-group>
            </div>
          </el-form-item>

        <!-- 通过时的额外选项 -->
        <template v-if="approvalForm.approvalResult === 1">
          <el-form-item label="退款方式" prop="refundMethod">
            <el-select
              v-model="approvalForm.refundMethod"
              placeholder="请选择退款方式"
              style="width: 100%"
            >
              <el-option label="原路退回" :value="1" />
              <el-option label="余额退款" :value="2" />
            </el-select>
          </el-form-item>

          <el-form-item label="实际退款金额" prop="actualRefundAmount">
            <el-input-number
              v-model="approvalForm.actualRefundAmount"
              :min="0"
              :max="refund.refundAmount"
              :precision="2"
              :step="0.01"
              style="width: 100%"
              placeholder="请输入实际退款金额"
            />
            <div class="amount-tip">
              最大可退款金额：¥{{ refund.refundAmount.toFixed(2) }}
            </div>
          </el-form-item>
        </template>

        <el-form-item label="审核备注" prop="approvalRemark">
          <el-input
            v-model="approvalForm.approvalRemark"
            type="textarea"
            :rows="4"
            placeholder="请输入审核备注（选填）"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button size="large" @click="handleClose">
          <el-icon><Close /></el-icon>
          取消
        </el-button>
        <el-button
          type="primary"
          size="large"
          @click="handleSubmit"
          :loading="submitting"
          :disabled="!approvalForm.approvalResult"
        >
          <el-icon><CircleCheck /></el-icon>
          {{ submitting ? '提交中...' : '提交审核' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { CircleCheck, Money, Document, User, Close } from '@element-plus/icons-vue'
import { approveRefund } from '@/api/refund'
import type { RefundApplicationVO, RefundApprovalDTO } from '@/types/refund'
import { useUserStore } from '@/stores/user'



interface Props {
  modelValue: boolean
  refund: RefundApplicationVO | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'refresh'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 用户信息
const userStore = useUserStore()

const visible = ref(false)
const submitting = ref(false)
const formRef = ref<FormInstance>()

// 审核表单
const approvalForm = reactive<RefundApprovalDTO>({
  refundApplicationId: 0,
  approvalResult: 0,
  approvalRemark: '',
  refundMethod: 1,
  actualRefundAmount: 0
})

// 表单验证规则
const rules: FormRules = {
  approvalResult: [
    { required: true, message: '请选择审核结果', trigger: 'change' }
  ],
  refundMethod: [
    { required: true, message: '请选择退款方式', trigger: 'change' }
  ],
  actualRefundAmount: [
    { required: true, message: '请输入实际退款金额', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '退款金额必须大于0', trigger: 'blur' }
  ]
}

// 监听显示状态
watch(
  () => props.modelValue,
  (newVal) => {
    visible.value = newVal
    if (newVal && props.refund) {
      resetForm()
      approvalForm.refundApplicationId = props.refund.id
      approvalForm.actualRefundAmount = props.refund.refundAmount
    }
  }
)

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 重置表单
const resetForm = () => {
  approvalForm.refundApplicationId = 0
  approvalForm.approvalResult = 0
  approvalForm.approvalRemark = ''
  approvalForm.refundMethod = 1
  approvalForm.actualRefundAmount = 0
  
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 提交审核
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    const statusText = approvalForm.approvalResult === 1 ? '通过' : '拒绝'
    await ElMessageBox.confirm(`确认${statusText}该退款申请？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    submitting.value = true

    // 构建提交数据
    const submitData: RefundApprovalDTO = {
      refundApplicationId: approvalForm.refundApplicationId,
      approvalResult: approvalForm.approvalResult,
      approvalRemark: approvalForm.approvalRemark || '',
      approverName: userStore.userInfo?.accountName || '系统管理员'
    }

    // 如果是通过，添加退款方式和金额
    if (approvalForm.approvalResult === 1) {
      submitData.refundMethod = approvalForm.refundMethod
      submitData.actualRefundAmount = approvalForm.actualRefundAmount
    }

    console.log('提交审核数据:', submitData)

    try {
      const response = await approveRefund(submitData)
      console.log('审核响应:', response)

      // 严格检查响应结果
      if (response && response.code === 1) {
        ElMessage.success(response.data || `审核${statusText}成功`)
        emit('refresh')
        handleClose()
      } else {
        // 处理业务失败
        const errorMsg = response?.msg || response?.data || `审核${statusText}失败，请稍后重试`
        ElMessage.error(errorMsg)
        console.error('审核业务失败:', response)
      }
    } catch (apiError: any) {
      // 处理网络错误或其他异常
      console.error('审核API调用失败:', apiError)

      let errorMessage = `审核${statusText}失败，请稍后重试`

      // 尝试从错误中提取有用信息
      if (apiError?.response?.data?.msg) {
        errorMessage = apiError.response.data.msg
      } else if (apiError?.response?.data?.message) {
        errorMessage = apiError.response.data.message
      } else if (apiError?.message) {
        errorMessage = `网络错误: ${apiError.message}`
      }

      ElMessage.error(errorMessage)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('审核失败:', error)
      ElMessage.error('审核失败')
    }
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  resetForm()
}
</script>

<style scoped lang="scss">
.refund-approval-dialog {
  .approval-content {
    .refund-overview {
      background: #f8f9fa;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 20px;

      h4 {
        margin: 0 0 15px 0;
        color: #303133;
        font-size: 16px;
        font-weight: 600;
        border-bottom: 2px solid #409eff;
        padding-bottom: 8px;
      }

      .overview-item {
        margin-bottom: 12px;
        display: flex;
        align-items: flex-start;

        .label {
          font-weight: 500;
          color: #606266;
          min-width: 80px;
          margin-right: 10px;
        }

        .value {
          color: #303133;
          flex: 1;

          &.amount {
            font-weight: bold;
            color: #f56c6c;
            font-size: 16px;
          }
        }

        .reason-text {
          background: white;
          padding: 10px;
          border-radius: 4px;
          border: 1px solid #dcdfe6;
          color: #606266;
          line-height: 1.5;
          flex: 1;
        }
      }
    }

    .approval-section {
      .approval-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 20px;

        .approval-icon {
          font-size: 20px;
          color: #67c23a;
        }

        h4 {
          margin: 0;
          color: #303133;
          font-size: 16px;
          font-weight: 600;
        }
      }

      .approval-form {
        .approval-options {
          .approval-radio-group {
            width: 100%;

            .radio-option {
              margin-bottom: 16px;
              border: 2px solid #e4e7ed;
              border-radius: 8px;
              padding: 16px;
              transition: all 0.3s ease;

              &:hover {
                border-color: #409eff;
                background: #f0f9ff;
              }

              &.success-option {
                &:hover {
                  border-color: #67c23a;
                  background: #f0f9ff;
                }

                :deep(.el-radio__input.is-checked) {
                  .el-radio__inner {
                    background-color: #67c23a;
                    border-color: #67c23a;
                  }
                }
              }

              &.danger-option {
                &:hover {
                  border-color: #f56c6c;
                  background: #fef0f0;
                }

                :deep(.el-radio__input.is-checked) {
                  .el-radio__inner {
                    background-color: #f56c6c;
                    border-color: #f56c6c;
                  }
                }
              }

              .radio-content {
                display: flex;
                align-items: center;
                gap: 12px;

                .radio-icon {
                  font-size: 20px;
                  color: #409eff;
                }

                .radio-text {
                  .radio-title {
                    font-size: 16px;
                    font-weight: 600;
                    color: #303133;
                    margin-bottom: 4px;
                  }

                  .radio-desc {
                    font-size: 14px;
                    color: #909399;
                  }
                }
              }

              :deep(.el-radio) {
                margin-right: 0;

                .el-radio__label {
                  padding-left: 0;
                }
              }
            }
          }
        }

        .amount-tip {
          font-size: 12px;
          color: #909399;
          margin-top: 5px;
        }
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px 0 0 0;
    border-top: 1px solid #e4e7ed;
    margin-top: 20px;

    .el-button {
      padding: 12px 24px;
      font-weight: 500;

      &.el-button--primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;

        &:hover {
          background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .refund-approval-dialog {
    .approval-content {
      .refund-overview {
        padding: 15px;

        .overview-item {
          flex-direction: column;

          .label {
            min-width: auto;
            margin-bottom: 5px;
          }
        }
      }
    }
  }
}
</style>
