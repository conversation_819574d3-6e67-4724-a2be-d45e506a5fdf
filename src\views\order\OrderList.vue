<template>
  <div class="order-list-container">
    <div class="page-header">
      <h2>订单列表</h2>
      <p>管理和查看所有订单信息</p>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="search-section">
      <el-card>
        <el-form :model="searchForm" inline class="search-form">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="8" :lg="6">
              <el-form-item label="订单号">
                <el-input
                  v-model="searchForm.number"
                  placeholder="请输入订单号"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8" :lg="6">
              <el-form-item label="买家ID">
                <el-input
                  v-model="searchForm.buyerId"
                  placeholder="请输入买家ID"
                  clearable
                  type="number"
                />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8" :lg="6">
              <el-form-item label="订单状态">
                <el-select
                  v-model="searchForm.status"
                  placeholder="请选择状态"
                  clearable
                >
                  <el-option label="待支付" :value="1" />
                  <el-option label="已支付" :value="2" />
                  <el-option label="已取消" :value="3" />
                  <el-option label="已发货" :value="4" />
                  <el-option label="已完成" :value="5" />
                  <el-option label="已关闭" :value="6" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8" :lg="6">
              <el-form-item label="支付方式">
                <el-select
                  v-model="searchForm.payMethod"
                  placeholder="请选择支付方式"
                  clearable
                >
                  <el-option label="微信支付" :value="1" />
                  <el-option label="支付宝" :value="2" />
                  <el-option label="银行卡" :value="3" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="8" :lg="6">
              <el-form-item label="商品名称">
                <el-input
                  v-model="searchForm.productName"
                  placeholder="请输入商品名称"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8" :lg="6">
              <el-form-item label="最小金额">
                <el-input
                  v-model="searchForm.minAmount"
                  placeholder="请输入最小金额"
                  clearable
                  type="number"
                />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8" :lg="6">
              <el-form-item label="最大金额">
                <el-input
                  v-model="searchForm.maxAmount"
                  placeholder="请输入最大金额"
                  clearable
                  type="number"
                />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8" :lg="6">
              <el-form-item label="下单时间">
                <el-date-picker
                  v-model="searchForm.dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24">
              <el-form-item class="search-buttons">
                <el-button type="primary" @click="handleSearch" :loading="tableLoading">
                  <el-icon><Search /></el-icon>
                  搜索
                </el-button>
                <el-button @click="handleReset">
                  <el-icon><Refresh /></el-icon>
                  重置
                </el-button>
                <el-button @click="toggleAdvancedSearch">
                  {{ showAdvancedSearch ? '收起' : '展开' }}
                </el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
    </div>

    <!-- 订单列表 -->
    <div class="table-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>订单列表</span>
            <div class="header-actions">
              <el-button @click="handleRefresh" :loading="loading">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </div>
        </template>

        <div class="table-toolbar">
          <div class="toolbar-left">
            <el-button
              type="primary"
              :disabled="selectedOrders.length === 0"
              @click="handleBatchShip"
            >
              批量发货
            </el-button>
            <el-button
              type="success"
              :disabled="selectedOrders.length === 0"
              @click="handleBatchComplete"
            >
              批量完成
            </el-button>
            <el-button
              type="danger"
              :disabled="selectedOrders.length === 0"
              @click="handleBatchCancel"
            >
              批量取消
            </el-button>
            <el-button
              type="info"
              :disabled="selectedOrders.length === 0"
              @click="handleBatchExport"
            >
              批量导出
            </el-button>
          </div>
          <div class="toolbar-right">
            <span class="selected-info">已选择 {{ selectedOrders.length }} 项</span>
          </div>
        </div>

        <el-table
          :data="orderList"
          v-loading="tableLoading"
          stripe
          style="width: 100%"
          @selection-change="handleSelectionChange"
          class="order-table"
        >
          <el-table-column type="selection" width="55" fixed="left" />
          <el-table-column prop="number" label="订单号" min-width="180" fixed="left" show-overflow-tooltip />
          <el-table-column prop="buyerId" label="买家ID" width="100" />
          <el-table-column prop="amount" label="订单金额" width="120" sortable>
            <template #default="{ row }">
              <span class="amount">¥{{ formatAmount(row.amount) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="订单状态" width="120">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)" size="small">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="orderTime" label="下单时间" width="160" sortable>
            <template #default="{ row }">
              {{ formatDateTime(row.orderTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="payTime" label="支付时间" width="160" sortable>
            <template #default="{ row }">
              {{ formatDateTime(row.payTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="payMethod" label="支付方式" width="120">
            <template #default="{ row }">
              {{ getPayMethodText(row.payMethod) }}
            </template>
          </el-table-column>
          <el-table-column prop="logisticsCompany" label="物流公司" width="120" show-overflow-tooltip>
            <template #default="{ row }">
              {{ row.logisticsCompany || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="trackingNumber" label="快递单号" width="140" show-overflow-tooltip>
            <template #default="{ row }">
              {{ row.trackingNumber || '-' }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="240" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button size="small" @click="handleViewDetail(row)">
                  详情
                </el-button>
                <el-button
                  v-if="row.status === 2"
                  size="small"
                  type="primary"
                  @click="handleShip(row)"
                >
                  发货
                </el-button>
                <el-button
                  v-if="row.status === 4"
                  size="small"
                  type="success"
                  @click="handleComplete(row)"
                >
                  完成
                </el-button>
                <el-dropdown v-if="getMoreActions(row).length > 0" @command="handleMoreAction">
                  <el-button size="small" type="text">
                    更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item
                        v-for="action in getMoreActions(row)"
                        :key="action.command"
                        :command="{ action: action.command, row }"
                      >
                        {{ action.label }}
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.size"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 订单详情对话框 -->
    <OrderDetailDialog
      v-model="detailDialogVisible"
      :order-id="selectedOrderId"
      @refresh="handleRefresh"
    />

    <!-- 取消订单对话框 -->
    <CancelOrderDialog
      v-model="cancelDialogVisible"
      :order="selectedOrder"
      @refresh="handleRefresh"
    />

    <!-- 发货对话框 -->
    <ShipOrderDialog
      v-model="shipDialogVisible"
      :order-info="selectedOrder"
      @success="handleRefresh"
    />




  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, ArrowDown } from '@element-plus/icons-vue'
import OrderDetailDialog from './components/OrderDetailDialog.vue'
import CancelOrderDialog from './components/CancelOrderDialog.vue'
import ShipOrderDialog from './components/ShipOrderDialog.vue'
import {
  getOrderList,
  completeOrder,
  cancelOrder,
  batchShip,
  batchComplete,
  batchCancel,
  batchExport
} from '@/api/order'
import type { OrderVO, OrderPageQueryDTO } from '@/types/order'
import {
  getStatusText,
  getStatusType,
  canPerformAction,
  canCancel,
  canRefund
} from '@/utils/orderStatus'
import { handleApiResponse, withErrorHandling } from '@/utils/errorHandler'
import { useTableLoading } from '@/utils/loadingManager'

// 搜索表单
const searchForm = reactive<OrderPageQueryDTO>({
  number: '',
  buyerId: undefined,
  status: undefined,
  payMethod: undefined,
  productName: '',
  minAmount: undefined,
  maxAmount: undefined,
  beginTime: '',
  endTime: '',
  dateRange: null,
  sortField: 'orderTime',
  sortOrder: 'desc',
  page: 1,
  pageSize: 10
})

// 界面状态
const showAdvancedSearch = ref(false)

// 订单列表数据
const orderList = ref<OrderVO[]>([])
const loading = ref(false)
const selectedOrders = ref<OrderVO[]>([])

// 分页信息
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 对话框状态
const detailDialogVisible = ref(false)
const cancelDialogVisible = ref(false)
const shipDialogVisible = ref(false)
const selectedOrderId = ref<number | null>(null)
const selectedOrder = ref<OrderVO | null>(null)

// 加载状态管理
const { tableLoading, withTableLoading } = useTableLoading()

// 获取订单列表
const fetchOrderList = withErrorHandling(async () => {
  return await withTableLoading(async () => {
    const params: any = {
      ...searchForm,
      page: pagination.page,
      pageSize: pagination.size
    }

    // 处理日期范围
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.beginTime = searchForm.dateRange[0]
      params.endTime = searchForm.dateRange[1]
    }

    // 清理空值参数
    Object.keys(params).forEach(key => {
      if (params[key] === null || params[key] === undefined || params[key] === '') {
        delete params[key]
      }
    })

    const response = await getOrderList(params)
    const result = handleApiResponse(response, {
      showErrorMessage: true,
      customErrorMessage: '获取订单列表失败'
    })

    if (result.success) {
      orderList.value = result.data?.list || []
      pagination.total = result.data?.total || 0
    }

    return result
  })
}, {
  loadingRef: loading,
  errorMessage: '获取订单列表失败'
})

// 切换高级搜索
const toggleAdvancedSearch = () => {
  showAdvancedSearch.value = !showAdvancedSearch.value
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchOrderList()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    number: '',
    buyerId: undefined,
    status: undefined,
    payMethod: undefined,
    productName: '',
    minAmount: undefined,
    maxAmount: undefined,
    beginTime: '',
    endTime: '',
    dateRange: null,
    sortField: 'orderTime',
    sortOrder: 'desc'
  })
  pagination.page = 1
  fetchOrderList()
}

// 刷新
const handleRefresh = () => {
  fetchOrderList()
}

// 选择变化
const handleSelectionChange = (selection: OrderVO[]) => {
  selectedOrders.value = selection
}

// 分页变化
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchOrderList()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchOrderList()
}

// 查看详情
const handleViewDetail = (order: OrderVO) => {
  selectedOrderId.value = order.id
  detailDialogVisible.value = true
}

// 发货
const handleShip = (order: OrderVO) => {
  selectedOrder.value = order
  shipDialogVisible.value = true
}

// 完成订单
const handleComplete = async (order: OrderVO) => {
  try {
    await ElMessageBox.confirm('确认完成该订单？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await completeOrder(order.id)
    if (response.code === 1) {
      ElMessage.success('订单完成')
      handleRefresh()
    } else {
      ElMessage.error(response.msg || '操作失败')
    }
  } catch (error) {
    console.error('完成订单失败:', error)
  }
}



// 取消订单
const handleCancel = async (order: OrderVO) => {
  try {
    const { value: reason } = await ElMessageBox.prompt('请输入取消原因', '取消订单', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /.+/,
      inputErrorMessage: '取消原因不能为空'
    })

    const response = await cancelOrder({ id: order.id, cancelReason: reason })
    if (response.code === 1) {
      ElMessage.success('取消成功')
      handleRefresh()
    } else {
      ElMessage.error(response.msg || '取消失败')
    }
  } catch (error) {
    console.error('取消订单失败:', error)
  }
}

// 批量发货
const handleBatchShip = async () => {
  if (selectedOrders.value.length === 0) {
    ElMessage.warning('请选择要发货的订单')
    return
  }

  try {
    await ElMessageBox.confirm(`确认批量发货选中的 ${selectedOrders.value.length} 个订单？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const orderIds = selectedOrders.value.map(order => order.id)
    const response = await batchShip({ orderIds })

    if (response.code === 1) {
      ElMessage.success('批量发货成功')
      handleRefresh()
    } else {
      ElMessage.error(response.msg || '批量发货失败')
    }
  } catch (error) {
    console.error('批量发货失败:', error)
  }
}

// 批量完成
const handleBatchComplete = async () => {
  if (selectedOrders.value.length === 0) {
    ElMessage.warning('请选择要完成的订单')
    return
  }

  try {
    await ElMessageBox.confirm(`确认批量完成选中的 ${selectedOrders.value.length} 个订单？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const orderIds = selectedOrders.value.map(order => order.id)
    const response = await batchComplete({ orderIds })

    if (response.code === 1) {
      ElMessage.success('批量完成成功')
      handleRefresh()
    } else {
      ElMessage.error(response.msg || '批量完成失败')
    }
  } catch (error) {
    console.error('批量完成失败:', error)
  }
}

// 批量取消
const handleBatchCancel = async () => {
  if (selectedOrders.value.length === 0) {
    ElMessage.warning('请选择要取消的订单')
    return
  }

  try {
    const { value: reason } = await ElMessageBox.prompt('请输入取消原因', '批量取消订单', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /.+/,
      inputErrorMessage: '取消原因不能为空'
    })

    const orderIds = selectedOrders.value.map(order => order.id)
    const response = await batchCancel({ orderIds, reason })

    if (response.code === 1) {
      ElMessage.success('批量取消成功')
      handleRefresh()
    } else {
      ElMessage.error(response.msg || '批量取消失败')
    }
  } catch (error) {
    console.error('批量取消失败:', error)
  }
}

// 批量导出
const handleBatchExport = async () => {
  if (selectedOrders.value.length === 0) {
    ElMessage.warning('请选择要导出的订单')
    return
  }

  try {
    const orderIds = selectedOrders.value.map(order => order.id)
    const response = await batchExport({ orderIds })

    if (response.code === 1) {
      ElMessage.success('导出成功')
      // 处理下载链接
      if (response.data?.downloadUrl) {
        window.open(response.data.downloadUrl)
      }
    } else {
      ElMessage.error(response.msg || '导出失败')
    }
  } catch (error) {
    console.error('批量导出失败:', error)
  }
}



// 更多操作
const getMoreActions = (order: OrderVO) => {
  const actions = []

  if (canCancel(order.status)) {
    actions.push({ command: 'cancel', label: '取消订单' })
  }

  if (canRefund(order.status)) {
    actions.push({ command: 'refund', label: '退款' })
  }

  if (canPerformAction(order.status, 'edit')) {
    actions.push({ command: 'edit', label: '编辑订单' })
  }

  return actions
}

// 处理更多操作
const handleMoreAction = ({ action, row }: { action: string; row: OrderVO }) => {
  switch (action) {
    case 'cancel':
      handleCancel(row)
      break
    case 'refund':
      // TODO: 实现退款功能
      ElMessage.info('暂不支持商家主动退款操作，请联系客服处理')
      break
    case 'edit':
      // TODO: 实现编辑功能
      ElMessage.info('编辑功能开发中')
      break
  }
}



// 获取支付方式文本 - 根据API文档更新
const getPayMethodText = (payMethod: number) => {
  const payMethodMap: Record<number, string> = {
    1: '微信支付',
    2: '支付宝支付',
    3: '信用卡支付',
    4: '货到付款'
  }
  return payMethodMap[payMethod] || '未知'
}

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 格式化金额
const formatAmount = (amount: number) => {
  if (!amount) return '0.00'
  return amount.toFixed(2)
}

onMounted(() => {
  fetchOrderList()
})
</script>

<style scoped lang="scss">
.order-list-container {
  padding: 20px;

  .page-header {
    margin-bottom: 20px;

    h2 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 24px;
      font-weight: 600;
    }

    p {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }

  .search-section {
    margin-bottom: 20px;

    .search-form {
      .el-form-item {
        margin-bottom: 16px;

        .el-input,
        .el-select,
        .el-date-picker {
          width: 100%;
        }
      }

      .search-buttons {
        text-align: center;
        margin-top: 10px;

        .el-button {
          margin: 0 8px;
        }
      }
    }
  }

  .table-section {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-actions {
        display: flex;
        gap: 10px;
      }
    }

    .table-toolbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding: 12px 0;
      border-bottom: 1px solid #ebeef5;

      .toolbar-left {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
      }

      .toolbar-right {
        .selected-info {
          color: #606266;
          font-size: 14px;
        }
      }
    }

    .order-table {
      .amount {
        color: #f56c6c;
        font-weight: 600;
      }

      .action-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;

        .el-button {
          margin: 0;
          padding: 4px 8px;
          font-size: 12px;
        }
      }
    }

    .pagination-container {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .order-list-container {
    padding: 10px;

    .search-section {
      .search-form {
        .el-col {
          margin-bottom: 10px;
        }
      }
    }

    .table-section {
      .table-toolbar {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;

        .toolbar-left {
          justify-content: center;
        }

        .toolbar-right {
          text-align: center;
        }
      }

      .order-table {
        .action-buttons {
          flex-direction: column;

          .el-button {
            width: 100%;
            margin-bottom: 2px;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .order-list-container {
    .table-section {
      .table-toolbar {
        .toolbar-left {
          .el-button {
            flex: 1;
            min-width: 0;
            font-size: 12px;
            padding: 6px 4px;
          }
        }
      }
    }
  }
}
</style>
