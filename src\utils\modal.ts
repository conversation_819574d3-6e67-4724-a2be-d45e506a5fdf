import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'

let loadingInstance: ReturnType<typeof ElLoading.service> | null = null

/**
 * 消息提示
 *
 * @param content 内容
 */
export function msg(content: string) {
  ElMessage.info(content)
}

/**
 * 错误消息
 *
 * @param content 内容
 */
export function msgError(content: string) {
  ElMessage.error(content)
}

/**
 * 成功消息
 *
 * @param content 内容
 */
export function msgSuccess(content: string) {
  ElMessage.success(content)
}

/**
 * 警告消息
 *
 * @param content 内容
 */
export function msgWarning(content: string) {
  ElMessage.warning(content)
}

/**
 * 确认窗体
 *
 * @param content 内容
 * @param title 标题
 * @param callback 回调函数
 */
export function confirm(content: string, title: string, callback: (action: string) => void) {
  ElMessageBox.confirm(content, title, {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      callback('confirm')
    })
    .catch(() => {
      callback('cancel')
    })
}

/**
 * 提示窗体
 *
 * @param content 内容
 * @param title 标题
 * @param callback 回调函数
 */
export function alert(content: string, title: string, callback: (action: string) => void) {
  ElMessageBox.alert(content, title, {
    confirmButtonText: '确定',
    callback: (action: string) => {
      callback(action)
    },
  })
}

/**
 * 打开遮罩层
 *
 * @param content 显示内容
 */
export function loading(content: string) {
  loadingInstance = ElLoading.service({
    lock: true,
    text: content,
    background: 'rgba(0, 0, 0, 0.7)',
  })
}

/**
 * 关闭遮罩层
 */
export function closeLoading() {
  if (loadingInstance) {
    loadingInstance.close()
    loadingInstance = null
  }
}
