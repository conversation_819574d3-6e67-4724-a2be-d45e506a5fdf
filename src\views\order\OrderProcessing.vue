<template>
  <div class="order-processing-container">
    <div class="page-header">
      <h2>处理中订单</h2>
      <p>查看和处理正在处理中的订单</p>
    </div>

    <!-- 快速筛选 -->
    <div class="quick-filter-section">
      <el-card>
        <div class="filter-tabs">
          <el-radio-group v-model="activeStatus" @change="handleStatusChange">
            <el-radio-button :label="2">已支付</el-radio-button>
            <el-radio-button :label="4">已发货</el-radio-button>
          </el-radio-group>
          <div class="filter-actions">
            <el-button @click="handleRefresh" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 订单列表 -->
    <div class="table-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>{{ getStatusText(activeStatus) }}订单 ({{ pagination.total }})</span>
            <div class="header-actions">
              <el-button 
                v-if="selectedOrders.length > 0"
                type="primary"
                @click="handleBatchShip"
                :disabled="!canBatchShip"
              >
                批量发货
              </el-button>
            </div>
          </div>
        </template>

        <el-table
          :data="orderList"
          v-loading="loading"
          stripe
          style="width: 100%"
          @selection-change="handleSelectionChange"
          class="processing-table"
        >
          <el-table-column
            type="selection"
            width="55"
            :selectable="(row) => [2, 4].includes(row.status)"
            fixed="left"
          />
          <el-table-column prop="number" label="订单号" min-width="180" fixed="left" show-overflow-tooltip />
          <el-table-column prop="buyerId" label="买家ID" width="100" />
          <el-table-column prop="amount" label="订单金额" width="120" sortable>
            <template #default="{ row }">
              <span class="amount">¥{{ formatAmount(row.amount) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="orderTime" label="下单时间" width="160" sortable>
            <template #default="{ row }">
              {{ formatDateTime(row.orderTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="payTime" label="支付时间" width="160" sortable>
            <template #default="{ row }">
              {{ formatDateTime(row.payTime) }}
            </template>
          </el-table-column>
          <el-table-column label="处理时长" width="120">
            <template #default="{ row }">
              <span :class="getProcessingTimeClass(row.orderTime)">
                {{ getProcessingTime(row.orderTime) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)" size="small">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="logisticsCompany" label="物流公司" width="120" show-overflow-tooltip>
            <template #default="{ row }">
              {{ row.logisticsCompany || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="trackingNumber" label="快递单号" width="140" show-overflow-tooltip>
            <template #default="{ row }">
              {{ row.trackingNumber || '-' }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="240" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button size="small" @click="handleViewDetail(row)">
                  详情
                </el-button>
                <el-button
                  v-if="row.status === 2"
                  size="small"
                  type="primary"
                  @click="handleShip(row)"
                >
                  发货
                </el-button>
                <el-button
                  v-if="row.status === 4"
                  size="small"
                  type="success"
                  @click="handleComplete(row)"
                >
                  完成
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.size"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 订单详情对话框 -->
    <OrderDetailDialog
      v-model="detailDialogVisible"
      :order-id="selectedOrderId"
      @refresh="handleRefresh"
    />

    <!-- 发货对话框 -->
    <ShipOrderDialog
      v-model="shipDialogVisible"
      :order-info="selectedOrder"
      @success="handleRefresh"
    />

    <!-- 批量发货对话框 -->
    <BatchShipDialog
      v-model="batchShipDialogVisible"
      :orders="selectedOrders"
      @refresh="handleRefresh"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import OrderDetailDialog from './components/OrderDetailDialog.vue'
import ShipOrderDialog from './components/ShipOrderDialog.vue'
import {
  getOrderList,
  completeOrder
} from '@/api/order'
import type { OrderVO, OrderPageQueryDTO } from '@/types/order'

// 当前状态
const activeStatus = ref(2) // 默认显示已付款订单

// 订单列表数据
const orderList = ref<OrderVO[]>([])
const loading = ref(false)
const selectedOrders = ref<OrderVO[]>([])

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 对话框状态
const detailDialogVisible = ref(false)
const shipDialogVisible = ref(false)
const batchShipDialogVisible = ref(false)
const selectedOrderId = ref<number | null>(null)
const selectedOrder = ref<OrderVO | null>(null)

// 是否可以批量发货
const canBatchShip = computed(() => {
  return selectedOrders.value.length > 0 && 
         selectedOrders.value.every(order => order.status === 2)
})

// 获取订单列表
const fetchOrderList = async () => {
  loading.value = true
  try {
    const params: OrderPageQueryDTO = {
      status: activeStatus.value,
      page: pagination.page,
      pageSize: pagination.size
    }
    
    const response = await getOrderList(params)
    if (response.code === 1) {
      orderList.value = response.data.list || []
      pagination.total = response.data.total || 0
    } else {
      ElMessage.error(response.msg || '获取订单列表失败')
    }
  } catch (error) {
    console.error('获取订单列表失败:', error)
    ElMessage.error('获取订单列表失败')
  } finally {
    loading.value = false
  }
}

// 状态变化
const handleStatusChange = () => {
  pagination.page = 1
  selectedOrders.value = []
  fetchOrderList()
}

// 刷新
const handleRefresh = () => {
  selectedOrders.value = []
  fetchOrderList()
}

// 选择变化
const handleSelectionChange = (selection: OrderVO[]) => {
  selectedOrders.value = selection
}

// 分页变化
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchOrderList()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchOrderList()
}

// 查看详情
const handleViewDetail = (order: OrderVO) => {
  selectedOrderId.value = order.id
  detailDialogVisible.value = true
}

// 发货
const handleShip = (order: OrderVO) => {
  selectedOrder.value = order
  shipDialogVisible.value = true
}

// 完成订单
const handleComplete = async (order: OrderVO) => {
  try {
    await ElMessageBox.confirm('确认完成该订单？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await completeOrder(order.id)
    if (response.code === 1) {
      ElMessage.success('订单完成')
      handleRefresh()
    } else {
      ElMessage.error(response.msg || '操作失败')
    }
  } catch (error) {
    console.error('完成订单失败:', error)
  }
}

// 批量发货
const handleBatchShip = () => {
  batchShipDialogVisible.value = true
}

// 获取状态类型 - 根据新的状态码更新
const getStatusType = (status: number) => {
  const statusMap: Record<number, string> = {
    1: 'warning',   // 待付款
    2: 'info',      // 已付款
    3: 'warning',   // 待审核
    4: 'primary',   // 处理中
    5: 'success',   // 已发货
    6: 'success',   // 已送达
    7: 'success',   // 已完成
    8: 'danger',    // 已取消
    9: 'info',      // 已退款
    10: 'danger'    // 已拒绝
  }
  return statusMap[status] || 'info'
}

// 获取状态文本 - 根据新的状态码更新
const getStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    1: '待付款',
    2: '已付款',
    3: '待审核',
    4: '处理中',
    5: '已发货',
    6: '已送达',
    7: '已完成',
    8: '已取消',
    9: '已退款',
    10: '已拒绝'
  }
  return statusMap[status] || '未知'
}

// 获取处理时长
const getProcessingTime = (orderTime: string) => {
  if (!orderTime) return '-'
  
  const now = new Date()
  const orderDate = new Date(orderTime)
  const diffMs = now.getTime() - orderDate.getTime()
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffHours / 24)
  
  if (diffDays > 0) {
    return `${diffDays}天${diffHours % 24}小时`
  } else {
    return `${diffHours}小时`
  }
}

// 获取处理时长样式类
const getProcessingTimeClass = (orderTime: string) => {
  if (!orderTime) return ''
  
  const now = new Date()
  const orderDate = new Date(orderTime)
  const diffHours = Math.floor((now.getTime() - orderDate.getTime()) / (1000 * 60 * 60))
  
  if (diffHours > 48) {
    return 'processing-time urgent'
  } else if (diffHours > 24) {
    return 'processing-time warning'
  } else {
    return 'processing-time normal'
  }
}

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 格式化金额
const formatAmount = (amount: number) => {
  if (!amount) return '0.00'
  return amount.toFixed(2)
}

onMounted(() => {
  fetchOrderList()
})
</script>

<style scoped lang="scss">
.order-processing-container {
  padding: 20px;
  
  .page-header {
    margin-bottom: 20px;
    
    h2 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 24px;
      font-weight: 600;
    }
    
    p {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }
  
  .quick-filter-section {
    margin-bottom: 20px;
    
    .filter-tabs {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .filter-actions {
        display: flex;
        gap: 10px;
      }
    }
  }
  
  .table-section {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-actions {
        display: flex;
        gap: 10px;
      }
    }
    
    .processing-table {
      .amount {
        color: #f56c6c;
        font-weight: 600;
      }

      .processing-time {
        font-size: 12px;
        font-weight: 600;

        &.normal {
          color: #67c23a;
        }

        &.warning {
          color: #e6a23c;
        }

        &.urgent {
          color: #f56c6c;
        }
      }

      .action-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;

        .el-button {
          margin: 0;
          padding: 4px 8px;
          font-size: 12px;
        }
      }
    }

    .pagination-container {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .order-processing-container {
    padding: 10px;

    .quick-filter-section {
      .filter-tabs {
        flex-direction: column;
        gap: 15px;

        .el-radio-group {
          .el-radio-button {
            margin-bottom: 5px;
          }
        }
      }
    }

    .table-section {
      .processing-table {
        .action-buttons {
          flex-direction: column;

          .el-button {
            width: 100%;
            margin-bottom: 2px;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .order-processing-container {
    .quick-filter-section {
      .filter-tabs {
        .el-radio-group {
          .el-radio-button {
            flex: 1;
            text-align: center;
          }
        }
      }
    }
  }
}
</style>
