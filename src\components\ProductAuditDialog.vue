<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    width="480px"
    center
    class="product-audit-dialog"
    top="25vh"
  >
    <div class="audit-container">
      <!-- 顶部装饰元素 -->
      <div class="top-decoration">
        <div class="decoration-circle circle-1"></div>
        <div class="decoration-circle circle-2"></div>
        <div class="decoration-circle circle-3"></div>
      </div>
      
      <div class="audit-icon-wrapper">
        <div class="audit-icon-bg">
          <el-icon class="check-icon"><CircleCheck /></el-icon>
          <el-icon class="clock-icon"><Clock /></el-icon>
        </div>
      </div>

      <h2 class="audit-title">商品审核中</h2>

      <div class="audit-content">
        <p>您的商品<span class="highlight">{{ productName }}</span>已提交成功</p>
        <p>平台将在<span class="highlight">24小时内</span>完成审核，请耐心等待</p>
      </div>

      <div class="audit-info">
        <div class="info-item">
          <el-icon><Timer /></el-icon>
          <span>审核时间：24小时内</span>
        </div>
        <div class="info-item">
          <el-icon><Bell /></el-icon>
          <span>审核通过后，商品将自动上架销售</span>
        </div>
        <div class="info-item">
          <el-icon><InfoFilled /></el-icon>
          <span>若审核未通过，您将收到通知说明原因</span>
        </div>
      </div>

      <div class="buttons-group">
        <el-button class="secondary-button" @click="handleViewProducts">
          查看商品列表
        </el-button>
        <el-button type="primary" class="primary-button" @click="handleAddAnother">
          继续添加商品
        </el-button>
      </div>

      <div class="countdown" v-if="showCountdown">
        <span>{{ countdown }}秒后自动跳转到商品列表</span>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, defineProps, defineEmits } from 'vue'
import { CircleCheck, Timer, Bell, InfoFilled, Clock } from '@element-plus/icons-vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  productName: {
    type: String,
    default: ''
  },
  showCountdown: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['view-products', 'add-another', 'update:visible'])

const countdown = ref(5)
let timer: number | null = null

const handleViewProducts = () => {
  if (timer) {
    window.clearInterval(timer)
    timer = null
  }
  emit('view-products')
}

const handleAddAnother = () => {
  if (timer) {
    window.clearInterval(timer)
    timer = null
  }
  emit('add-another')
}

onMounted(() => {
  if (props.visible && props.showCountdown) {
    startCountdown()
  }
})

const startCountdown = () => {
  timer = window.setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      if (timer) {
        window.clearInterval(timer)
        timer = null
      }
      handleViewProducts()
    }
  }, 1000)
}

onBeforeUnmount(() => {
  if (timer) {
    window.clearInterval(timer)
    timer = null
  }
})
</script>

<style scoped lang="scss">
.product-audit-dialog {
  :deep(.el-dialog) {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(235, 238, 245, 0.8);
  }

  :deep(.el-dialog__header) {
    display: none;
  }

  :deep(.el-dialog__body) {
    padding: 0;
  }
}

.audit-container {
  padding: 35px 30px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.top-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 120px;
  overflow: hidden;
  
  .decoration-circle {
    position: absolute;
    border-radius: 50%;
    opacity: 0.4;
  }
  
  .circle-1 {
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, #409EFF, #a0cfff);
    top: -50px;
    right: -20px;
  }
  
  .circle-2 {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #409EFF, #a0cfff);
    top: -20px;
    left: 20%;
  }
  
  .circle-3 {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #409EFF, #a0cfff);
    top: 20px;
    left: 35%;
  }
}

.audit-icon-wrapper {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: linear-gradient(135deg, #e1f6ff 0%, #f5faff 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
  animation: pulse 2s infinite;
  position: relative;
  z-index: 2;
}

.audit-icon-bg {
  position: relative;
  width: 80px;
  height: 80px;
  
  .check-icon {
    position: absolute;
    top: 0;
    left: 0;
    font-size: 80px;
    color: #67c23a;
    animation: appear 0.5s ease-out;
  }
  
  .clock-icon {
    position: absolute;
    bottom: 0;
    right: 0;
    font-size: 35px;
    color: #409EFF;
    background: white;
    border-radius: 50%;
    padding: 5px;
    border: 2px solid #f0f0f0;
    animation: slideUp 0.5s ease-out 0.2s both;
  }
}

.audit-title {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20px;
  animation: slideDown 0.5s ease-out;
  background: linear-gradient(90deg, #409EFF, #79bbff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.audit-content {
  margin-bottom: 25px;

  p {
    font-size: 16px;
    line-height: 1.8;
    color: #606266;
    margin: 5px 0;
  }

  .highlight {
    color: #409EFF;
    font-weight: 600;
    padding: 0 4px;
  }
}

.audit-info {
  background-color: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  width: 100%;
  margin-bottom: 25px;
  border: 1px solid #ebeef5;
  animation: fadeIn 0.8s ease-out;

  .info-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    text-align: left;

    &:last-child {
      margin-bottom: 0;
    }

    .el-icon {
      font-size: 18px;
      color: #409EFF;
      margin-right: 10px;
    }

    span {
      font-size: 14px;
      color: #606266;
    }
  }
}

.buttons-group {
  display: flex;
  width: 100%;
  gap: 15px;
  margin-bottom: 15px;
  
  .secondary-button, 
  .primary-button {
    flex: 1;
    height: 46px;
    border-radius: 8px;
    font-size: 15px;
    font-weight: 500;
    transition: all 0.3s;
  }
  
  .primary-button {
    background: linear-gradient(90deg, #409EFF, #79bbff);
    border: none;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(64, 158, 255, 0.3);
    }
  }
  
  .secondary-button {
    &:hover {
      transform: translateY(-2px);
    }
  }
}

.countdown {
  font-size: 14px;
  color: #909399;
  animation: fadeIn 1s ease-out;
  margin-top: 5px;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 15px rgba(64, 158, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);
  }
}

@keyframes appear {
  from {
    opacity: 0;
    transform: scale(0.5);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style> 