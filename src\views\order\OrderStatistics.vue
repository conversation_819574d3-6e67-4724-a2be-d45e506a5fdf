<template>
  <div class="order-statistics-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>订单统计</h2>
      <p>全面的订单数据统计分析</p>
    </div>

    <!-- 时间选择器 -->
    <div class="date-filter">
      <el-card>
        <div class="filter-content">
          <div class="filter-item">
            <label>统计时间范围：</label>
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="onDateRangeChange"
            />
          </div>
          <div class="filter-actions">
            <el-button type="primary" @click="refreshAllData" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新数据
            </el-button>
            <el-button @click="resetDateRange">重置</el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 核心统计卡片 -->
    <div class="core-stats-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stats-card total-orders">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon><ShoppingCart /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ enhancedStats.totalOrders || 0 }}</div>
                <div class="stats-label">总订单数</div>
                <div class="stats-trend" v-if="comparisonData.orderGrowthRate !== undefined">
                  <span :class="comparisonData.orderGrowthRate >= 0 ? 'trend-up' : 'trend-down'">
                    {{ comparisonData.orderGrowthRate >= 0 ? '↗' : '↘' }}
                    {{ Math.abs(comparisonData.orderGrowthRate).toFixed(1) }}%
                  </span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card total-sales">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon><Money /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">¥{{ formatAmount(enhancedStats.totalSales) }}</div>
                <div class="stats-label">总销售额</div>
                <div class="stats-trend" v-if="comparisonData.salesGrowthRate !== undefined">
                  <span :class="comparisonData.salesGrowthRate >= 0 ? 'trend-up' : 'trend-down'">
                    {{ comparisonData.salesGrowthRate >= 0 ? '↗' : '↘' }}
                    {{ Math.abs(comparisonData.salesGrowthRate).toFixed(1) }}%
                  </span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card avg-amount">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon><DataAnalysis /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">¥{{ formatAmount(enhancedStats.avgOrderAmount) }}</div>
                <div class="stats-label">平均订单金额</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card conversion-rate">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ enhancedStats.paymentRate || 0 }}%</div>
                <div class="stats-label">支付转化率</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 时间维度统计 -->
    <div class="time-dimension-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>时间维度统计</span>
            <el-button @click="fetchTimeDimensionStats" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="time-stat-item">
              <h4>今日 vs 昨日</h4>
              <div class="comparison-data">
                <div class="period-data">
                  <span class="period-label">今日</span>
                  <span class="period-orders">{{ timeDimensionStats.today?.totalOrders || 0 }}单</span>
                  <span class="period-sales">¥{{ formatAmount(timeDimensionStats.today?.totalSales) }}</span>
                </div>
                <div class="period-data">
                  <span class="period-label">昨日</span>
                  <span class="period-orders">{{ timeDimensionStats.yesterday?.totalOrders || 0 }}单</span>
                  <span class="period-sales">¥{{ formatAmount(timeDimensionStats.yesterday?.totalSales) }}</span>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="time-stat-item">
              <h4>本周 vs 上周</h4>
              <div class="comparison-data">
                <div class="period-data">
                  <span class="period-label">本周</span>
                  <span class="period-orders">{{ timeDimensionStats.thisWeek?.totalOrders || 0 }}单</span>
                  <span class="period-sales">¥{{ formatAmount(timeDimensionStats.thisWeek?.totalSales) }}</span>
                </div>
                <div class="period-data">
                  <span class="period-label">上周</span>
                  <span class="period-orders">{{ timeDimensionStats.lastWeek?.totalOrders || 0 }}单</span>
                  <span class="period-sales">¥{{ formatAmount(timeDimensionStats.lastWeek?.totalSales) }}</span>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="time-stat-item">
              <h4>本月 vs 上月</h4>
              <div class="comparison-data">
                <div class="period-data">
                  <span class="period-label">本月</span>
                  <span class="period-orders">{{ timeDimensionStats.thisMonth?.totalOrders || 0 }}单</span>
                  <span class="period-sales">¥{{ formatAmount(timeDimensionStats.thisMonth?.totalSales) }}</span>
                </div>
                <div class="period-data">
                  <span class="period-label">上月</span>
                  <span class="period-orders">{{ timeDimensionStats.lastMonth?.totalOrders || 0 }}单</span>
                  <span class="period-sales">¥{{ formatAmount(timeDimensionStats.lastMonth?.totalSales) }}</span>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>

    <!-- 订单状态分布和转化率 -->
    <div class="status-conversion-section">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>订单状态分布</span>
                <el-button @click="refreshStatusChart" :loading="loading">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
              </div>
            </template>
            <div ref="statusChartRef" style="height: 300px;"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>转化率指标</span>
              </div>
            </template>
            <div class="conversion-metrics">
              <div class="metric-item">
                <span class="metric-label">支付转化率</span>
                <span class="metric-value">{{ conversionStats.paymentRate || 0 }}%</span>
                <div class="metric-bar">
                  <div class="metric-progress" :style="{ width: (conversionStats.paymentRate || 0) + '%' }"></div>
                </div>
              </div>
              <div class="metric-item">
                <span class="metric-label">完成率</span>
                <span class="metric-value">{{ conversionStats.completionRate || 0 }}%</span>
                <div class="metric-bar">
                  <div class="metric-progress" :style="{ width: (conversionStats.completionRate || 0) + '%' }"></div>
                </div>
              </div>
              <div class="metric-item">
                <span class="metric-label">取消率</span>
                <span class="metric-value">{{ conversionStats.cancellationRate || 0 }}%</span>
                <div class="metric-bar">
                  <div class="metric-progress cancel" :style="{ width: (conversionStats.cancellationRate || 0) + '%' }"></div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 支付方式统计 -->
    <div class="payment-method-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>支付方式统计</span>
            <el-button @click="fetchPaymentMethodStats" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </template>
        <div ref="paymentChartRef" style="height: 300px;"></div>
      </el-card>
    </div>

    <!-- 商品销售排行 -->
    <div class="product-ranking-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>热销商品排行</span>
            <el-button @click="fetchProductStats" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </template>
        <el-table :data="productStats" style="width: 100%">
          <el-table-column prop="rank" label="排名" width="80" />
          <el-table-column prop="product_name" label="商品名称" />
          <el-table-column prop="totalQuantity" label="销售数量" width="120" />
          <el-table-column prop="orderCount" label="订单数" width="100" />
          <el-table-column prop="totalAmount" label="销售金额" width="150">
            <template #default="scope">
              ¥{{ formatAmount(scope.row.totalAmount) }}
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 用户维度统计 -->
    <div class="user-dimension-section">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>新老用户分布</span>
                <el-button @click="fetchUserStats" :loading="loading">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
              </div>
            </template>
            <div ref="userChartRef" style="height: 300px;"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>用户购买频次</span>
              </div>
            </template>
            <div class="user-frequency-stats">
              <div
                v-for="item in userStats.userOrderFrequency"
                :key="item.frequency_range"
                class="frequency-item"
              >
                <span class="frequency-label">{{ item.frequency_range }}</span>
                <span class="frequency-count">{{ item.userCount }}人</span>
                <div class="frequency-bar">
                  <div
                    class="frequency-progress"
                    :style="{ width: getFrequencyPercentage(item.userCount) + '%' }"
                  ></div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 趋势图表 -->
    <div class="trend-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>销售趋势</span>
            <div class="trend-controls">
              <el-radio-group v-model="trendGranularity" @change="onTrendGranularityChange">
                <el-radio-button label="hour">小时</el-radio-button>
                <el-radio-button label="day">日</el-radio-button>
                <el-radio-button label="week">周</el-radio-button>
                <el-radio-button label="month">月</el-radio-button>
              </el-radio-group>
              <el-button @click="refreshTrendChart" :loading="loading">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </div>
        </template>
        <div ref="trendChartRef" style="height: 400px;"></div>
      </el-card>
    </div>

    <!-- 实时统计 -->
    <div class="realtime-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>实时统计</span>
            <div class="realtime-controls">
              <el-switch
                v-model="autoRefresh"
                @change="toggleAutoRefresh"
                active-text="自动刷新"
                inactive-text="手动刷新"
              />
              <el-button @click="fetchRealtimeStats" :loading="loading">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="realtime-stat-item">
              <div class="realtime-value">{{ realtimeStats.totalOrders || 0 }}</div>
              <div class="realtime-label">最近1小时订单</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="realtime-stat-item">
              <div class="realtime-value">¥{{ formatAmount(realtimeStats.totalSales) }}</div>
              <div class="realtime-label">最近1小时销售额</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="realtime-stat-item">
              <div class="realtime-value">{{ realtimeStats.pendingOrders || 0 }}</div>
              <div class="realtime-label">待处理订单</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="realtime-stat-item">
              <div class="realtime-value">{{ realtimeStats.completedOrders || 0 }}</div>
              <div class="realtime-label">已完成订单</div>
            </div>
          </el-col>
        </el-row>
        <div class="realtime-footer">
          <div class="realtime-timestamp" v-if="realtimeStats.timestamp">
            最后更新时间：{{ realtimeStats.timestamp }}
          </div>
          <div class="realtime-status" v-if="autoRefresh">
            <span class="status-indicator"></span>
            自动刷新中...
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  ShoppingCart,
  Money,
  DataAnalysis,
  TrendCharts,
  Refresh
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import {
  getEnhancedStatistics,
  getTimeDimensionStatistics,
  getPaymentMethodStatistics,
  getProductStatistics,
  getUserStatistics,
  getConversionRateStatistics,
  getRealtimeStatistics,
  getComparisonStatistics,
  getTrendStatistics,
  getStatusDistribution
} from '@/api/order'
import type {
  EnhancedOrderStatisticsVO,
  TimeDimensionStatVO,
  PaymentMethodStatVO,
  ProductStatVO,
  ConversionRateStatVO,
  RealtimeStatVO,
  ComparisonDataVO
} from '@/types/order'

// 响应式数据
const loading = ref(false)
const dateRange = ref<string[]>([])
const enhancedStats = ref<EnhancedOrderStatisticsVO>({} as EnhancedOrderStatisticsVO)
const timeDimensionStats = ref<TimeDimensionStatVO>({} as TimeDimensionStatVO)
const paymentMethodStats = ref<PaymentMethodStatVO[]>([])
const productStats = ref<ProductStatVO[]>([])
const userStats = ref<any>({ newOldUserStats: [], userOrderFrequency: [] })
const conversionStats = ref<ConversionRateStatVO>({} as ConversionRateStatVO)
const realtimeStats = ref<RealtimeStatVO>({} as RealtimeStatVO)
const comparisonData = ref<any>({})
const trendGranularity = ref('day')
const autoRefresh = ref(false)

// 图表引用
const statusChartRef = ref<HTMLDivElement>()
const paymentChartRef = ref<HTMLDivElement>()
const trendChartRef = ref<HTMLDivElement>()
const userChartRef = ref<HTMLDivElement>()

// 图表实例
let statusChart: echarts.ECharts | null = null
let paymentChart: echarts.ECharts | null = null
let trendChart: echarts.ECharts | null = null
let userChart: echarts.ECharts | null = null

// 自动刷新定时器
let refreshTimer: NodeJS.Timeout | null = null

// 工具函数
const formatAmount = (amount: number | undefined): string => {
  if (amount === undefined || amount === null || isNaN(amount)) return '0.00'
  return amount.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}

// 数据验证函数
const validateApiResponse = (response: any, dataType: string): boolean => {
  if (!response || response.code !== 1) {
    console.warn(`${dataType}接口返回异常:`, response)
    return false
  }
  if (!response.data) {
    console.warn(`${dataType}接口返回数据为空`)
    return false
  }
  return true
}

// 安全获取数组数据
const safeGetArray = (data: any, key: string): any[] => {
  return Array.isArray(data?.[key]) ? data[key] : []
}

// 安全获取数值数据
const safeGetNumber = (data: any, key: string, defaultValue: number = 0): number => {
  const value = data?.[key]
  return typeof value === 'number' && !isNaN(value) ? value : defaultValue
}

// 获取默认日期范围（最近30天）
const getDefaultDateRange = (): string[] => {
  const end = new Date()
  const start = new Date()
  start.setDate(start.getDate() - 30)

  return [
    start.toISOString().split('T')[0],
    end.toISOString().split('T')[0]
  ]
}

// 重置日期范围
const resetDateRange = () => {
  dateRange.value = getDefaultDateRange()
  onDateRangeChange()
}

// 日期范围变化处理
const onDateRangeChange = () => {
  if (dateRange.value && dateRange.value.length === 2) {
    refreshAllData()
  }
}

// 趋势粒度变化处理
const onTrendGranularityChange = () => {
  refreshTrendChart()
}

// 切换自动刷新
const toggleAutoRefresh = (value: boolean) => {
  if (value) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
}

// 开始自动刷新
const startAutoRefresh = () => {
  stopAutoRefresh() // 先停止现有的定时器
  refreshTimer = setInterval(() => {
    fetchRealtimeStats()
  }, 30000) // 每30秒刷新一次实时数据
}

// 停止自动刷新
const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 获取增强统计数据
const fetchEnhancedStats = async () => {
  if (!dateRange.value || dateRange.value.length !== 2) return

  try {
    const response = await getEnhancedStatistics(dateRange.value[0], dateRange.value[1])
    if (validateApiResponse(response, '增强统计')) {
      // 安全设置数据，确保所有字段都有默认值
      enhancedStats.value = {
        totalOrders: safeGetNumber(response.data, 'totalOrders'),
        totalSales: safeGetNumber(response.data, 'totalSales'),
        avgOrderAmount: safeGetNumber(response.data, 'avgOrderAmount'),
        paymentRate: safeGetNumber(response.data, 'paymentRate'),
        completionRate: safeGetNumber(response.data, 'completionRate'),
        cancellationRate: safeGetNumber(response.data, 'cancellationRate'),
        ...response.data
      }
      console.log('增强统计数据:', enhancedStats.value)
    } else {
      // 设置默认值
      enhancedStats.value = {
        totalOrders: 0,
        totalSales: 0,
        avgOrderAmount: 0,
        paymentRate: 0,
        completionRate: 0,
        cancellationRate: 0
      } as EnhancedOrderStatisticsVO
    }
  } catch (error) {
    console.error('获取增强统计数据失败:', error)
    ElMessage.error('获取增强统计数据失败')
    // 设置默认值
    enhancedStats.value = {
      totalOrders: 0,
      totalSales: 0,
      avgOrderAmount: 0,
      paymentRate: 0,
      completionRate: 0,
      cancellationRate: 0
    } as EnhancedOrderStatisticsVO
  }
}

// 获取时间维度统计
const fetchTimeDimensionStats = async () => {
  try {
    const response = await getTimeDimensionStatistics()
    if (response.code === 1) {
      timeDimensionStats.value = response.data
      console.log('时间维度统计数据:', response.data)
    }
  } catch (error) {
    console.error('获取时间维度统计失败:', error)
    ElMessage.error('获取时间维度统计失败')
  }
}

// 获取支付方式统计
const fetchPaymentMethodStats = async () => {
  if (!dateRange.value || dateRange.value.length !== 2) return

  try {
    const response = await getPaymentMethodStatistics(dateRange.value[0], dateRange.value[1])
    if (response.code === 1) {
      paymentMethodStats.value = response.data.paymentMethodStats || []
      console.log('支付方式统计数据:', response.data)
      nextTick(() => {
        initPaymentChart()
      })
    }
  } catch (error) {
    console.error('获取支付方式统计失败:', error)
    ElMessage.error('获取支付方式统计失败')
  }
}

// 获取商品统计
const fetchProductStats = async () => {
  if (!dateRange.value || dateRange.value.length !== 2) return

  try {
    const response = await getProductStatistics(dateRange.value[0], dateRange.value[1], 10)
    if (response.code === 1) {
      productStats.value = response.data.topProducts || []
      console.log('商品统计数据:', response.data)
    }
  } catch (error) {
    console.error('获取商品统计失败:', error)
    ElMessage.error('获取商品统计失败')
  }
}

// 获取转化率统计
const fetchConversionStats = async () => {
  if (!dateRange.value || dateRange.value.length !== 2) return

  try {
    const response = await getConversionRateStatistics(dateRange.value[0], dateRange.value[1])
    if (response.code === 1) {
      conversionStats.value = response.data
      console.log('转化率统计数据:', response.data)
    }
  } catch (error) {
    console.error('获取转化率统计失败:', error)
    ElMessage.error('获取转化率统计失败')
  }
}

// 获取用户统计
const fetchUserStats = async () => {
  if (!dateRange.value || dateRange.value.length !== 2) return

  try {
    const response = await getUserStatistics(dateRange.value[0], dateRange.value[1])
    if (response.code === 1) {
      userStats.value = response.data
      console.log('用户统计数据:', response.data)
      nextTick(() => {
        initUserChart()
      })
    }
  } catch (error) {
    console.error('获取用户统计失败:', error)
    ElMessage.error('获取用户统计失败')
  }
}

// 计算用户频次百分比
const getFrequencyPercentage = (count: number): number => {
  const total = userStats.value.userOrderFrequency.reduce((sum: number, item: any) => sum + item.userCount, 0)
  return total > 0 ? (count / total) * 100 : 0
}

// 获取实时统计
const fetchRealtimeStats = async () => {
  try {
    const response = await getRealtimeStatistics()
    if (validateApiResponse(response, '实时统计')) {
      // 安全设置实时统计数据
      realtimeStats.value = {
        totalOrders: safeGetNumber(response.data, 'totalOrders'),
        totalSales: safeGetNumber(response.data, 'totalSales'),
        pendingOrders: safeGetNumber(response.data, 'pendingOrders'),
        completedOrders: safeGetNumber(response.data, 'completedOrders'),
        timestamp: response.data.timestamp || new Date().toLocaleString('zh-CN')
      }
      console.log('实时统计数据:', realtimeStats.value)
    } else {
      // 设置默认值
      realtimeStats.value = {
        totalOrders: 0,
        totalSales: 0,
        pendingOrders: 0,
        completedOrders: 0,
        timestamp: new Date().toLocaleString('zh-CN')
      }
    }
  } catch (error) {
    console.error('获取实时统计失败:', error)
    // 只在手动刷新时显示错误消息，自动刷新时静默处理
    if (!autoRefresh.value) {
      ElMessage.error('获取实时统计失败')
    }
    // 设置默认值
    realtimeStats.value = {
      totalOrders: 0,
      totalSales: 0,
      pendingOrders: 0,
      completedOrders: 0,
      timestamp: new Date().toLocaleString('zh-CN')
    }
  }
}

// 获取对比数据
const fetchComparisonData = async () => {
  if (!dateRange.value || dateRange.value.length !== 2) return

  try {
    const response = await getComparisonStatistics(dateRange.value[0], dateRange.value[1])
    if (response.code === 1) {
      comparisonData.value = response.data.yearOverYearGrowth || {}
      console.log('对比统计数据:', response.data)
    }
  } catch (error) {
    console.error('获取对比统计失败:', error)
    ElMessage.error('获取对比统计失败')
  }
}

// 获取趋势数据
const fetchTrendData = async () => {
  if (!dateRange.value || dateRange.value.length !== 2) return

  try {
    const response = await getTrendStatistics(dateRange.value[0], dateRange.value[1], trendGranularity.value)
    if (response.code === 1) {
      console.log('趋势统计数据:', response.data)
      nextTick(() => {
        initTrendChart(response.data)
      })
    }
  } catch (error) {
    console.error('获取趋势统计失败:', error)
    ElMessage.error('获取趋势统计失败')
  }
}

// 初始化状态分布图表
const initStatusChart = async () => {
  if (!statusChartRef.value) return

  try {
    const response = await getStatusDistribution()
    if (response.code !== 1) return

    const data = response.data || {}
    const chartData = [
      { name: '待付款', value: data.pendingPayment || 0 },
      { name: '已付款', value: data.paid || 0 },
      { name: '处理中', value: data.processing || 0 },
      { name: '已发货', value: data.shipped || 0 },
      { name: '已完成', value: data.completed || 0 },
      { name: '已取消', value: data.cancelled || 0 },
      { name: '已退款', value: data.refunded || 0 }
    ].filter(item => item.value > 0)

    if (statusChart) {
      statusChart.dispose()
    }

    statusChart = echarts.init(statusChartRef.value)

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c}单 ({d}%)',
        backgroundColor: 'rgba(50, 50, 50, 0.9)',
        borderColor: '#409eff',
        borderWidth: 1,
        textStyle: {
          color: '#fff'
        }
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        textStyle: {
          fontSize: 12
        }
      },
      color: ['#409eff', '#67c23a', '#e6a23c', '#f56c6c', '#909399', '#c0c4cc', '#d3dce6'],
      series: [
        {
          name: '订单状态',
          type: 'pie',
          radius: ['30%', '70%'],
          center: ['60%', '50%'],
          data: chartData,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          label: {
            show: true,
            formatter: '{b}: {c}单\n({d}%)',
            fontSize: 11
          },
          labelLine: {
            show: true
          }
        }
      ]
    }

    statusChart.setOption(option)
  } catch (error) {
    console.error('初始化状态图表失败:', error)
  }
}

// 初始化支付方式图表
const initPaymentChart = () => {
  if (!paymentChartRef.value || !paymentMethodStats.value.length) return

  if (paymentChart) {
    paymentChart.dispose()
  }

  paymentChart = echarts.init(paymentChartRef.value)

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: function(params: any) {
        const percentage = params.data.percentage || params.percent
        return `${params.seriesName}<br/>${params.name}: ¥${formatAmount(params.value)} (${percentage.toFixed(1)}%)`
      },
      backgroundColor: 'rgba(50, 50, 50, 0.9)',
      borderColor: '#409eff',
      borderWidth: 1,
      textStyle: {
        color: '#fff'
      }
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      textStyle: {
        fontSize: 12
      }
    },
    color: ['#409eff', '#67c23a', '#e6a23c', '#f56c6c', '#909399'],
    series: [
      {
        name: '支付方式',
        type: 'pie',
        radius: ['30%', '70%'],
        center: ['60%', '50%'],
        data: paymentMethodStats.value.map(item => ({
          name: item.methodName,
          value: item.totalAmount,
          percentage: item.percentage
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        label: {
          show: true,
          formatter: function(params: any) {
            const percentage = params.data.percentage || params.percent
            return `${params.name}\n¥${formatAmount(params.value)}\n(${percentage.toFixed(1)}%)`
          },
          fontSize: 11
        },
        labelLine: {
          show: true
        }
      }
    ]
  }

  paymentChart.setOption(option)
}

// 初始化用户图表
const initUserChart = () => {
  if (!userChartRef.value || !userStats.value.newOldUserStats.length) return

  if (userChart) {
    userChart.dispose()
  }

  userChart = echarts.init(userChartRef.value)

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: function(params: any) {
        return `${params.seriesName}<br/>${params.name}: ${params.value}单 (${params.percent.toFixed(1)}%)<br/>销售额: ¥${formatAmount(params.data.amount || 0)}`
      },
      backgroundColor: 'rgba(50, 50, 50, 0.9)',
      borderColor: '#409eff',
      borderWidth: 1,
      textStyle: {
        color: '#fff'
      }
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      textStyle: {
        fontSize: 12
      }
    },
    color: ['#409eff', '#67c23a'],
    series: [
      {
        name: '用户类型',
        type: 'pie',
        radius: ['30%', '70%'],
        center: ['60%', '50%'],
        data: userStats.value.newOldUserStats.map((item: any) => ({
          name: item.userType === 'new' ? '新用户' : '老用户',
          value: item.orderCount,
          amount: item.totalAmount
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        label: {
          show: true,
          formatter: function(params: any) {
            return `${params.name}\n${params.value}单\n(${params.percent.toFixed(1)}%)`
          },
          fontSize: 11
        },
        labelLine: {
          show: true
        }
      }
    ]
  }

  userChart.setOption(option)
}

// 初始化趋势图表
const initTrendChart = (trendData: any) => {
  if (!trendChartRef.value || !trendData) return

  if (trendChart) {
    trendChart.dispose()
  }

  trendChart = echarts.init(trendChartRef.value)

  const statisticsData = trendData.dailyStatistics || trendData.hourlyStatistics || []
  const dates = statisticsData.map((item: any) => item.date || item.hour)
  const orderCounts = statisticsData.map((item: any) => item.orderCount)
  const salesAmounts = statisticsData.map((item: any) => item.amount)

  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: function(params: any) {
        let result = `<div style="font-weight: bold; margin-bottom: 5px;">${params[0].name}</div>`
        params.forEach((param: any) => {
          if (param.seriesName === '销售额') {
            result += `<div>${param.marker} ${param.seriesName}: ¥${formatAmount(param.value)}</div>`
          } else {
            result += `<div>${param.marker} ${param.seriesName}: ${param.value}单</div>`
          }
        })
        return result
      },
      backgroundColor: 'rgba(50, 50, 50, 0.9)',
      borderColor: '#409eff',
      borderWidth: 1,
      textStyle: {
        color: '#fff'
      }
    },
    legend: {
      data: ['订单数量', '销售额'],
      top: 10
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLabel: {
        rotate: dates.length > 10 ? 45 : 0,
        fontSize: 11
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '订单数量',
        position: 'left',
        axisLabel: {
          formatter: '{value}单',
          fontSize: 11
        },
        nameTextStyle: {
          fontSize: 12
        }
      },
      {
        type: 'value',
        name: '销售额',
        position: 'right',
        axisLabel: {
          formatter: function(value: number) {
            return '¥' + (value >= 10000 ? (value / 10000).toFixed(1) + 'w' : value.toFixed(0))
          },
          fontSize: 11
        },
        nameTextStyle: {
          fontSize: 12
        }
      }
    ],
    series: [
      {
        name: '订单数量',
        type: 'line',
        data: orderCounts,
        yAxisIndex: 0,
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          width: 3,
          color: '#409eff'
        },
        itemStyle: {
          color: '#409eff'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: 'rgba(64, 158, 255, 0.3)'
            }, {
              offset: 1, color: 'rgba(64, 158, 255, 0.1)'
            }]
          }
        }
      },
      {
        name: '销售额',
        type: 'bar',
        data: salesAmounts,
        yAxisIndex: 1,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: '#67c23a'
            }, {
              offset: 1, color: '#85ce61'
            }]
          }
        },
        barWidth: '60%'
      }
    ]
  }

  trendChart.setOption(option)
}

// 刷新状态图表
const refreshStatusChart = () => {
  initStatusChart()
}

// 刷新趋势图表
const refreshTrendChart = () => {
  fetchTrendData()
}

// 刷新所有数据
const refreshAllData = async () => {
  loading.value = true
  try {
    await Promise.all([
      fetchEnhancedStats(),
      fetchTimeDimensionStats(),
      fetchPaymentMethodStats(),
      fetchProductStats(),
      fetchUserStats(),
      fetchConversionStats(),
      fetchComparisonData(),
      fetchTrendData()
    ])

    nextTick(() => {
      initStatusChart()
    })
  } catch (error) {
    console.error('刷新数据失败:', error)
    ElMessage.error('刷新数据失败')
  } finally {
    loading.value = false
  }
}

// 组件挂载
onMounted(async () => {
  // 设置默认日期范围
  dateRange.value = getDefaultDateRange()

  // 加载初始数据
  await refreshAllData()

  // 启动实时统计
  fetchRealtimeStats()
})

// 组件卸载
onUnmounted(() => {
  stopAutoRefresh()

  // 销毁图表实例
  if (statusChart) {
    statusChart.dispose()
    statusChart = null
  }
  if (paymentChart) {
    paymentChart.dispose()
    paymentChart = null
  }
  if (trendChart) {
    trendChart.dispose()
    trendChart = null
  }
  if (userChart) {
    userChart.dispose()
    userChart = null
  }
})
</script>

<style scoped lang="scss">
.order-statistics-container {
  padding: 20px;

  .page-header {
    margin-bottom: 20px;

    h2 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 24px;
      font-weight: 600;
    }

    p {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }

  .date-filter {
    margin-bottom: 20px;

    .filter-content {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .filter-item {
        display: flex;
        align-items: center;
        gap: 10px;

        label {
          font-weight: 500;
          color: #606266;
        }
      }

      .filter-actions {
        display: flex;
        gap: 10px;
      }
    }
  }

  .core-stats-section {
    margin-bottom: 20px;

    .stats-card {
      .stats-content {
        display: flex;
        align-items: center;
        padding: 20px;

        .stats-icon {
          width: 60px;
          height: 60px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;
          font-size: 24px;
          color: white;

          &.total-orders {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          }

          &.total-sales {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
          }

          &.avg-amount {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
          }

          &.conversion-rate {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
          }
        }

        .stats-info {
          flex: 1;

          .stats-value {
            font-size: 28px;
            font-weight: 700;
            color: #303133;
            line-height: 1;
            margin-bottom: 4px;
          }

          .stats-label {
            font-size: 14px;
            color: #909399;
          }

          .stats-trend {
            margin-top: 4px;
            font-size: 12px;

            .trend-up {
              color: #67c23a;
            }

            .trend-down {
              color: #f56c6c;
            }
          }
        }
      }
    }
  }

  .time-dimension-section,
  .status-conversion-section,
  .payment-method-section,
  .product-ranking-section,
  .user-dimension-section,
  .trend-section,
  .realtime-section {
    margin-bottom: 20px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .trend-controls,
    .realtime-controls {
      display: flex;
      align-items: center;
      gap: 10px;
    }
  }

  .time-stat-item {
    text-align: center;

    h4 {
      margin: 0 0 16px 0;
      color: #303133;
      font-size: 16px;
    }

    .comparison-data {
      .period-data {
        display: flex;
        flex-direction: column;
        gap: 4px;
        margin-bottom: 12px;

        .period-label {
          font-size: 12px;
          color: #909399;
        }

        .period-orders {
          font-size: 18px;
          font-weight: 600;
          color: #409eff;
        }

        .period-sales {
          font-size: 16px;
          font-weight: 500;
          color: #67c23a;
        }
      }
    }
  }

  .conversion-metrics {
    .metric-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;

      .metric-label {
        font-size: 14px;
        color: #606266;
        min-width: 80px;
      }

      .metric-value {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        min-width: 60px;
        text-align: right;
      }

      .metric-bar {
        flex: 1;
        height: 8px;
        background-color: #f5f7fa;
        border-radius: 4px;
        margin: 0 12px;
        overflow: hidden;

        .metric-progress {
          height: 100%;
          background: linear-gradient(90deg, #409eff 0%, #67c23a 100%);
          border-radius: 4px;
          transition: width 0.3s ease;

          &.cancel {
            background: linear-gradient(90deg, #f56c6c 0%, #ff9a9e 100%);
          }
        }
      }
    }
  }

  .realtime-stat-item {
    text-align: center;
    padding: 20px;
    border: 1px solid #ebeef5;
    border-radius: 8px;

    .realtime-value {
      font-size: 24px;
      font-weight: 700;
      color: #303133;
      margin-bottom: 8px;
    }

    .realtime-label {
      font-size: 14px;
      color: #909399;
    }
  }

  .realtime-footer {
    margin-top: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .realtime-timestamp {
      font-size: 12px;
      color: #c0c4cc;
    }

    .realtime-status {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 12px;
      color: #67c23a;

      .status-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: #67c23a;
        animation: pulse 2s infinite;
      }
    }
  }

  @keyframes pulse {
    0% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.5;
      transform: scale(1.2);
    }
    100% {
      opacity: 1;
      transform: scale(1);
    }
  }

  .user-frequency-stats {
    .frequency-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;

      .frequency-label {
        font-size: 14px;
        color: #606266;
        min-width: 80px;
      }

      .frequency-count {
        font-size: 14px;
        font-weight: 600;
        color: #303133;
        min-width: 60px;
        text-align: right;
      }

      .frequency-bar {
        flex: 1;
        height: 8px;
        background-color: #f5f7fa;
        border-radius: 4px;
        margin: 0 12px;
        overflow: hidden;

        .frequency-progress {
          height: 100%;
          background: linear-gradient(90deg, #409eff 0%, #67c23a 100%);
          border-radius: 4px;
          transition: width 0.3s ease;
        }
      }
    }
  }
}
</style>
