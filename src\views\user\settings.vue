<template>
  <div class="settings-container">
    <div class="settings-content">
      <el-card class="settings-card" shadow="hover">
        <div class="card-header">
          <div class="header-icon">
            <el-icon><Lock /></el-icon>
          </div>
          <div class="header-text">
            <h2 class="page-title">修改密码</h2>
            <p class="page-subtitle">为了保护账户安全，请定期更新密码</p>
          </div>
        </div>

        <div class="form-container">
          <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" status-icon>
            <el-form-item label="当前密码" prop="oldPassword">
              <el-input
                v-model="form.oldPassword"
                type="password"
                placeholder="请输入当前密码"
                show-password
                clearable
              />
            </el-form-item>

            <el-form-item label="新密码" prop="newPassword">
              <el-input
                v-model="form.newPassword"
                type="password"
                placeholder="请输入新密码"
                show-password
                clearable
              />
              <div class="password-requirements">
                <div class="requirement-title">密码要求：</div>
                <div class="requirement-item" :class="{ satisfied: lengthValid }">
                  <el-icon><Check /></el-icon>
                  长度在6-20位之间
                </div>
                <div class="requirement-item" :class="{ satisfied: formatValid }">
                  <el-icon><Check /></el-icon>
                  包含大小写字母和数字
                </div>
              </div>
            </el-form-item>

            <el-form-item label="确认新密码" prop="confirmPassword">
              <el-input
                v-model="form.confirmPassword"
                type="password"
                placeholder="请再次输入新密码"
                show-password
                clearable
              />
            </el-form-item>

            <div class="form-actions">
              <el-button type="primary" :loading="loading" @click="handleSubmit" class="submit-btn">
                确认修改
              </el-button>
              <el-button @click="resetForm">重置</el-button>
            </div>
          </el-form>
        </div>

        <div class="tips-section">
          <el-alert title="安全提示" type="info" :closable="false" show-icon>
            <template #default>
              <div class="tips-content">
                <p>· 建议使用字母、数字和符号的组合</p>
                <p>· 请勿使用易被猜测的密码（如生日、手机号等）</p>
                <p>· 定期更换密码可以提高账户安全性</p>
              </div>
            </template>
          </el-alert>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Lock, Check } from '@element-plus/icons-vue'
import request from '@/utils/request'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()
const formRef = ref()
const loading = ref(false)

const form = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: '',
})

// 密码验证计算属性
const lengthValid = computed(() => {
  const len = form.newPassword.length
  return len >= 6 && len <= 20
})

const formatValid = computed(() => {
  const regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{6,20}$/
  return regex.test(form.newPassword)
})

// 验证规则
const validateNewPassword = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback(new Error('请输入新密码'))
  } else if (!lengthValid.value) {
    callback(new Error('密码长度必须在6-20位之间'))
  } else if (!formatValid.value) {
    callback(new Error('密码必须包含大小写字母和数字'))
  } else {
    callback()
  }
}

const validateConfirmPassword = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback(new Error('请再次输入新密码'))
  } else if (value !== form.newPassword) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

const rules = {
  oldPassword: [{ required: true, message: '请输入当前密码', trigger: 'blur' }],
  newPassword: [{ required: true, validator: validateNewPassword, trigger: 'blur' }],
  confirmPassword: [{ required: true, validator: validateConfirmPassword, trigger: 'blur' }],
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    // 获取当前用户ID
    const userId = userStore.userInfo?.id
    if (!userId) {
      ElMessage.error('获取用户信息失败，请重新登录')
      return
    }

    const res = await request({
      url: '/seller/PassWord',
      method: 'post',
      data: {
        id: userId.toString(),
        oldPassword: form.oldPassword,
        newPassword: form.newPassword,
      },
    })

    if (res.code === 1) {
      ElMessage.success('密码修改成功')
      resetForm()
    } else {
      ElMessage.error(res.msg || '密码修改失败')
    }
  } catch (error) {
    console.error('密码修改失败:', error)
    ElMessage.error('密码修改失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  form.oldPassword = ''
  form.newPassword = ''
  form.confirmPassword = ''
}
</script>

<style scoped lang="scss">
.settings-container {
  min-height: calc(100vh - 60px);
  padding: 40px;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e7eb 100%);

  .settings-content {
    max-width: 800px;
    margin: 0 auto;
  }

  .settings-card {
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    background-color: #fff;
    overflow: hidden;

    .card-header {
      padding: 30px;
      background: linear-gradient(135deg, #3a7bd5, #2c5499);
      color: white;
      display: flex;
      align-items: center;
      gap: 20px;

      .header-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;

        .el-icon {
          font-size: 24px;
        }
      }

      .header-text {
        .page-title {
          margin: 0;
          font-size: 24px;
          font-weight: 600;
        }

        .page-subtitle {
          margin: 8px 0 0;
          font-size: 14px;
          opacity: 0.8;
        }
      }
    }

    .form-container {
      padding: 40px;

      :deep(.el-form-item) {
        margin-bottom: 30px;
      }

      :deep(.el-input__wrapper) {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        padding: 0 15px;
        height: 44px;
        transition: all 0.3s;

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }

        &.is-focus {
          box-shadow: 0 0 0 1px var(--el-color-primary);
        }
      }
    }

    .password-requirements {
      margin-top: 12px;
      font-size: 13px;
      color: #606266;

      .requirement-title {
        margin-bottom: 8px;
        color: #303133;
        font-weight: 500;
      }

      .requirement-item {
        display: flex;
        align-items: center;
        gap: 6px;
        margin-bottom: 6px;
        color: #909399;

        .el-icon {
          font-size: 14px;
          opacity: 0.5;
        }

        &.satisfied {
          color: #67c23a;

          .el-icon {
            opacity: 1;
          }
        }
      }
    }

    .form-actions {
      display: flex;
      justify-content: center;
      gap: 20px;
      margin-top: 40px;

      .submit-btn {
        min-width: 120px;
      }
    }

    .tips-section {
      padding: 0 40px 40px;

      :deep(.el-alert) {
        background-color: #f5f7fa;
        border: none;
        border-radius: 8px;
      }

      .tips-content {
        margin-top: 8px;
        color: #606266;
        font-size: 13px;
        line-height: 1.8;

        p {
          margin: 0;
        }
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .settings-container {
    padding: 20px;

    .settings-card {
      .card-header {
        padding: 20px;

        .header-icon {
          width: 40px;
          height: 40px;

          .el-icon {
            font-size: 20px;
          }
        }

        .page-title {
          font-size: 20px;
        }
      }

      .form-container {
        padding: 20px;
      }

      .tips-section {
        padding: 0 20px 20px;
      }
    }
  }
}
</style>
