<template>
  <div class="page-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="page-header">
          <h2 class="page-title">编辑商品</h2>
          <div class="page-actions">
            <el-button @click="handleCancel">取消</el-button>
            <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
              保存修改
            </el-button>
          </div>
        </div>
      </template>

      <div class="page-content">
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="120px"
          class="product-form"
          @submit.prevent
          v-loading="loading"
        >
          <!-- 基本信息 -->
          <div class="form-section">
            <h3 class="section-title">基本信息</h3>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="商品名称" prop="name">
                  <el-input v-model="form.name" placeholder="请输入商品名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="商品分类" prop="productCategoryId">
                  <el-select
                    v-model="form.productCategoryId"
                    placeholder="请选择商品分类"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="category in thirdLevelCategories"
                      :key="category.id"
                      :label="category.fullName"
                      :value="category.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="商品编码" prop="out_product_id">
                  <el-input v-model="form.out_product_id" placeholder="商品编码" disabled>
                    <template #append>
                      <el-tooltip content="商品编码是系统生成的唯一标识" placement="top">
                        <el-icon><InfoFilled /></el-icon>
                      </el-tooltip>
                    </template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="品牌" prop="brand_name">
                  <el-input v-model="form.brand_name" placeholder="请输入品牌名称" clearable />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="商品价格" prop="price">
                  <el-input-number
                    v-model="form.price"
                    :min="0"
                    :precision="2"
                    :step="10"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="商品单位" prop="unit">
                  <el-select v-model="form.unit" placeholder="请选择商品单位">
                    <el-option
                      v-for="unit in unitOptions"
                      :key="unit"
                      :label="unit"
                      :value="unit"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="商品重量(克)" prop="weight">
                  <el-input-number
                    v-model="form.weight"
                    :min="0"
                    :precision="2"
                    :step="0.1"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="商品排序" prop="sort">
                  <el-input-number v-model="form.sort" :min="0" style="width: 100%" />
                  <div class="field-tip">
                    <el-icon><InfoFilled /></el-icon>
                    <span>排序值越小越靠前，用于控制商品在列表中的显示顺序</span>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="上架状态" prop="publish_status">
                  <el-radio-group v-model="form.publish_status">
                    <el-radio :label="1">上架</el-radio>
                    <el-radio :label="0">下架</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="当前库存" prop="inventory">
                  <el-input-number v-model="form.inventory" :min="0"></el-input-number>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 商品图片 -->
          <div class="form-section">
            <h3 class="section-title">商品图片</h3>
            <el-form-item label="商品主图" prop="pic">
              <el-upload
                class="main-image-uploader"
                action="#"
                :auto-upload="false"
                :show-file-list="false"
                :on-change="handleMainImageChange"
              >
                <img v-if="form.pic" :src="form.pic" class="main-image" />
                <div v-else class="upload-placeholder">
                  <el-icon><Plus /></el-icon>
                  <div class="upload-text">上传主图</div>
                </div>
              </el-upload>
              <div class="upload-tip">建议尺寸：800x800px，支持jpg、png格式</div>
            </el-form-item>

            <el-form-item label="商品相册" prop="album_pics">
              <el-upload
                class="gallery-uploader"
                action="#"
                :auto-upload="false"
                list-type="picture-card"
                :on-change="handleGalleryChange"
                :on-remove="handleGalleryRemove"
                :file-list="galleryList"
                multiple
              >
                <el-icon><Plus /></el-icon>
              </el-upload>
              <div class="upload-tip">最多上传5张商品展示图，建议尺寸：800x800px</div>
            </el-form-item>
          </div>

          <!-- 商品属性 -->
          <div class="form-section">
            <h3 class="section-title">商品属性</h3>
            <el-form-item label="商品属性" prop="product_attr">
              <div class="attr-container">
                <!-- 左侧：属性选择 -->
                <div class="attr-selection-panel attr-child">
                  <el-tabs type="border-card">
                    <el-tab-pane
                      v-for="(group, groupIndex) in predefinedAttrs"
                      :key="groupIndex"
                      :label="group.groupName"
                    >
                      <div class="attr-group">
                        <el-checkbox-group v-model="selectedAttrs[groupIndex]">
                          <el-button
                            v-for="attr in group.attrs"
                            :key="attr"
                            :label="attr"
                            @click="
                              handleCheckboxChange(
                                $event,
                                selectedAttrs[groupIndex],
                                groupIndex,
                                attr,
                              )
                            "
                            class="attr-checkbox"
                          >
                            {{ attr }}
                          </el-button>
                        </el-checkbox-group>
                      </div>
                    </el-tab-pane>
                  </el-tabs>
                </div>

                <!-- 右侧：已选属性展示 -->
                <div class="attr-preview-panel attr-child">
                  <div class="panel-header">
                    <span class="panel-title">已选择的属性</span>
                    <span class="attr-count" v-if="form.attrList.length > 0">
                      共 <b>{{ form.attrList.length }}</b> 个属性
                    </span>
                  </div>

                  <div class="attr-tags-container">
                    <div v-if="form.attrList.length === 0" class="no-attrs">
                      <el-empty description="暂无选择的属性" :image-size="80">
                        <template #description>
                          <p class="empty-tip">从左侧选择商品属性</p>
                          <p class="empty-tip-sub">添加属性将帮助买家更好地了解您的商品</p>
                        </template>
                      </el-empty>
                    </div>
                    <div v-else class="attr-list">
                      <div v-for="(attr, index) in form.attrList" :key="index" class="attr-item">
                        <el-dropdown class="attr-dropDown" size="large">
                          <el-button type="primary" class="attr-button">
                            {{ attr.key }}
                            <el-icon class="el-icon--right"><arrow-down /></el-icon>
                          </el-button>
                          <template #dropdown>
                            <el-dropdown-menu class="attr-dropDown">
                              <el-dropdown-item
                                v-for="(inputAttrValue, attrIndex) in attr.value"
                                :key="attrIndex"
                              >
                                <div class="attr-item-content">
                                  <div class="attr-item-value">{{ inputAttrValue }}</div>
                                </div>
                                <div class="attr-item-actions">
                                  <el-tooltip content="编辑属性值" placement="top">
                                    <el-button
                                      type="primary"
                                      link
                                      circle
                                      @click="
                                        editAttrValue(index, attr.key, inputAttrValue, attrIndex)
                                      "
                                    >
                                      <el-icon><Edit /></el-icon>
                                    </el-button>
                                  </el-tooltip>
                                  <el-tooltip content="删除属性" placement="top">
                                    <el-button
                                      type="danger"
                                      link
                                      circle
                                      @click="handleRemoveAttr(index, attrIndex)"
                                    >
                                      <el-icon><Delete /></el-icon>
                                    </el-button>
                                  </el-tooltip>
                                </div>
                              </el-dropdown-item>
                            </el-dropdown-menu>
                          </template>
                        </el-dropdown>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 属性值输入对话框 -->
              <el-dialog
                v-model="dialogVisible"
                :title="
                  currentAction === 'add' ? `请输入 ${currentKey} 的值` : `编辑 ${currentKey} 的值`
                "
                width="30%"
                :close-on-click-modal="false"
                :show-close="true"
                align-center
                destroy-on-close
              >
                <el-form @submit.prevent>
                  <el-form-item :label="currentKey">
                    <el-input
                      ref="attrValueInput"
                      v-model="attrValue"
                      placeholder="请输入属性值"
                      autofocus
                      @keyup.enter="handleValueConfirm"
                    >
                      <template #prefix v-if="currentAction === 'edit'">
                        <el-icon><Edit /></el-icon>
                      </template>
                    </el-input>
                  </el-form-item>
                  <div class="attr-tips">
                    <el-alert
                      title="请保持属性值简洁、准确，有助于买家理解商品特性"
                      type="info"
                      :closable="false"
                      show-icon
                    />
                  </div>
                </el-form>
                <template #footer>
                  <span class="dialog-footer">
                    <el-button @click="cancelAttributeValue">取消</el-button>
                    <el-button type="primary" @click="handleValueConfirm">确认</el-button>
                  </span>
                </template>
              </el-dialog>
            </el-form-item>
          </div>

          <!-- 商品详情部分 -->
          <div class="form-section">
            <h3 class="section-title">商品详情</h3>
            <el-form-item label="商品详情" prop="detail_html">
              <el-input
                v-model="form.detail_html"
                type="textarea"
                :rows="6"
                placeholder="请输入商品详情HTML"
              />
            </el-form-item>

            <!-- 图文介绍 -->
            <el-form-item label="图文介绍" prop="graphic_introduction">
              <el-upload
                list-type="picture-card"
                action="#"
                :auto-upload="false"
                :on-change="handleIntroductionChange"
                :on-remove="handleIntroductionRemove"
                multiple
                :file-list="graphicIntroductList"
              >
                <el-icon><Plus /></el-icon>
              </el-upload>
              <div class="upload-tip">建议尺寸：800x800px，支持jpg、png格式</div>
            </el-form-item>

            <!-- PDF文档 -->
            <el-form-item label="商品文档(PDF)" prop="pdf_document">
              <el-upload
                class="pdf-uploader"
                action="#"
                :auto-upload="false"
                :show-file-list="false"
                :on-change="handlePdfChange"
                :on-remove="handlePdfRemove"
                :limit="1"
                :before-upload="beforePdfUpload"
                accept=".pdf,application/pdf"
              >
                <el-button type="primary" plain>
                  <el-icon><Document /></el-icon>
                  <span>{{ pdfFile ? '更换PDF文档' : '上传PDF文档' }}</span>
                </el-button>
                <template #tip>
                  <div class="upload-tip">仅支持PDF格式，最大10MB</div>
                  <div v-if="pdfFile" class="upload-size-info">
                    <span class="size-text">
                      PDF大小：{{ (pdfFile.size / 1024 / 1024).toFixed(2) }}MB / 10MB
                    </span>
                    <el-progress
                      :percentage="(pdfFile.size / 1024 / 1024) * 10"
                      :status="pdfFile.size / 1024 / 1024 > 10 ? 'exception' : ''"
                      :stroke-width="8"
                    ></el-progress>
                  </div>
                </template>
              </el-upload>
              <div v-if="pdfFile" class="pdf-file-info">
                <span class="file-name">{{ pdfFile.name }}</span>
                <el-button type="danger" link @click="handlePdfRemove">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </el-form-item>
          </div>
        </el-form>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick, onMounted, onBeforeMount } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Plus, Edit, Delete, InfoFilled } from '@element-plus/icons-vue'
import type { UploadUserFile } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { getProductDetail, getSellerProductDetail, updateSellerProduct } from '@/api/product'
import { getCategoryTree } from '@/api/category'

// 定义API响应接口
interface ApiResponse<T = any> {
  code: number
  msg: string | null
  data: T | null
}
const productName = ref('')
const router = useRouter()
const route = useRoute()
const formRef = ref()
const submitLoading = ref(false)
const loading = ref(true)
const galleryList = ref<UploadUserFile[]>([])
const graphicIntroductList = ref<UploadUserFile[]>([])
// 获取商品ID
const productId = ref<number>(Number(route.params.id))

// 分类数据
const categoryOptions = ref<any[]>([])
const thirdLevelCategories = ref<{ id: number; fullName: string }[]>([])

// 品牌选项
const brandOptions = [
  { id: 1, name: '华为' },
  { id: 2, name: '小米' },
  { id: 3, name: '苹果' },
  { id: 4, name: '三星' },
  { id: 5, name: 'OPPO' },
  { id: 6, name: 'vivo' },
]

// 单位选项
const unitOptions = ['件', '个', '套', '盒', '袋', '箱', '瓶', '千克', '克', '米', '厘米']

// 添加属性输入状态
const dialogVisible = ref(false)
const attrValue = ref('')

const inputVisible = reactive({
  key: false,
  value: false,
})

const inputValue = reactive({
  key: '',
  value: '',
})
interface CurrentAttr {
  index: number
  value: string
}
// 商品属性列表项form.attrList.value项
const currentAttr = reactive<CurrentAttr>({
  index: 0,
  value: '',
})
const currentKey = ref('')
const attrValueInput = ref(null)
const currentAction = ref('add') // 'add' 或 'edit'
const currentEditIndex = ref(-1) // 正在编辑的属性索引

// 预定义的销售属性
const predefinedAttrs = [
  {
    groupName: '基本属性',
    attrs: ['颜色', '尺寸', '材质', '款式', '重量'],
  },
  {
    groupName: '电子产品',
    attrs: ['内存', '存储容量', '处理器', '屏幕尺寸', '电池容量', '操作系统'],
  },
  {
    groupName: '服装',
    attrs: ['季节', '适用人群', '领型', '袖长', '风格', '版型'],
  },
  {
    groupName: '食品',
    attrs: ['口味', '产地', '保质期', '包装', '净含量'],
  },
] as { groupName: string; attrs: string[] }[]

// 已选择的属性（用于按钮组）
const selectedAttrs = ref(Array(predefinedAttrs.length).fill([]))

// 表单数据
const form = reactive({
  id: 0,
  product_snapshot_id: null,
  brand_id: null,
  category_id: null,
  productCategoryId: null,
  out_product_id: '',
  name: '',
  pic: '',
  album_pics: '',
  introduce_pics: '',
  product_pdf: '',
  publish_status: 1,
  sort: 0,
  inventory: 0,
  price: 0,
  unit: '件',
  weight: 0,
  detail_html: '',
  brand_name: '',
  product_category_name: '',
  product_attr: '',
  attrList: [] as { key: string; value: string[] }[],
})

// 在表单数据中添加存储原始文件的字段
const mainPicFile = ref<File | null>(null)
const albumPicFiles = ref<File[]>([])
const graphicIntroductFiles = ref<File[]>([])
const pdfFile = ref<File | null>(null)
// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入商品名称', trigger: 'blur' },
    { min: 2, max: 64, message: '长度在 2 到 64 个字符', trigger: 'blur' },
  ],
  category_id: [{ required: true, message: '请选择商品分类', trigger: 'change' }],
  price: [{ required: true, message: '请输入商品价格', trigger: 'blur' }],
  inventory: [{ required: true, message: '请输入商品库存', trigger: 'blur' }],
}

// 处理属性按钮点击
const handleAttrChange = (val: string[], groupIndex: number, attrName: string) => {
  // 显示属性值输入对话框
  currentAction.value = 'add'
  currentKey.value = attrName
  attrValue.value = ''
  dialogVisible.value = true
  console.log('对话框打开')

  // 延迟聚焦到输入框
  nextTick(() => {
    if (attrValueInput.value) {
      ;(attrValueInput.value as any).focus()
    }
  })
}

// 处理商品属性变化事件
const handleCheckboxChange = (
  checked: boolean,
  selectedAttrs: string[],
  groupIndex: number,
  attrName: string,
) => {
  // 复用现有的处理逻辑
  handleAttrChange(selectedAttrs, groupIndex, attrName)
}

// 取消属性值输入
const cancelAttributeValue = () => {
  if (currentAction.value === 'add') {
  }
  dialogVisible.value = false
}

// 确认添加或编辑属性值
const handleValueConfirm = () => {
  if (attrValue.value.trim() && currentKey.value) {
    if (currentAction.value === 'edit' && currentEditIndex.value !== -1) {
      // 编辑现有属性
      console.log('编辑属性')

      form.attrList[currentEditIndex.value].value[currentAttr.index] = attrValue.value.trim()
      ElMessage.success(`属性 ${currentKey.value} 已更新`)
    } else {
      // 检查是否已存在相同key的属性
      const existingIndex = form.attrList.findIndex((item) => item.key === currentKey.value)
      console.log('存在', form.attrList[existingIndex])

      if (existingIndex !== -1) {
        // 在已存在的属性值列表中添加一项
        form.attrList[existingIndex].value.push(attrValue.value.trim())
        ElMessage.success(`属性 ${currentKey.value} 已更新`)
      } else {
        // 添加新属性
        form.attrList.push({
          key: currentKey.value,
          value: [attrValue.value],
        })
        ElMessage.success(`属性 ${currentKey.value} 已添加`)
      }
    }

    // 更新product_attr字段为JSON字符串
    form.product_attr = JSON.stringify(form.attrList)
  } else {
    ElMessage.warning('属性值不能为空')
    return
  }

  dialogVisible.value = false
  currentKey.value = ''
  attrValue.value = ''
  currentAction.value = 'add'
  currentEditIndex.value = -1
  console.log('form.attrList:', form.attrList)
}

// 编辑属性值
const editAttrValue = (attrIndex: number, key: string, value: string, attrValueIndex: number) => {
  attrValue.value = value
  console.log('attrValueIndex', attrValueIndex)
  currentKey.value = key
  console.log(form.attrList[attrIndex].value[attrValueIndex])
  currentAction.value = 'edit'
  // 在属性列表中查找匹配的索引位置，用于后续更新操作
  // currentEditIndex.value = form.attrList.findIndex((item) => item.key === key)
  currentEditIndex.value = attrIndex
  form.attrList[currentEditIndex.value].value[attrValueIndex] = attrValue.value
  // 显示属性值输入对话框
  dialogVisible.value = true
  currentAttr.index = attrValueIndex
  currentAttr.value = inputValue.value
}

// 删除属性
const handleRemoveAttr = (index: number, attrIndex: number) => {
  const removedAttr = form.attrList[index].value[attrIndex]
  form.attrList[index].value.splice(attrIndex, 1)
  ElMessage.success(`属性 ${removedAttr} 已移除`)
  if (form.attrList[index].value.length === 0) form.attrList.splice(index, 1)

  form.product_attr = JSON.stringify(form.attrList)
}

// 获取商品详情并填充表单
const fetchProductDetail = async () => {
  if (!productId.value) {
    ElMessage.error('无效的商品ID')
    router.push('/main/product/list')
    return
  }

  loading.value = true
  try {
    // 获取当前登录的商家ID
    const userStore = useUserStore()
    const sellerId = userStore.userInfo?.id

    if (!sellerId) {
      ElMessage.error('未获取到商家ID，请重新登录')
      router.push('/')
      return
    }

    console.log(`正在获取商家(${sellerId})的商品(${productId.value})详情...`)

    // 调用商家商品详情接口
    const response = await getSellerProductDetail(sellerId, productId.value)

    if (response.code === 1 && response.data) {
      const productData = response.data
      console.log('成功获取到商品详情:', productData)

      // 填充表单数据
      form.id = productData.id
      form.name = productData.name
      form.brand_id = productData.brandId
      form.category_id = productData.categoryId
      form.out_product_id = productData.outProductId || ''
      form.pic = productData.pic || ''
      form.album_pics = productData.albumPics || ''
      form.introduce_pics = productData.introductPics || ''
      form.product_pdf = productData.pdfDocument || ''
      form.publish_status = productData.publishStatus
      form.sort = productData.sort || 0
      form.inventory = productData.inventory
      form.price = productData.price
      form.unit = productData.unit || '件'
      form.weight = productData.weight || 0
      form.detail_html = productData.detailHtml || ''
      form.brand_name = productData.brandName || ''
      form.product_category_name = productData.productCategoryName || ''
      form.productCategoryId = productData.categoryId // 填充下拉框选择值

      // 处理商品属性
      if (productData.productAttr) {
        try {
          form.product_attr = productData.productAttr
          console.log('商品属性数据:', productData.productAttr)

          // 尝试解析JSON字符串
          let attrList = []
          if (typeof productData.productAttr === 'string') {
            attrList = JSON.parse(productData.productAttr)
          } else if (Array.isArray(productData.productAttr)) {
            attrList = productData.productAttr
          }

          form.attrList = attrList

          // 设置复选框选中状态
          predefinedAttrs.forEach((group, groupIndex) => {
            const selected = []
            for (const attr of form.attrList) {
              if (group.attrs.includes(attr.key)) {
                selected.push(attr.key)
              }
            }
            selectedAttrs.value[groupIndex] = selected
          })

          console.log('属性处理完成, 共', form.attrList.length, '个属性')
        } catch (e) {
          console.error('解析商品属性出错:', e)
          form.attrList = []
        }
      }

      // 处理相册图片
      if (productData.albumPics) {
        try {
          console.log('原始相册图片数据:', productData.albumPics)
          let albumUrls = []

          // 确保albumPics是字符串类型并处理它
          if (typeof productData.albumPics === 'string') {
            // 使用正则表达式查找所有的图片URL，以常见图片扩展名结尾
            const urlRegex =
              /(https?:\/\/[^\s,'"]+\.(jpeg|jpg|png|gif|webp|JPEG|JPG|PNG|GIF|WEBP)(?:\?[^\s,'"]*)?)/g
            const matches = productData.albumPics.match(urlRegex)

            if (matches && matches.length > 0) {
              albumUrls = matches
              console.log('通过正则表达式提取的URL:', albumUrls)
            } else {
              // 回退方法：尝试基于一些启发式规则分割
              const albumPicsStr = productData.albumPics
              let currentUrl = ''
              let result = []

              // 遍历字符串查找合法URL
              for (let i = 0; i < albumPicsStr.length; i++) {
                currentUrl += albumPicsStr[i]

                // 当找到图片扩展名后的逗号或字符串结束时，认为一个URL结束
                if (
                  (currentUrl.match(/\.(jpeg|jpg|png|gif|webp)$/i) &&
                    (i + 1 >= albumPicsStr.length || albumPicsStr[i + 1] === ',')) ||
                  i + 1 >= albumPicsStr.length
                ) {
                  if (currentUrl.trim() && currentUrl.startsWith('http')) {
                    result.push(currentUrl.trim())
                  }
                  // 跳过逗号
                  if (i + 1 < albumPicsStr.length && albumPicsStr[i + 1] === ',') {
                    i++
                  }
                  currentUrl = ''
                }
              }

              if (result.length > 0) {
                albumUrls = result
                console.log('通过扩展名分割提取的URL:', albumUrls)
              }
            }
          } else if (Array.isArray(productData.albumPics)) {
            // 如果是数组，直接使用
            albumUrls = productData.albumPics
          }

          console.log('处理后的相册URL列表:', albumUrls)

          // 设置相册预览列表
          galleryList.value = albumUrls.map((url: string, index: number) => ({
            name: `相册图${index + 1}`,
            url: url,
            uid: -(index + 1),
          }))

          console.log('相册图片处理完成，共 ' + galleryList.value.length + ' 张')
        } catch (error) {
          console.error('处理相册图片出错:', error)
          galleryList.value = []
        }
      } else {
        console.log('商品没有相册图片')
        galleryList.value = []
      }
      // 处理图文介绍图片
if (productData.introductPics) {
        try {
          let introductUrls = [];

          // 确保introductPics是字符串类型并处理它
          if (typeof productData.introductPics === 'string') {
            // 使用正则表达式查找所有的图片URL
            const urlRegex = /(https?:\/\/[^\s,'"]+\.(jpeg|jpg|png|gif|webp)(?:\?[^\s,'"]*)?)/g;
            const matches = productData.introductPics.match(urlRegex);

            if (matches && matches.length > 0) {
              introductUrls = matches;
            }
          } else if (Array.isArray(productData.introductPics)) {
            // 如果是数组，直接使用
            introductUrls = productData.introductPics;
          }

          // 设置图文介绍预览列表
          graphicIntroductList.value = introductUrls.map((url: string, index: number) => ({
            name: `图文介绍图${index + 1}`,
            url: url,
            uid: -(index + 1000), // 使用不同的uid范围避免冲突
          }));



        } catch (error) {
          console.error('处理图文介绍图片出错:', error);
          graphicIntroductList.value = [];
          graphicIntroductFiles.value = [];
        }
      } else {
        graphicIntroductList.value = [];
        graphicIntroductFiles.value = [];
      }

      // 处理PDF文档
      if (productData.pdfDocument) {
        form.product_pdf = productData.pdfDocument
        pdfFile.value = {
          name: `${form.name}介绍文档.pdf`,
          url: productData.pdfDocument,
          type: 'application/pdf',
        } as unknown as File
      } else {
        pdfFile.value = null
        form.product_pdf = ''
      }

      console.log('商品详情加载完成')
      ElMessage.success('商品详情加载成功')
    } else {
      ElMessage.error(response.msg || '获取商品详情失败')
      console.error('获取商品详情失败:', response)
      router.push('/main/product/list')
    }
  } catch (error) {
    console.error('获取商品详情出错:', error)
    ElMessage.error('获取商品详情失败，请稍后重试')
    router.push('/main/product/list')
  } finally {
    loading.value = false
  }
}

// 加载分类数据
const fetchCategories = async () => {
  try {
    const res = await getCategoryTree()
    if (res.code === 1 && res.data) {
      categoryOptions.value = res.data
      let flatCategories: { id: number; fullName: string }[] = []

      // 处理分类数据，找出所有三级分类，并构建完整路径名称
      const processCategories = (categories: any[], parentName = '') => {
        for (const category of categories) {
          const currentName = parentName ? `${parentName} > ${category.name}` : category.name

          if (category.level === 2 || !category.children || category.children.length === 0) {
            // 这是一个叶子节点，添加到结果中
            flatCategories.push({
              id: category.id,
              fullName: currentName,
            })
          }

          if (category.children && category.children.length > 0) {
            processCategories(category.children, currentName)
          }
        }
      }

      processCategories(res.data)
      thirdLevelCategories.value = flatCategories
    }
  } catch (error) {
    console.error('获取分类数据失败:', error)
    ElMessage.error('获取分类数据失败，请稍后重试')
  }
}

// 处理主图上传
const handleMainImageChange = (file: UploadUserFile) => {
  if (file.raw) {
    // 验证文件类型
    const isJPG = file.raw.type === 'image/jpeg'
    const isPNG = file.raw.type === 'image/png'
    if (!isJPG && !isPNG) {
      ElMessage.error('商品主图只能是JPG或PNG格式!')
      return
    }

    // 验证文件大小
    const isLt2M = file.raw.size / 1024 / 1024 < 2
    if (!isLt2M) {
      ElMessage.error('商品主图大小不能超过2MB!')
      return
    }

    // 保存文件
    mainPicFile.value = file.raw

    // 创建预览URL
    const reader = new FileReader()
    reader.readAsDataURL(file.raw)
    reader.onload = () => {
      form.pic = reader.result as string
    }
  }
}

// 处理相册图片上传
const handleGalleryChange = (file: UploadUserFile, fileList: UploadUserFile[]) => {
  console.log('相册图片列表：',fileList);
  
  // 限制最多5张图片
  if (fileList.length > 5) {
    fileList.pop()
    ElMessage.warning('最多只能上传5张相册图片')
    return
  }

  if (file.raw) {
    // 验证文件类型
    const isJPG = file.raw.type === 'image/jpeg'
    const isPNG = file.raw.type === 'image/png'
    if (!isJPG && !isPNG) {
      ElMessage.error('相册图片只能是JPG或PNG格式!')
      const index = fileList.indexOf(file)
      if (index !== -1) {
        fileList.splice(index, 1)
      }
      return
    }

    // 验证文件大小
    const isLt2M = file.raw.size / 1024 / 1024 < 2
    if (!isLt2M) {
      ElMessage.error('相册图片大小不能超过2MB!')
      const index = fileList.indexOf(file)
      if (index !== -1) {
        fileList.splice(index, 1)
      }
      return
    }

    // 更新预览
    galleryList.value = fileList

    // 保存新上传的图片
    albumPicFiles.value.push(file.raw)

    // 如果文件已有URL，使用它；否则创建新的URL
    if (!file.url && file.raw) {
      const reader = new FileReader()
      reader.readAsDataURL(file.raw)
      reader.onload = () => {
        file.url = reader.result as string

        // 更新file列表
        galleryList.value = [...fileList]

        // 更新album_pics字段，保存相册图片URL
        updateAlbumPics()
      }
    }
  }
}
const updateIntroductionPics = () => {
  // 从graphicIntroductList中提取所有URL
  const urls = graphicIntroductList.value.map((item) => item.url).filter(Boolean)

  // 转换为逗号分隔的字符串，与后端格式匹配
  form.introduce_pics = urls.join(',')
  console.log('已更新图文介绍图片列表:', form.introduce_pics)
}
// 处理图文介绍图片上传
const handleIntroductionChange = (file: UploadUserFile, fileList: UploadUserFile[]) => {
  console.log('当前文件列表：',fileList);
  
  // 检查文件类型和大小
  const isImage = file.raw?.type === 'image/jpeg' || file.raw?.type === 'image/png'
  if (!isImage) {
    ElMessage.error('商品图片只能是JPG或PNG格式!')
    return false
  }

  // 检查单个文件大小
  const isLt5M = file.size! / 1024 / 1024 < 5
  if (!isLt5M) {
    ElMessage.error('商品图片大小不能超过5MB!')
    return false
  }

  // 限制图片数量
  if (fileList.length > 5) {
    ElMessage.warning('最多只能上传5张商品展示图')
    fileList.pop() // 移除最后一个
    return false
  }

  // 检查文件对象
  if (!file.raw) {
    ElMessage.error('无法获取文件对象!')
    return false
  }

  // 更新文件列表
  graphicIntroductList.value = fileList
  // 更新原始文件列表
  graphicIntroductFiles.value.push(file.raw)

  // 如果文件没有URL，创建预览URL
  if (!file.url && file.raw) {
    const reader = new FileReader()
    reader.onload = () => {
      file.url = reader.result as string
      graphicIntroductList.value = [...fileList]
      updateIntroductionPics() // 更新图文介绍字段
    }
    reader.readAsDataURL(file.raw)
  } else {
    updateIntroductionPics() // 更新图文介绍字段
  }
  console.log('当前图文介绍文件列表:', graphicIntroductFiles.value)
  return false
}
// 新增beforePdfUpload验证方法
const beforePdfUpload = (file: File) => {
  const isPdf = file.type === 'application/pdf'
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isPdf) {
    ElMessage.error('只能上传PDF格式的文件!')
    return false
  }

  if (!isLt10M) {
    ElMessage.error('PDF文件大小不能超过10MB!')
    return false
  }
  return true
}

// 处理PDF文件变化
const handlePdfChange = (file: UploadUserFile) => {
  if (!file.raw) {
    ElMessage.error('无法获取文件对象!')
    return false
  }

  // 使用beforePdfUpload进行验证
  if (!beforePdfUpload(file.raw)) {
    return false
  }

  // 保存原始文件对象
  pdfFile.value = file.raw

  // 创建预览URL
  if (!file.url && file.raw) {
    const reader = new FileReader()
    reader.onload = () => {
      form.product_pdf = reader.result as string
    }
    reader.readAsDataURL(file.raw)
  } else {
    form.product_pdf = file.url
  }

  console.log('PDF文件已保存:', file.name, file.size, file.type)
  return false
}

// 处理PDF文件移除
const handlePdfRemove = () => {
  if (pdfFile.value) {
    console.log('移除PDF文件:', pdfFile.value.name)
  }
  pdfFile.value = null
  form.product_pdf = ''
}
// 处理图文介绍图片移除
const handleIntroductionRemove = (file: UploadUserFile) => {
  // 1. 从预览列表中移除图片
  const previewIndex = graphicIntroductList.value.findIndex((item) => item.uid === file.uid);
  if (previewIndex !== -1) {
    graphicIntroductList.value.splice(previewIndex, 1);
  }

  // 2. 从原始文件列表中移除对应的文件
  if (file.raw) {
    const fileIndex = graphicIntroductFiles.value.findIndex((f) => 
      f.name === file.name && f.size === file.size
    );
    if (fileIndex !== -1) {
      graphicIntroductFiles.value.splice(fileIndex, 1);
    }
  }

  // 3. 更新表单中的图文介绍图片字段
  updateIntroductionPics();

  console.log('当前图文介绍文件列表:', graphicIntroductFiles.value);
}
// 更新album_pics字段
const updateAlbumPics = () => {
  // 从galleryList中提取所有URL，包括已有的和新上传的
  const urls = galleryList.value.map((item) => item.url).filter(Boolean) // 过滤掉undefined和null

  // 转换为逗号分隔的字符串，与后端格式匹配
  form.album_pics = urls.join(',')
  console.log('已更新相册图片列表:', form.album_pics)
}
// 处理相册图片删除
const handleGalleryRemove = (file: UploadUserFile) => {
  const index = galleryList.value.indexOf(file)
  if (index !== -1) {
    // 更新预览图片
    galleryList.value.splice(index, 1)
    // 如果是新上传的文件，从原始文件列表中移除
    if (file.raw) {
      const rawIndex = albumPicFiles.value.findIndex((f) => f === file.raw)
      if (rawIndex !== -1) {
        albumPicFiles.value.splice(rawIndex, 1)
      }
    }

    // 更新album_pics字段
    updateAlbumPics()
  }
  console.log('当前相册文件列表:', albumPicFiles.value)

}

// 更新商品接口
const updateProduct = async (productData: any): Promise<any> => {
  try {
    // 获取当前登录商家ID
    const userStore = useUserStore()
    const sellerId = userStore.userInfo?.id
    console.log('获取到的商家ID:', sellerId)

    if (!sellerId) {
      ElMessage.error('未获取到商家ID，请重新登录')
      throw new Error('未获取到商家ID，请重新登录')
    }

    // 转换字段名称以匹配后端PmsProduct实体类
    // 注意：确保不包含任何图片相关字段
    const product = {
      id: productData.id,
      productSnapshotId: productData.product_snapshot_id || null,
      brandId: productData.brand_id,
      categoryId: productData.category_id,
      outProductId: productData.out_product_id || '',
      name: productData.name,
      publishStatus: productData.publish_status,
      sort: productData.sort,
      inventory: productData.inventory,
      price: productData.price,
      unit: productData.unit,
      weight: productData.weight,
      detailHtml: productData.detail_html,
      brandName: productData.brand_name,
      productCategoryName: productData.product_category_name,
      productAttr: productData.product_attr,
      // 严格不包含pic和albumPics字段
    }

    console.log('准备调用updateSellerProduct API...')
    console.log('商品基本数据:', {
      id: product.id,
      name: product.name,
      brandId: product.brandId,
      categoryId: product.categoryId,
      price: product.price,
    })
    console.log(
      '图片信息 (单独传递) - 主图:',
      mainPicFile.value ? '已更新' : '无更新',
      '相册图片:',
      albumPicFiles.value.length,
      '张',
    )
    console.log('上传的图片', graphicIntroductFiles.value, pdfFile.value)

    // 调用API模块中的方法更新商品，文件单独传递
    const result = await updateSellerProduct(
      sellerId,
      productId.value,
      product,
      mainPicFile.value,
      albumPicFiles.value,
      graphicIntroductFiles.value,
      pdfFile.value,
    )

    console.log('updateSellerProduct API返回结果:', result)
    return result
  } catch (error) {
    console.error('更新商品失败:', error)
    throw error
  }
}

// 取消
const handleCancel = () => {
  ElMessage.warning('正在返回商品列表')
  router.push('/main/product/list')
}

// 处理表单提交
const handleSubmit = async () => {
  // 验证表单
  if (!form.name) {
    ElMessage.error('请输入商品名称')
    return
  }

  if (!form.category_id) {
    ElMessage.error('请选择商品分类')
    return
  }

  if (!form.price) {
    ElMessage.error('请输入商品价格')
    return
  }
  if (!form.inventory) {
    ElMessage.error('请输入商品库存')
    return
  }

  try {
    submitLoading.value = true

    // 准备分类名称和品牌名称
    if (form.category_id) {
      const category = thirdLevelCategories.value.find((item) => item.id === form.category_id)
      if (category) {
        form.product_category_name = category.fullName
      }
    }

    if (form.brand_id) {
      const brand = brandOptions.find((item) => item.id === form.brand_id)
      if (brand) {
        form.brand_name = brand.name
      }
    }

    console.log('更新的数据：', form)

    const result = await updateProduct(form)

    if (result.code === 1) {
      ElMessage.success('商品更新成功')
      // 延迟跳转，确保用户看到成功消息
      setTimeout(() => {
        router.push('/main/product/list')
      }, 1500)
    } else {
      ElMessage.error(result.msg || '商品更新失败')
    }
  } catch (error: any) {
    console.error('更新商品出错:', error)

    let errorMessage = '商品更新失败，请稍后重试'

    // 处理常见HTTP错误
    if (error.response) {
      const status = error.response.status
      switch (status) {
        case 400:
          errorMessage = '请求参数错误，请检查输入'
          break
        case 401:
          errorMessage = '登录已过期，请重新登录'
          break
        case 403:
          errorMessage = '您没有权限执行此操作'
          break
        case 404:
          errorMessage = '商品不存在或已被删除'
          break
        case 500:
          errorMessage = '服务器错误，请联系管理员'
          break
      }

      // 如果有详细错误响应，显示它
      if (error.response.data && error.response.data.message) {
        errorMessage += `\n详情: ${error.response.data.message}`
      }
    }

    // 对网络错误的处理
    if (error.name === 'NetworkError' || error.message.includes('network')) {
      errorMessage = '网络连接错误，请检查您的网络连接'
    }

    // 对超时错误的处理
    if (error.message.includes('timeout')) {
      errorMessage = '请求超时，请稍后重试'
    }

    ElMessage.error(errorMessage)

    // 针对401错误，自动跳转到登录页
    if (error.response && error.response.status === 401) {
      setTimeout(() => {
        router.push('/')
      }, 1500)
    }
  } finally {
    submitLoading.value = false
  }
}

// 初始化加载分类数据和商品详情
onMounted(async () => {
  await fetchCategories()
  await fetchProductDetail()
})
</script>

<style scoped lang="scss">
.page-container {
  padding: 16px;

  .page-card {
    :deep(.el-card__header) {
      padding: 16px 20px;
    }

    :deep(.el-card__body) {
      padding: 20px;
    }
  }

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .page-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
    }
  }

  .form-section {
    margin-bottom: 32px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 24px;

    &:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }

    .section-title {
      font-size: 16px;
      margin-top: 0;
      margin-bottom: 24px;
      padding-left: 10px;
      border-left: 3px solid #409eff;
    }
  }

  .field-tip {
    font-size: 12px;
    color: #999;
    margin-top: 5px;
    display: flex;
    align-items: center;

    .el-icon {
      margin-right: 5px;
      color: #909399;
    }
  }

  .main-image-uploader {
    :deep(.el-upload) {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      width: 200px;
      height: 200px;
      display: flex;
      justify-content: center;
      align-items: center;
      transition: all 0.3s;

      &:hover {
        border-color: #409eff;
      }
    }

    .main-image {
      width: 200px;
      height: 200px;
      display: block;
      object-fit: contain;
    }

    .upload-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      color: #909399;

      .el-icon {
        font-size: 28px;
        margin-bottom: 8px;
      }

      .upload-text {
        font-size: 14px;
      }
    }
  }

  /* 商品属性部分样式 */
  .attr-container {
    display: flex;
    width: 100%;
    gap: 20px;
    height: 400px;

    .attr-child {
      flex: 1;
      box-sizing: border-box;
    }
    .attr-selection-panel {
      flex: 0 0 40%;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      overflow: hidden;
      height: 100%;

      :deep(.el-tabs) {
        height: 100%;

        .el-tabs__content {
          padding: 15px;
          height: calc(100% - 40px);
          overflow-y: auto;
        }
      }

      .attr-group {
        margin-bottom: 10px;

        .attr-checkbox {
          margin-right: 10px;
          margin-bottom: 10px;
        }
      }
    }

    .attr-preview-panel {
      flex: 0 0 56%;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      height: 100%;

      .panel-header {
        padding: 10px 15px;
        background-color: #f5f7fa;
        border-bottom: 1px solid #ebeef5;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .panel-title {
          font-weight: 600;
          font-size: 14px;
        }

        .attr-count {
          font-size: 12px;
          color: #909399;

          b {
            color: #409eff;
          }
        }
      }

      .attr-tags-container {
        padding: 15px;
        flex: 1;
        overflow-y: auto;

        .no-attrs {
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;

          .empty-tip {
            margin: 0;
            margin-top: 10px;
            font-size: 14px;
            color: #909399;
          }

          .empty-tip-sub {
            margin: 5px 0 0 0;
            font-size: 12px;
            color: #c0c4cc;
          }
        }

        .attr-list {
          display: flex;
          // flex-direction: column;
          flex-wrap: wrap;
          gap: 5%;
          width: 100%;
          .attr-item {
            width: 30%;
            box-sizing: border-box;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border-left: 3px solid #409eff;

            .attr-button {
              width: 100% !important;
              flex-grow: 1;
            }
            .attr-item-content {
              display: flex;
              flex-direction: column;
              gap: 5px;

              .attr-item-key {
                font-weight: 600;
                font-size: 14px;
                color: #303133;
              }

              .attr-item-value {
                font-size: 13px;
                color: #606266;
              }
            }

            .attr-item-actions {
              display: flex;
              gap: 5px;
            }
          }
        }
      }
    }
  }

  .attr-tips {
    margin-top: 10px;
  }

  .upload-tip {
    color: #999;
    font-size: 12px;
    margin-top: 5px;
  }

  .gallery-uploader {
    :deep(.el-upload--picture-card) {
      width: 148px;
      height: 148px;
      line-height: 148px;
    }
  }
}
</style>
