<template>
  <div class="help-container">
    <el-row :gutter="20">
      <el-col :span="4">
        <el-menu :default-active="activeMenu" class="help-menu" @select="handleSelect">
          <el-menu-item index="overview">
            <el-icon><Document /></el-icon>
            <span>概述</span>
          </el-menu-item>
          <el-sub-menu index="getting-started">
            <template #title>
              <el-icon><Guide /></el-icon>
              <span>快速入门</span>
            </template>
            <el-menu-item index="account-setup">账号设置</el-menu-item>
            <el-menu-item index="basic-operations">基本操作</el-menu-item>
          </el-sub-menu>
          <el-sub-menu index="products">
            <template #title>
              <el-icon><Goods /></el-icon>
              <span>商品管理</span>
            </template>
            <el-menu-item index="add-product">添加商品</el-menu-item>
            <el-menu-item index="edit-product">编辑商品</el-menu-item>
            <el-menu-item index="product-category">商品分类</el-menu-item>
          </el-sub-menu>
          <el-sub-menu index="orders">
            <template #title>
              <el-icon><List /></el-icon>
              <span>订单管理</span>
            </template>
            <el-menu-item index="order-process">订单处理</el-menu-item>
            <el-menu-item index="shipping">发货管理</el-menu-item>
            <el-menu-item index="returns">退货处理</el-menu-item>
          </el-sub-menu>
          <el-sub-menu index="marketing">
            <template #title>
              <el-icon><Promotion /></el-icon>
              <span>营销管理</span>
            </template>
            <el-menu-item index="promotions">促销活动</el-menu-item>
            <el-menu-item index="coupons">优惠券</el-menu-item>
          </el-sub-menu>
          <el-menu-item index="faq">
            <el-icon><QuestionFilled /></el-icon>
            <span>常见问题</span>
          </el-menu-item>
          <el-menu-item index="contact">
            <el-icon><Service /></el-icon>
            <span>联系客服</span>
          </el-menu-item>
        </el-menu>
      </el-col>
      <el-col :span="20">
        <router-view v-slot="{ Component }">
          <transition name="fade" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import {
  Document,
  Guide,
  Goods,
  List,
  Promotion,
  QuestionFilled,
  Service,
} from '@element-plus/icons-vue'

const router = useRouter()
const activeMenu = ref('overview')

const handleSelect = (index: string) => {
  // 根据选中的菜单项导航到相应的内容页面
  router.push({ name: 'help-docs', query: { section: index } })
}
</script>

<style scoped>
.help-container {
  padding: 20px;
  height: 100%;
  background-color: #fff;
}

.help-menu {
  height: calc(100vh - 120px);
  border-right: 1px solid #e6e6e6;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
