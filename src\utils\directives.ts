import type { Directive } from 'vue'
import { refreshTranslation } from './language'

/**
 * 异步翻译指令
 * 用法：v-translate-when="dataLoaded"
 * 
 * 当绑定的值变为true时，自动触发区域翻译
 * 
 * 选项：
 * v-translate-when:200="dataLoaded" - 设置延迟时间为200ms
 * v-translate-when.once="dataLoaded" - 只触发一次翻译
 */
export const translateWhen: Directive = {
  mounted(el, binding) {
    el._translate_triggered = false
    el._translate_watcher = null
    
    // 获取设置的延迟，默认200ms
    const delay = parseInt(binding.arg || '200', 10)
    
    // 是否只触发一次
    const once = binding.modifiers.once === true
    
    // 创建监视器函数
    const watcher = () => {
      // 如果值为true且(允许多次触发或尚未触发过)
      if (binding.value === true && (!once || !el._translate_triggered)) {
        el._translate_triggered = true
        
        // 刷新当前元素的翻译
        refreshTranslation(`#${el.id || 'app'}`, delay)
      }
    }
    
    // 首次检查
    watcher()
    
    // 设置值变化的监听
    if (typeof binding.value === 'boolean') {
      el._translate_value = binding.value
      
      // 创建MutationObserver来监听值变化
      const observer = new MutationObserver(() => {
        if (binding.value !== el._translate_value) {
          el._translate_value = binding.value
          watcher()
        }
      })
      
      // 启动观察
      observer.observe(document.body, { 
        attributes: true,
        childList: true,
        subtree: true 
      })
      
      // 保存观察器以便清理
      el._translate_watcher = observer
    }
  },
  
  updated(el, binding) {
    // 保存当前值
    el._translate_value = binding.value
    
    // 是否只触发一次
    const once = binding.modifiers.once === true
    
    // 检查值并在满足条件时触发翻译
    if (binding.value === true && (!once || !el._translate_triggered)) {
      el._translate_triggered = true
      
      // 获取设置的延迟，默认200ms
      const delay = parseInt(binding.arg || '200', 10)
      
      // 刷新当前元素的翻译
      refreshTranslation(`#${el.id || 'app'}`, delay)
    }
  },
  
  beforeUnmount(el) {
    // 清理观察器
    if (el._translate_watcher) {
      el._translate_watcher.disconnect()
      el._translate_watcher = null
    }
  }
}

// 导出所有自定义指令
export default {
  translateWhen
}

// 扩展HTMLElement以添加我们需要的属性
declare global {
  interface HTMLElement {
    _translate_triggered?: boolean;
    _translate_watcher?: MutationObserver | null;
    _translate_value?: any;
  }
} 