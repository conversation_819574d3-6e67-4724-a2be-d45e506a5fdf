@use './variables.scss' as v;

/* 全局SCSS样式 */

/* 重置默认样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

html,
body {
  font-family: v.$font-family;
  font-size: v.$font-size-base;
  color: v.$text-primary;
  height: 100%;
  width: 100%;
  overflow-x: hidden;
  background-color: v.$background-color-base;
}

#app {
  height: 100%;
  width: 100%;
}

/* Element Plus 主题变量覆盖 */
:root {
  --el-color-primary: #{v.$primary-color};
  --el-color-success: #{v.$success-color};
  --el-color-warning: #{v.$warning-color};
  --el-color-danger: #{v.$danger-color};
  --el-color-info: #{v.$info-color};
  --el-font-size-base: #{v.$font-size-base};
  --el-font-size-small: #{v.$font-size-small};

  // 添加阴影和圆角变量
  --el-border-radius-base: 4px;
  --el-border-radius-small: 2px;
  --el-border-radius-round: 20px;
  --el-border-radius-circle: 50%;

  --el-box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
  --el-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.12);
  --el-box-shadow-dark: 0 2px 16px 0 rgba(0, 0, 0, 0.16);

  // 添加过渡效果变量
  --el-transition-all: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --el-transition-hover: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 增强的卡片样式 */
.el-card {
  border: none !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08) !important;
  transition:
    box-shadow 0.3s ease,
    transform 0.3s ease;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12) !important;
  }

  .el-card__header {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 20px;
  }
}

/* 表格样式增强 */
.el-table {
  border-radius: 8px;
  overflow: hidden;

  th.el-table__cell {
    background-color: #fafafa !important;
    font-weight: 500;
  }

  .el-table__row {
    transition: background-color 0.2s ease;

    &:hover > td.el-table__cell {
      background-color: #f5f7fa !important;
    }
  }
}

/* 按钮样式增强 */
.el-button {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

  &--primary {
    &:not(.is-disabled) {
      &:hover,
      &:focus {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(var(--el-color-primary-rgb), 0.3);
      }

      &:active {
        transform: translateY(0);
        box-shadow: 0 2px 6px rgba(var(--el-color-primary-rgb), 0.2);
      }
    }
  }
}

/* 表单控件增强 */
.el-input__wrapper,
.el-textarea__wrapper {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  border-radius: 6px !important;

  &.is-focus {
    box-shadow: 0 0 0 1px var(--el-color-primary-light-7) !important;
    transform: translateY(-1px);
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(144, 147, 153, 0.3);
  border-radius: 3px;

  &:hover {
    background-color: rgba(144, 147, 153, 0.5);
  }
}

::-webkit-scrollbar-track {
  background-color: transparent;
}

/* 常用工具类 */
.flex {
  display: flex;
}
.flex-center {
  @include v.flex-center;
}
.flex-between {
  @include v.flex-between;
}
.flex-column {
  display: flex;
  flex-direction: column;
}

.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.truncate {
  @include v.text-ellipsis;
}

.mb-10 {
  margin-bottom: v.$spacing-small;
}
.mb-20 {
  margin-bottom: v.$spacing-large;
}
.mt-10 {
  margin-top: v.$spacing-small;
}
.mt-20 {
  margin-top: v.$spacing-large;
}
.ml-10 {
  margin-left: v.$spacing-small;
}
.mr-10 {
  margin-right: v.$spacing-small;
}
.p-10 {
  padding: v.$spacing-small;
}
.p-20 {
  padding: v.$spacing-large;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease forwards;
}

.fade-enter-active,
.fade-leave-active {
  transition: v.$transition-fade;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: v.$transition-slide;
}

.slide-up-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.slide-up-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

/* 响应式布局 */
@include v.respond-to(sm) {
  html,
  body {
    font-size: v.$font-size-small;
  }
}

/* 按钮样式扩展 */
.btn-hover-effect {
  transition: v.$transition-all;
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
  }
}
