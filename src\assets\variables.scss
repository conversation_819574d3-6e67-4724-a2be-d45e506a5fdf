// 颜色系统 - 更新为更现代的配色
$primary-color: #4c84ff;
$success-color: #52c41a;
$warning-color: #faad14;
$danger-color: #ff4d4f;
$info-color: #8c8c8c;

$text-primary: #262626;
$text-regular: #595959;
$text-secondary: #8c8c8c;
$text-placeholder: #bfbfbf;

$border-color-base: #d9d9d9;
$border-color-light: #e8e8e8;
$border-color-lighter: #f0f0f0;
$border-color-extra-light: #f5f5f5;

$background-color-base: #f5f7fa;
$background-color-white: #ffffff;

// 字体系统
$font-family:
  'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial,
  sans-serif;
$font-size-extra-large: 20px;
$font-size-large: 18px;
$font-size-medium: 16px;
$font-size-base: 14px;
$font-size-small: 13px;
$font-size-extra-small: 12px;

$font-weight-primary: 500;
$font-weight-secondary: 400;

$line-height-primary: 24px;
$line-height-secondary: 20px;

// 边框圆角
$border-radius-base: 4px;
$border-radius-small: 2px;
$border-radius-round: 20px;
$border-radius-circle: 50%;

// 阴影
$box-shadow-base:
  0 2px 4px rgba(0, 0, 0, 0.12),
  0 0 6px rgba(0, 0, 0, 0.04);
$box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
$box-shadow-dark: 0 2px 12px 0 rgba(0, 0, 0, 0.2);

// 间距
$spacing-extra-small: 5px;
$spacing-small: 10px;
$spacing-medium: 15px;
$spacing-large: 20px;
$spacing-extra-large: 30px;

// 断点
$breakpoint-sm: 768px;
$breakpoint-md: 992px;
$breakpoint-lg: 1200px;
$breakpoint-xl: 1920px;

// 过渡动画
$transition-all: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
$transition-fade: opacity 0.3s cubic-bezier(0.23, 1, 0.32, 1);
$transition-slide: transform 0.3s cubic-bezier(0.23, 1, 0.32, 1);

// 混合器
@mixin flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

@mixin flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

@mixin text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin scrollbar {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(144, 147, 153, 0.3);
    border-radius: 3px;

    &:hover {
      background-color: rgba(144, 147, 153, 0.5);
    }
  }
}

// 响应式断点
@mixin respond-to($breakpoint) {
  @if $breakpoint == sm {
    @media only screen and (max-width: $breakpoint-sm) {
      @content;
    }
  } @else if $breakpoint == md {
    @media only screen and (max-width: $breakpoint-md) {
      @content;
    }
  } @else if $breakpoint == lg {
    @media only screen and (max-width: $breakpoint-lg) {
      @content;
    }
  } @else if $breakpoint == xl {
    @media only screen and (min-width: $breakpoint-lg + 1) {
      @content;
    }
  }
}
