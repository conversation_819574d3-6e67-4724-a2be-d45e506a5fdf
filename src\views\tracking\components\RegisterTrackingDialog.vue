<template>
  <el-dialog
    v-model="visible"
    title="注册物流单号"
    width="600px"
    :before-close="handleClose"
    class="register-tracking-dialog"
  >
    <el-form
      ref="formRef"
      :model="registerForm"
      :rules="rules"
      label-width="120px"
      class="register-form"
    >
      <el-form-item label="物流单号" prop="trackingNumber" required>
        <el-input
          v-model="registerForm.trackingNumber"
          placeholder="请输入物流单号"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="运输商" prop="carrierCode" required>
        <el-select
          v-model="registerForm.carrierCode"
          placeholder="请选择运输商"
          style="width: 100%"
          filterable
        >
          <el-option
            v-for="carrier in carriers"
            :key="carrier.code"
            :label="carrier.name"
            :value="carrier.code"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="关联订单ID" prop="orderId">
        <el-input-number
          v-model="registerForm.orderId"
          :min="1"
          style="width: 100%"
          placeholder="请输入订单ID（可选）"
        />
      </el-form-item>

      <el-form-item label="发货国家" prop="originCountry">
        <el-select
          v-model="registerForm.originCountry"
          placeholder="请选择发货国家"
          style="width: 100%"
          filterable
        >
          <el-option label="中国" value="CN" />
          <el-option label="美国" value="US" />
          <el-option label="英国" value="GB" />
          <el-option label="德国" value="DE" />
          <el-option label="法国" value="FR" />
          <el-option label="日本" value="JP" />
          <el-option label="韩国" value="KR" />
          <el-option label="澳大利亚" value="AU" />
          <el-option label="加拿大" value="CA" />
          <el-option label="新加坡" value="SG" />
        </el-select>
      </el-form-item>

      <el-form-item label="目的地国家" prop="destinationCountry">
        <el-select
          v-model="registerForm.destinationCountry"
          placeholder="请选择目的地国家"
          style="width: 100%"
          filterable
        >
          <el-option label="中国" value="CN" />
          <el-option label="美国" value="US" />
          <el-option label="英国" value="GB" />
          <el-option label="德国" value="DE" />
          <el-option label="法国" value="FR" />
          <el-option label="日本" value="JP" />
          <el-option label="韩国" value="KR" />
          <el-option label="澳大利亚" value="AU" />
          <el-option label="加拿大" value="CA" />
          <el-option label="新加坡" value="SG" />
        </el-select>
      </el-form-item>

      <el-form-item label="自定义标签" prop="tag">
        <el-input
          v-model="registerForm.tag"
          placeholder="请输入自定义标签（可选）"
          maxlength="50"
        />
      </el-form-item>

      <el-form-item label="翻译语言" prop="lang">
        <el-select
          v-model="registerForm.lang"
          placeholder="请选择翻译语言"
          style="width: 100%"
        >
          <el-option label="中文" value="zh-CN" />
          <el-option label="英文" value="en" />
          <el-option label="日文" value="ja" />
          <el-option label="韩文" value="ko" />
          <el-option label="法文" value="fr" />
          <el-option label="德文" value="de" />
          <el-option label="西班牙文" value="es" />
        </el-select>
      </el-form-item>

      <el-form-item label="附加参数" prop="param">
        <el-input
          v-model="registerForm.param"
          placeholder="请输入附加跟踪参数（可选）"
          maxlength="100"
        />
      </el-form-item>

      <el-form-item label="备注信息" prop="remark">
        <el-input
          v-model="registerForm.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息（可选）"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
        >
          注册
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick, onMounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { registerTracking, getCarriers } from '@/api/tracking'
import { validateTrackingNumber } from '@/api/tracking'
import type { RegisterTrackingDTO, Carrier } from '@/types/tracking'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'refresh'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = ref(false)
const submitting = ref(false)
const formRef = ref<FormInstance>()
const carriers = ref<Carrier[]>([])

// 注册表单
const registerForm = reactive<RegisterTrackingDTO>({
  trackingNumber: '',
  carrierCode: '',
  orderId: undefined,
  originCountry: '',
  destinationCountry: '',
  tag: '',
  remark: '',
  lang: 'zh-CN',
  param: ''
})

// 表单验证规则
const rules: FormRules = {
  trackingNumber: [
    { required: true, message: '请输入物流单号', trigger: 'blur' },
    { min: 6, max: 50, message: '物流单号长度应在6-50个字符之间', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value && registerForm.carrierCode) {
          if (!validateTrackingNumber(value, registerForm.carrierCode)) {
            callback(new Error('物流单号格式不正确'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  carrierCode: [
    { required: true, message: '请选择运输商', trigger: 'change' }
  ]
}

// 监听显示状态
watch(
  () => props.modelValue,
  (newVal) => {
    visible.value = newVal
    if (newVal) {
      resetForm()
    }
  }
)

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 获取运输商列表
const fetchCarriers = async () => {
  try {
    const response = await getCarriers()
    if (response.code === 1) {
      carriers.value = response.data || []
    }
  } catch (error) {
    console.error('获取运输商列表失败:', error)
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(registerForm, {
    trackingNumber: '',
    carrierCode: '',
    orderId: undefined,
    originCountry: '',
    destinationCountry: '',
    tag: '',
    remark: '',
    lang: 'zh-CN',
    param: ''
  })
  
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 提交注册
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    submitting.value = true

    // 构建提交数据，过滤空值
    const submitData: RegisterTrackingDTO = {
      trackingNumber: registerForm.trackingNumber,
      carrierCode: registerForm.carrierCode
    }

    if (registerForm.orderId) submitData.orderId = registerForm.orderId
    if (registerForm.originCountry) submitData.originCountry = registerForm.originCountry
    if (registerForm.destinationCountry) submitData.destinationCountry = registerForm.destinationCountry
    if (registerForm.tag) submitData.tag = registerForm.tag
    if (registerForm.remark) submitData.remark = registerForm.remark
    if (registerForm.lang) submitData.lang = registerForm.lang
    if (registerForm.param) submitData.param = registerForm.param

    const response = await registerTracking(submitData)
    
    if (response.code === 1) {
      ElMessage.success('注册成功')
      emit('refresh')
      handleClose()
    } else {
      ElMessage.error(response.msg || '注册失败')
    }
  } catch (error) {
    console.error('注册失败:', error)
    ElMessage.error('注册失败')
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  resetForm()
}

// 组件挂载
onMounted(() => {
  fetchCarriers()
})
</script>

<style scoped lang="scss">
.register-tracking-dialog {
  .register-form {
    .el-form-item {
      margin-bottom: 20px;
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .register-tracking-dialog {
    .register-form {
      .el-form-item {
        margin-bottom: 15px;
      }
    }
  }
}
</style>
