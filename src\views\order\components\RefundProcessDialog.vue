<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="refundInfo" class="refund-process">
      <!-- 退款信息概览 -->
      <div class="refund-overview">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <span class="label">订单号：</span>
              <span class="value">{{ refundInfo.orderNumber }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">退款金额：</span>
              <span class="value amount">¥{{ refundInfo.refundAmount }}</span>
            </div>
          </el-col>
          <el-col :span="24">
            <div class="info-item">
              <span class="label">退款原因：</span>
              <span class="value">{{ refundInfo.refundReason }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 处理表单 -->
      <el-form
        ref="formRef"
        :model="processForm"
        :rules="rules"
        label-width="100px"
        label-position="left"
      >
        <el-form-item v-if="action === 'approve'" label="退款方式" prop="refundMethod" required>
          <el-radio-group v-model="processForm.refundMethod">
            <el-radio value="original">原路退回</el-radio>
            <el-radio value="manual">手动退款</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item v-if="action === 'approve'" label="退款金额" prop="actualAmount" required>
          <el-input-number
            v-model="processForm.actualAmount"
            :min="0"
            :max="refundInfo.refundAmount"
            :precision="2"
            style="width: 200px"
          />
          <span class="amount-tip">（最大可退：¥{{ refundInfo.refundAmount }}）</span>
        </el-form-item>

        <el-form-item v-if="action === 'approve'" label="预计到账" prop="estimatedTime">
          <el-select v-model="processForm.estimatedTime" placeholder="请选择预计到账时间">
            <el-option label="1-3个工作日" value="1-3" />
            <el-option label="3-5个工作日" value="3-5" />
            <el-option label="5-7个工作日" value="5-7" />
            <el-option label="7-15个工作日" value="7-15" />
          </el-select>
        </el-form-item>

        <el-form-item :label="action === 'approve' ? '处理备注' : '拒绝原因'" prop="remark" required>
          <el-input
            v-model="processForm.remark"
            type="textarea"
            :placeholder="action === 'approve' ? '请输入处理备注' : '请输入拒绝原因'"
            :rows="4"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item v-if="action === 'approve'" label="通知买家">
          <el-checkbox v-model="processForm.notifyBuyer">发送退款处理通知</el-checkbox>
        </el-form-item>
      </el-form>

      <!-- 风险提示 -->
      <el-alert
        v-if="action === 'reject'"
        title="拒绝退款风险提示"
        type="warning"
        :closable="false"
        show-icon
      >
        <template #default>
          <p>拒绝退款可能会影响店铺信誉，请确保有充分的理由。建议：</p>
          <ul>
            <li>详细说明拒绝原因</li>
            <li>提供相关证据或说明</li>
            <li>主动与买家沟通协商</li>
          </ul>
        </template>
      </el-alert>

      <el-alert
        v-if="action === 'approve'"
        title="退款确认提示"
        type="info"
        :closable="false"
        show-icon
      >
        <template #default>
          <p>同意退款后将无法撤销，请确认：</p>
          <ul>
            <li>退款金额是否正确</li>
            <li>退款方式是否合适</li>
            <li>是否已与买家确认</li>
          </ul>
        </template>
      </el-alert>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          :type="action === 'approve' ? 'success' : 'danger'"
          :loading="submitting"
          @click="handleSubmit"
        >
          {{ action === 'approve' ? '确认退款' : '确认拒绝' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { refundOrder } from '@/api/order'

interface Props {
  modelValue: boolean
  refundInfo?: any
  action: 'approve' | 'reject'
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = ref(false)
const submitting = ref(false)
const formRef = ref<FormInstance>()

// 处理表单
const processForm = reactive({
  refundMethod: 'original',
  actualAmount: 0,
  estimatedTime: '3-5',
  remark: '',
  notifyBuyer: true
})

// 对话框标题
const dialogTitle = computed(() => {
  return props.action === 'approve' ? '同意退款' : '拒绝退款'
})

// 表单验证规则
const rules: FormRules = {
  refundMethod: [
    { required: true, message: '请选择退款方式', trigger: 'change' }
  ],
  actualAmount: [
    { required: true, message: '请输入退款金额', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '退款金额必须大于0', trigger: 'blur' }
  ],
  estimatedTime: [
    { required: true, message: '请选择预计到账时间', trigger: 'change' }
  ],
  remark: [
    { required: true, message: props.action === 'approve' ? '请输入处理备注' : '请输入拒绝原因', trigger: 'blur' },
    { min: 5, max: 500, message: '长度在 5 到 500 个字符', trigger: 'blur' }
  ]
}

// 监听对话框显示状态
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal && props.refundInfo) {
    resetForm()
    processForm.actualAmount = props.refundInfo.refundAmount
  }
})

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 提交处理
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    submitting.value = true

    // 构建请求参数
    const requestData = {
      refundReason: processForm.remark,
      refundAmount: props.action === 'approve' ? processForm.actualAmount.toString() : '0'
    }

    const response = await refundOrder(props.refundInfo.id, requestData)
    
    if (response.code === 1) {
      ElMessage.success(props.action === 'approve' ? '退款处理成功' : '拒绝退款成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error(response.msg || '处理失败')
    }
  } catch (error) {
    console.error('处理退款失败:', error)
    ElMessage.error('处理失败')
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  processForm.refundMethod = 'original'
  processForm.actualAmount = 0
  processForm.estimatedTime = '3-5'
  processForm.remark = ''
  processForm.notifyBuyer = true
  formRef.value?.clearValidate()
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
}
</script>

<style scoped lang="scss">
.refund-process {
  .refund-overview {
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;
    margin-bottom: 20px;

    .info-item {
      margin-bottom: 12px;
      display: flex;
      align-items: center;

      .label {
        color: #606266;
        font-weight: 500;
        min-width: 80px;
      }

      .value {
        color: #303133;

        &.amount {
          color: #f56c6c;
          font-weight: 600;
          font-size: 16px;
        }
      }
    }
  }

  .amount-tip {
    margin-left: 10px;
    color: #909399;
    font-size: 12px;
  }

  .el-alert {
    margin-top: 20px;

    ul {
      margin: 10px 0 0 0;
      padding-left: 20px;

      li {
        margin-bottom: 5px;
        color: #606266;
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
