<template>
  <div class="page-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="page-header">
          <h2 class="page-title">页面标题</h2>
          <div class="page-actions">
            <el-button type="primary">
              <el-icon><Plus /></el-icon>
              <span>添加</span>
            </el-button>
          </div>
        </div>
      </template>

      <div class="page-content">
        <el-empty description="暂无内容" />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Plus } from '@element-plus/icons-vue'
</script>

<style scoped lang="scss">
.page-container {
  padding: 16px;

  .page-card {
    :deep(.el-card__header) {
      padding: 16px 20px;
    }

    :deep(.el-card__body) {
      padding: 20px;
    }
  }

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .page-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }

    .page-actions {
      display: flex;
      gap: 12px;
    }
  }

  .page-content {
    min-height: 400px;
  }
}
</style>
