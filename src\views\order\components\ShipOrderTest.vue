<template>
  <div class="ship-order-test">
    <el-card>
      <template #header>
        <h3>发货功能测试</h3>
      </template>
      
      <div class="test-section">
        <h4>测试发货对话框</h4>
        <el-button type="primary" @click="openShipDialog">
          打开发货对话框
        </el-button>
      </div>

      <div class="test-section">
        <h4>测试快递公司接口</h4>
        <el-button @click="testCourierAPI">
          获取快递公司列表
        </el-button>
        <div v-if="courierList.length > 0" class="courier-list">
          <h5>快递公司列表：</h5>
          <ul>
            <li v-for="courier in courierList" :key="courier.code">
              {{ courier.name }} ({{ courier.code }}) - {{ courier.country }}
            </li>
          </ul>
        </div>
      </div>

      <div class="test-section">
        <h4>测试发货接口</h4>
        <el-form :model="testForm" label-width="120px">
          <el-form-item label="订单ID">
            <el-input-number v-model="testForm.orderId" :min="1" />
          </el-form-item>
          <el-form-item label="物流单号">
            <el-input v-model="testForm.trackingNumber" placeholder="请输入物流单号" />
          </el-form-item>
          <el-form-item label="快递公司">
            <el-select v-model="testForm.courierCode" placeholder="请选择快递公司">
              <el-option
                v-for="courier in courierList"
                :key="courier.code"
                :label="courier.name"
                :value="courier.code"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="发货备注">
            <el-input v-model="testForm.remark" placeholder="可选" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="testShipAPI" :loading="testing">
              测试发货接口
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <div v-if="testResult" class="test-result">
        <h4>测试结果：</h4>
        <pre>{{ JSON.stringify(testResult, null, 2) }}</pre>
      </div>
    </el-card>

    <!-- 发货对话框 -->
    <ShipOrderDialog
      v-model="shipDialogVisible"
      :order-info="mockOrder"
      @success="handleShipSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { shipOrder, getCourierList } from '@/api/order'
import type { OrderVO, Courier, ShipOrderDTO } from '@/types/order'
import ShipOrderDialog from './ShipOrderDialog.vue'

// 测试状态
const testing = ref(false)
const shipDialogVisible = ref(false)
const courierList = ref<Courier[]>([])
const testResult = ref<any>(null)

// 测试表单
const testForm = reactive<ShipOrderDTO>({
  orderId: 1,
  trackingNumber: '',
  courierCode: '',
  courierName: '',
  shipNote: ''
})

// 模拟订单数据
const mockOrder = ref<OrderVO>({
  id: 1,
  number: 'TEST001',
  buyerId: 123,
  addressId: 1,
  status: 2,
  amount: 99.99,
  orderTime: '2024-01-01 10:00:00',
  payMethod: 1,
  orderDetails: []
})

// 打开发货对话框
const openShipDialog = () => {
  shipDialogVisible.value = true
}

// 测试快递公司接口
const testCourierAPI = async () => {
  try {
    const response = await getCourierList()
    if (response.code === 1) {
      courierList.value = response.data || []
      ElMessage.success('获取快递公司列表成功')
    } else {
      ElMessage.error(response.msg || '获取快递公司列表失败')
    }
  } catch (error) {
    console.error('获取快递公司列表失败:', error)
    ElMessage.error('获取快递公司列表失败')
  }
}

// 测试发货接口
const testShipAPI = async () => {
  if (!testForm.logisticsNumber || !testForm.logisticsCompany) {
    ElMessage.warning('请填写必要信息')
    return
  }

  testing.value = true
  try {
    // 不需要额外处理，logisticsCompany已经是选择的值

    const response = await shipOrder(testForm)
    testResult.value = response
    
    if (response.code === 1) {
      ElMessage.success('发货接口测试成功')
    } else {
      ElMessage.error(response.msg || '发货接口测试失败')
    }
  } catch (error) {
    console.error('发货接口测试失败:', error)
    testResult.value = { error: error.message }
    ElMessage.error('发货接口测试失败')
  } finally {
    testing.value = false
  }
}

// 发货成功回调
const handleShipSuccess = () => {
  ElMessage.success('发货对话框测试成功')
}
</script>

<style scoped lang="scss">
.ship-order-test {
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;

  h4 {
    margin-top: 0;
    color: #303133;
  }
}

.courier-list {
  margin-top: 15px;
  
  h5 {
    margin-bottom: 10px;
    color: #606266;
  }
  
  ul {
    margin: 0;
    padding-left: 20px;
    
    li {
      margin-bottom: 5px;
      color: #909399;
    }
  }
}

.test-result {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  
  h4 {
    margin-top: 0;
    color: #303133;
  }
  
  pre {
    margin: 0;
    color: #606266;
    font-size: 12px;
    white-space: pre-wrap;
    word-break: break-all;
  }
}
</style>
