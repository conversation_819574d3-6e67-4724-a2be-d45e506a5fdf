// 全局接口声明

interface Window {
  translate: {
    changeLanguage: (language: string) => void
    _changeLanguage: ((language: string) => void) | null
    selectLanguageTag: {
      show: boolean
    }
    service: {
      use: (service: string) => void
    }
    execute: () => void
  }
}

// 添加ImportMeta的环境变量声明
interface ImportMeta {
  readonly env: {
    readonly VITE_API_BASE_URL: string
    readonly VITE_HELP_CENTER_URL: string
    readonly [key: string]: string
  }
}

// 添加更多全局接口声明...
