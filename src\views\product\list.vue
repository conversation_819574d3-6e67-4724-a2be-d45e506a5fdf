<template>
  <div class="page-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="page-header">
          <h2 class="page-title">商品列表</h2>
          <div class="page-actions">
            <el-button type="primary" @click="goToAddProduct">
              <el-icon><Plus /></el-icon>
              <span>添加商品</span>
            </el-button>
          </div>
        </div>
      </template>

      <div class="page-content">
        <!-- 搜索过滤区域 -->
        <div class="filter-container">
          <el-form :inline="true" :model="filterForm" class="filter-form">
            <el-form-item label="商品名称">
              <el-input v-model="filterForm.name" placeholder="请输入商品名称" clearable />
            </el-form-item>
            <el-form-item label="商品分类">
              <el-select v-model="filterForm.category_id" placeholder="请选择分类" clearable>
                <el-option
                  v-for="category in categories"
                  :key="category.id"
                  :label="category.name"
                  :value="category.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="状态">
              <el-select v-model="filterForm.publish_status" placeholder="请选择状态" clearable>
                <el-option label="上架中" :value="1" />
                <el-option label="已下架" :value="0" />
              </el-select>
            </el-form-item>
            <el-form-item label="审核状态">
              <el-select v-model="filterForm.status" placeholder="请选择审核状态" clearable>
                <el-option label="审核通过" :value="1" />
                <el-option label="待审核" :value="0" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">搜索</el-button>
              <el-button @click="resetFilter">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 表格区域 -->
        <el-table
          :data="tableData"
          style="width: 100%"
          v-loading="loading"
          border
          :header-cell-style="{ background: '#f5f7fa' }"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column prop="id" label="商品ID" width="80" />
          <el-table-column label="商品信息" min-width="200">
            <template #default="scope">
              <div class="product-info">
                <el-image
                  :src="
                    scope.row.pic || 'https://placeholder.pics/svg/100x100/DEDEDE/555555/商品图片'
                  "
                  fit="cover"
                  class="product-image"
                  :preview-src-list="[scope.row.pic]"
                />
                <div class="product-detail">
                  <span class="product-name">{{ scope.row.name }}</span>
                  <span class="product-sku">{{ scope.row.outProductId || '暂无货号' }}</span>
                  <span class="product-brand" v-if="scope.row.brandName">
                    <el-tag size="small" effect="plain">{{ scope.row.brandName }}</el-tag>
                  </span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="分类" min-width="180">
            <template #default="scope">
              <el-tooltip
                :content="scope.row.productCategoryName"
                placement="top"
                :disabled="!scope.row.productCategoryName"
              >
                <div class="category-name">
                  {{ scope.row.productCategoryName || '未分类' }}
                </div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="属性" min-width="150">
            <template #default="scope">
              <div class="product-attrs">
                <template
                  v-if="scope.row.productAttr && parseProductAttr(scope.row.productAttr).length > 0"
                >
                  <el-popover placement="top" trigger="hover" :width="200">
                    <template #reference>
                      <el-tag size="small" effect="light" type="info">
                        {{ parseProductAttr(scope.row.productAttr).length }}个属性
                      </el-tag>
                    </template>
                    <div class="attr-list-popover">
                      <div
                        v-for="(attr, index) in parseProductAttr(scope.row.productAttr)"
                        :key="index"
                        class="attr-item-popover"
                      >
                        <span class="attr-key">{{ attr.key }}:</span>
                        <span class="attr-value">{{ Array.isArray(attr.value) ? attr.value.join(',') : attr.value }}</span>
                      </div>
                    </div>
                  </el-popover>
                </template>
                <template v-else>
                  <span class="no-attrs">无属性</span>
                </template>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="price" label="价格" width="100" sortable>
            <template #default="scope">
              <span class="price">￥{{ scope.row.price.toFixed(2) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="stock" label="库存" width="100" sortable>
            <template #default="scope">
              <el-tag :type="getStockTagType(scope.row.stock || 0)" size="small">
                {{ scope.row.stock || 0 }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="publishStatus" label="状态" width="90">
            <template #default="scope">
              <el-tag :type="getStatusTagType(scope.row.publishStatus)" effect="plain">
                {{ getStatusText(scope.row.publishStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <!-- 添加审核状态列 -->
          <el-table-column prop="status" label="审核状态" width="90">
            <template #default="scope">
              <el-tag :type="getAuditStatusTagType(scope.row.status)" effect="plain">
                {{ getAuditStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="scope">
              <!-- 待审核商品显示提示信息 -->
              <div v-if="scope.row.status === 0" class="audit-tip">
                <el-tooltip content="商品审核通过后才能执行操作" placement="top">
                  <span class="tip-text">商品待审核暂时无法操作</span>
                </el-tooltip>
              </div>
              
              <!-- 已审核商品显示正常操作按钮 -->
              <div v-else class="operation-buttons">
                <el-button
                  size="small"
                  type="primary"
                  :plain="true"
                  class="btn-edit"
                  @click="handleEdit(scope.row)"
                >
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-button>
                <el-button
                  size="small"
                  :type="scope.row.publishStatus === 1 ? 'warning' : 'success'"
                  :plain="true"
                  class="btn-status"
                  @click="handleStatusChange(scope.row)"
                >
                  <el-icon>
                    <BottomRight v-if="scope.row.publishStatus === 1" />
                    <TopRight v-else />
                  </el-icon>
                  {{ scope.row.publishStatus === 1 ? '下架' : '上架' }}
                </el-button>
                <el-button
                  size="small"
                  type="danger"
                  :plain="true"
                  class="btn-delete"
                  @click="handleDelete(scope.row)"
                >
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页区域 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Edit, Delete, BottomRight, TopRight } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import {
  getSellerProductList,
  updateSellerProduct,
  deleteProduct,
  deleteSellerProduct,
} from '@/api/product'
import { getCategoryTree } from '@/api/category'

const router = useRouter()
const userStore = useUserStore()

// 筛选表单
const filterForm = reactive({
  name: '',
  category_id: '',
  publish_status: '',
  status: '',
})

// 商品分类列表
const categories = ref<any[]>([])

// 加载商品分类
const loadCategories = async () => {
  try {
    const res = await getCategoryTree()
    if (res.code === 1 && res.data) {
      categories.value = flattenCategories(res.data)
    }
  } catch (error) {
    console.error('获取分类列表失败:', error)
  }
}

// 将树形分类数据转为扁平数组
const flattenCategories = (categoryTree: any[]): any[] => {
  let result: any[] = []
  categoryTree.forEach((cat) => {
    result.push({
      id: cat.id,
      name: cat.name,
    })
    if (cat.children && cat.children.length > 0) {
      const children = flattenCategories(cat.children)
      children.forEach((child) => {
        result.push({
          id: child.id,
          name: `${cat.name} > ${child.name}`,
        })
      })
    }
  })
  return result
}

// 重置筛选条件
const resetFilter = () => {
  filterForm.name = ''
  filterForm.category_id = ''
  filterForm.publish_status = ''
  filterForm.status = ''
  applyFilters()
}

// 表格数据
const tableData = ref<any[]>([])
// 所有商品数据（未分页）
const allProductData = ref<any[]>([])

// 分页相关
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 过滤后的数据
const filteredData = ref<any[]>([])

// 应用过滤器
const applyFilters = () => {
  const { name, category_id, publish_status, status } = filterForm

  // 过滤数据
  filteredData.value = allProductData.value.filter((item) => {
    // 按名称过滤
    if (name && !item.name.toLowerCase().includes(name.toLowerCase())) {
      return false
    }

    // 按分类过滤
    if (category_id && item.categoryId !== category_id) {
      return false
    }

    // 按状态过滤
    if (publish_status !== '' && item.publishStatus !== publish_status) {
      return false
    }

    // 按审核状态过滤
    if (status !== '' && item.status !== status) {
      return false
    }

    return true
  })

  // 更新总数
  total.value = filteredData.value.length
  // 重置到第一页
  currentPage.value = 1
  // 应用分页
  handlePagination()
}

// 前端分页处理
const handlePagination = () => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  // 从过滤后的数据中截取当前页的数据
  tableData.value = filteredData.value.slice(start, end)
}

// 获取库存标签类型
const getStockTagType = (stock: number) => {
  if (stock === 0) {
    return 'danger'
  } else if (stock < 20) {
    return 'warning'
  }
  return 'success'
}

// 获取状态标签类型
const getStatusTagType = (status: number) => {
  switch (status) {
    case 1:
      return 'success'
    case 0:
      return 'info'
    default:
      return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: number) => {
  switch (status) {
    case 1:
      return '上架中'
    case 0:
      return '已下架'
    default:
      return '未知'
  }
}

// 获取审核状态标签类型
const getAuditStatusTagType = (status: number) => {
  switch (status) {
    case 1:
      return 'success'
    case 0:
      return 'info'
    default:
      return 'info'
  }
}

// 获取审核状态文本
const getAuditStatusText = (status: number) => {
  switch (status) {
    case 1:
      return '审核通过'
    case 0:
      return '待审核'
    default:
      return '未知'
  }
}

// 处理搜索
const handleSearch = async () => {
  if (allProductData.value.length === 0) {
    // 如果还没有获取过数据，则从服务器获取
    await fetchProductData()
  } else {
    // 否则只在前端过滤现有数据
    applyFilters()
  }
}

// 从服务器获取商品数据
const fetchProductData = async () => {
  loading.value = true
  const sellerId = userStore.userInfo?.id

  if (!sellerId) {
    ElMessage.error('未获取到商家ID，请重新登录')
    loading.value = false
    return
  }

  try {
    console.log('获取商家商品列表，sellerId:', sellerId)

    // 调用API获取商品列表
    const res = await getSellerProductList(sellerId)
    console.log('获取商品列表响应:', res)

    if (res.code === 1) {
      // 保存所有数据
      allProductData.value = res.data || []
      // 应用过滤器
      applyFilters()

      console.log('商品列表数据加载完成，总数:', allProductData.value.length)
    } else {
      ElMessage.warning(res.msg || '获取商品列表失败')
      allProductData.value = []
      filteredData.value = []
      tableData.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取商品列表出错:', error)
    ElMessage.error('获取商品列表失败，请稍后重试')
    allProductData.value = []
    filteredData.value = []
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 跳转到添加商品页面
const goToAddProduct = () => {
  router.push('/main/product/add')
}

// 处理编辑
const handleEdit = (row: any) => {
  router.push(`/main/product/edit/${row.id}`)
}

// 处理状态变更
const handleStatusChange = (row: any) => {
  const newStatus = row.publishStatus === 1 ? 0 : 1
  const actionText = newStatus === 1 ? '上架' : '下架'

  ElMessageBox.confirm(`确定要${actionText}商品 "${row.name}" 吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      loading.value = true
      try {
        // 获取商家ID
        const sellerId = userStore.userInfo?.id
        if (!sellerId) {
          ElMessage.error('未获取到商家ID，请重新登录')
          return
        }

        // 构建仅包含必要字段的商品对象
        const product = {
          id: row.id,
          publishStatus: newStatus,
          // 保留必要的其他字段以防后端验证需要
          name: row.name,
        }

        // 调用API更新商品状态
        const res = await updateSellerProduct(sellerId, row.id, product, null, [])

        if (res.code === 1) {
          // 更新本地数据状态
          row.publishStatus = newStatus
          // 更新所有数据中的状态
          const item = allProductData.value.find((item) => item.id === row.id)
          if (item) {
            item.publishStatus = newStatus
          }

          ElMessage.success(`商品${actionText}成功`)
        } else {
          ElMessage.error(res.msg || `商品${actionText}失败`)
        }
      } catch (error) {
        console.error(`更新商品状态出错:`, error)
        ElMessage.error(`商品${actionText}失败，请稍后重试`)
      } finally {
        loading.value = false
      }
    })
    .catch(() => {
      // 用户取消操作
    })
}

// 处理删除
const handleDelete = (row: any) => {
  ElMessageBox.confirm(`确定要删除商品 "${row.name}" 吗?`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      loading.value = true
      try {
        // 获取商家ID
        const sellerId = userStore.userInfo?.id
        if (!sellerId) {
          ElMessage.error('未获取到商家ID，请重新登录')
          return
        }

        // 调用API删除商品
        const res = await deleteSellerProduct(sellerId, row.id)
        if (res.code === 1) {
          // 从本地数据中移除被删除的商品
          allProductData.value = allProductData.value.filter((item) => item.id !== row.id)
          // 重新应用过滤和分页
          applyFilters()

          ElMessage.success('删除成功')
        } else {
          ElMessage.error(res.msg || '删除失败')
        }
      } catch (error) {
        console.error('删除商品出错:', error)
        ElMessage.error('删除失败，请稍后重试')
      } finally {
        loading.value = false
      }
    })
    .catch(() => {
      // 用户取消操作
    })
}

// 处理分页大小变更
const handleSizeChange = (val: number) => {
  pageSize.value = val
  handlePagination()
}

// 处理页码变更
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  handlePagination()
}

// 解析商品属性（从JSON字符串转为对象）
const parseProductAttr = (attrJson: string | null): { key: string; value: string[] }[] => {
  if (!attrJson) return []
  // console.log('商品属性：',JSON.parse(attrJson));
  try {
    return JSON.parse(attrJson)
  } catch (error) {
    console.error('解析商品属性出错:', error)
    return []
  }
}

// 页面加载时获取数据
onMounted(() => {
  loadCategories()
  fetchProductData()
})
</script>

<style scoped lang="scss">
.page-container {
  padding: 16px;

  .page-card {
    :deep(.el-card__header) {
      padding: 16px 20px;
    }

    :deep(.el-card__body) {
      padding: 20px;
    }
  }

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .page-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }

    .page-actions {
      display: flex;
      gap: 12px;
    }
  }

  .filter-container {
    margin-bottom: 20px;
    padding: 16px;
    background-color: #f9f9f9;
    border-radius: 4px;

    .filter-form {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;

      :deep(.el-form-item) {
        margin-bottom: 0;
        margin-right: 16px;
      }
    }
  }

  .product-info {
    display: flex;
    align-items: center;

    .product-image {
      width: 50px;
      height: 50px;
      border-radius: 4px;
      object-fit: cover;
      margin-right: 10px;
    }

    .product-detail {
      display: flex;
      flex-direction: column;

      .product-name {
        font-weight: 500;
        margin-bottom: 4px;
      }

      .product-sku {
        color: #909399;
        font-size: 12px;
      }
    }
  }

  .operation-buttons {
    display: flex;
    flex-wrap: nowrap;
    gap: 3px;
    justify-content: space-between;

    :deep(.el-button) {
      padding: 4px 8px;
      font-size: 12px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 2px;
      min-width: 0;

      .el-icon {
        margin-right: 2px;
        font-size: 14px;
      }
    }
  }

  .audit-tip {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;
    
    .tip-text {
      color: #909399;
      font-size: 12px;
      cursor: pointer;
      text-align: center;
      border: 1px dashed #dcdfe6;
      padding: 4px 8px;
      border-radius: 4px;
      background-color: #f8f8f8;
      
      &:hover {
        color: #409eff;
        border-color: #c6e2ff;
        background-color: #ecf5ff;
      }
    }
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}

// 响应式调整
@media screen and (max-width: 768px) {
  .page-container {
    .filter-container {
      .filter-form {
        flex-direction: column;

        :deep(.el-form-item) {
          margin-right: 0;
          margin-bottom: 10px;
          width: 100%;
        }
      }
    }
  }
}
</style>
