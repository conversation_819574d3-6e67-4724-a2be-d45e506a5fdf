/**
 * 图片工具函数（增强版）
 */

/**
 * 智能解析图片URL字符串
 * @param {string|Array} urlInput - 可能包含多个图片URL的字符串或数组
 * @returns {Array} 解析后的URL数组
 */
export const parseImageUrls = (urlInput) => {
  if (!urlInput) return []

  try {
    // 处理数组类型输入
    if (Array.isArray(urlInput)) {
      return urlInput
        .filter((url) => typeof url === 'string')
        .map((url) => url.trim())
        .filter((url) => url.startsWith('http'))
    }

    // 处理字符串类型输入
    if (typeof urlInput === 'string') {
      const trimmed = urlInput.trim()
      if (!trimmed) return []

      // 增强版正则表达式（支持包含特殊字符的完整URL）
      const urlPattern = /https?:\/\/(?:[^\s,()]|\([^)]+\))+(?:\.(?:jpe?g|png|gif|webp|svg))/gi

      const urls = []
      let lastIndex = 0
      let match

      while ((match = urlPattern.exec(trimmed)) !== null) {
        // 处理URL之间的无效字符
        if (match.index > lastIndex) {
          const between = trimmed.slice(lastIndex, match.index)
          if (between.includes('http')) {
            console.warn('发现可能未处理的URL片段:', between)
          }
        }

        let url = match[0]
        // 清理尾部无效字符（非URL组成部分）
        url = url.replace(/[,\s]+$/, '')

        // 二次验证URL结构
        if (/\.(jpe?g|png|gif|webp|svg)(\?.*)?$/.test(url)) {
          urls.push(url)
          lastIndex = urlPattern.lastIndex
        }
      }

      return [...new Set(urls)] // 去重处理
    }

    return []
  } catch (err) {
    console.error('URL解析异常:', err)
    return []
  }
}

/**
 * 增强版图片错误处理
 * @param {Event} event - 错误事件
 * @param {string} size - 占位图尺寸
 * @param {string} type - 图片类型（avatar/content等）
 */
export const handleImageError = (event, size = '300x300', type = 'content') => {
  const [width, height] = size.split('x')
  const img = event.target

  // 保留原始URL信息用于调试
  img.dataset.failedUrl = img.src

  // 根据类型设置不同占位图
  img.src =
    type === 'avatar'
      ? `https://ui-avatars.com/api/?name=User&size=${width}`
      : `https://via.placeholder.com/${width}x${height}?text=Image+Not+Found`

  // 添加错误状态标识
  img.classList.add('broken-image')
}

export default {
  parseImageUrls,
  handleImageError,
}
