﻿<template>
  <div class="declaration-page">
    <div class="declaration-container">
      <div class="declaration-header">
        <h1>Sharewharf平台供应商协议</h1>
      </div>

      <div class="declaration-content">
        <div class="content-section">
          <h3>一、定义</h3>
          <p>1.1 Sharewharf: 指Sharewharf Technology Co.,Limited，系Sharewharf平台的经营者。</p>
          <p>
            1.2 Sharewharf平台: 指由Sharewharf Technology
            Co.,Limited主办运营的企业间贸易在线信息发布及交易平台，域名为https://www.Sharewharf.com/。
          </p>
          <p>1.3 工作日: 指香港的银行开门经营一般银行业务的日子（星期六星期日除外）。</p>
          <p>1.4 客户: 指所有在Sharewharf平台上注册成为用户的法人、自然人及其他组织。</p>
          <p>1.5 平台规则: 指平台服务协议、交易规则、公告等规范性文件。</p>
        </div>

        <div class="content-section">
          <h3>二、服务内容</h3>
          <p>
            2.1 Sharewharf平台交易服务：供应商可通过平台发布商品信息、进行报价并与客户达成交易。
          </p>
          <p>2.2 Sharewharf物流及仓储服务：提供头程海运、仓储管理、第三方配送等有偿服务。</p>
          <p>2.3 咨询服务：提供平台使用指导及运营建议，供应商自行决定是否采纳。</p>
          <p>2.4 特殊需求服务：供应商提出的定制化服务需求视为有效订单。</p>
        </div>

        <div class="content-section">
          <h3>三、费用与付款</h3>
          <p>3.1 平台佣金：交易总额的5%（可能享有优惠）。</p>
          <p>3.2 物流费用：包括海运、仓储、配送等相关费用。</p>
          <p>3.3 结算周期：每周结算，N+2周账单日，账单日后0-1工作日支付。</p>
          <p>3.4 售后保证金：冻结$1000用于售后处理，清算后多退少补。</p>
          <p>3.5 逾期利息：未付款项按年利率24%计息。</p>
        </div>

        <div class="content-section">
          <h3>四、声明及保证</h3>
          <p>4.1 保证注册资料真实有效，妥善保管账户信息。</p>
          <p>4.2 保证商品质量合格且不侵犯第三方知识产权。</p>
          <p>4.3 遵守平台规则，维护良性竞争秩序。</p>
          <p>4.4 承担商品售后责任，提供合规票据。</p>
          <p>4.5 授权平台使用商品信息进行推广展示。</p>
        </div>

        <div class="content-section">
          <h3>五、售后责任</h3>
          <p>5.1 物流问题退货：运输丢包/破损等情况由供应商承担费用。</p>
          <p>5.2 质量问题退货：产品缺陷/描述不符等情况由供应商承担费用。</p>
          <p>5.3 无理由退货：买方承担物流费用，商品影响二次销售扣20%处置费。</p>
          <p>5.4 发货时效：超7工作日未发货需双倍赔偿。</p>
          <p>5.5 售后处理时限：需在7个工作日内响应处理。</p>
        </div>

        <div class="content-section">
          <h3>六、违约责任</h3>
          <p>6.1 平台有权采取下架商品、暂停服务、冻结账户等措施。</p>
          <p>6.2 赔偿范围包括律师费、商誉损失、行政处罚等。</p>
          <p>6.3 禁止线下交易：违约需支付$500+交易额25%的赔偿。</p>
          <p>6.4 责任上限：最近3个月佣金或$1000取低值。</p>
        </div>

        <div class="content-section">
          <h3>七、协议变更与终止</h3>
          <p>7.1 费用条款变更需提前通知，异议期3个工作日。</p>
          <p>7.2 终止后需处理3个月内售后问题并完成清算。</p>
          <p>7.3 逾期未搬离货物平台有权处置并收取费用。</p>
        </div>

        <div class="content-section">
          <h3>八、其他条款</h3>
          <p>8.1 适用香港特别行政区法律，争议提交中国办事处所在地法院。</p>
          <p>8.2 保密义务：禁止披露平台技术细节和运营数据。</p>
          <p>8.3 通知方式：站内信、邮件等电子送达视为有效。</p>
        </div>
      </div>

      <div class="declaration-footer">
        <el-checkbox v-model="agreed" class="declaration-checkbox">
          我已阅读并同意以上协议内容
        </el-checkbox>
        <div class="button-group">
          <el-button @click="handleBack">返回登录</el-button>
          <el-button type="primary" :disabled="!agreed" @click="handleConfirm">
            确认协议并注册
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

const router = useRouter()
const agreed = ref(false)

const handleBack = () => {
  router.push('/')
}

const handleConfirm = () => {
  if (agreed.value) {
    ElMessage.success('您已同意商城声明，即将跳转到注册页面')
    router.push('/register/step1')
  }
}
</script>

<style scoped lang="scss">
.declaration-page {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #eef2f7 100%);
  padding: 0;
  margin: 0;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.declaration-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  background: white;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  border-radius: 0;
}

.declaration-header {
  padding: 20px 30px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;

  h1 {
    font-size: 24px;
    color: #333;
    margin: 0;
    font-weight: 600;
  }
}

.declaration-content {
  flex: 1;
  padding: 20px 40px;
  overflow-y: auto;
  max-height: calc(100vh - 140px);

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #ddd;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background-color: #f5f5f5;
  }

  .content-section {
    margin-bottom: 25px;

    h3 {
      font-size: 18px;
      color: #333;
      margin-bottom: 12px;
      font-weight: 600;
    }

    p {
      font-size: 14px;
      color: #666;
      line-height: 1.7;
      margin-bottom: 10px;
      text-indent: 2em;
    }
  }
}

.declaration-footer {
  padding: 15px 40px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fafafa;

  .declaration-checkbox {
    color: #333;
    font-size: 14px;
  }

  .button-group {
    display: flex;
    gap: 15px;
  }
}

@media (max-width: 768px) {
  .declaration-container {
    min-height: 100vh;
    border-radius: 0;
    max-width: 100%;
  }

  .declaration-content {
    padding: 15px 20px;
  }

  .declaration-footer {
    flex-direction: column;
    gap: 15px;
    padding: 15px 20px;

    .button-group {
      width: 100%;
      justify-content: center;
    }
  }
}
</style>
