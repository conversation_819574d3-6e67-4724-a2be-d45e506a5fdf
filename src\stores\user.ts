import { defineStore } from 'pinia'
import { ref, reactive, watch } from 'vue'
import { getToken, removeToken, isTokenExpired } from '@/utils/auth'

export interface UserInfo {
  id: number
  accountName: string
  avatar?: string
  role?: string
  userRole?: string // 添加用户角色字段: 超级管理员, 管理员, 普通用户
  permission?: string[] // 修改这里，改为permission与后端一致
}
 
export interface UserPreferences {
  defaultPage: string
  sidebarCollapsed: boolean
  tableDensity: string
  language: string
  themeColor: string
}

// 用户信息本地存储键
const USER_INFO_KEY = 'user_info'
const LOGGED_IN_KEY = 'is_logged_in'

// 从localStorage读取用户信息
const getSavedUserInfo = (): UserInfo | null => {
  const savedInfo = localStorage.getItem(USER_INFO_KEY)
  if (savedInfo) {
    try {
      return JSON.parse(savedInfo)
    } catch (e) {
      console.error('解析本地存储的用户信息失败:', e)
      return null
    }
  }
  return null
}

// 从localStorage读取登录状态
const getSavedLoginStatus = (): boolean => {
  return localStorage.getItem(LOGGED_IN_KEY) === 'true'
}

export const useUserStore = defineStore('user', () => {
  const token = ref<string | null>(getToken())
  const userInfo = ref<UserInfo | null>(getSavedUserInfo())
  const isLoggedIn = ref(getSavedLoginStatus())
  const permissions = ref<string[]>(userInfo.value?.permission || [])

  const unreadCount=ref(0)
  const updataUnreadCount=(newCount:number)=>{
    unreadCount.value=newCount
  }
  // 用户偏好设置
  const preferences = reactive<UserPreferences>({
    defaultPage: localStorage.getItem('defaultPage') || 'welcome',
    sidebarCollapsed: localStorage.getItem('sidebarCollapsed') === 'true',
    tableDensity: localStorage.getItem('tableDensity') || 'default',
    language: localStorage.getItem('currentLanguage') || 'chinese_simplified',
    themeColor: localStorage.getItem('themeColor') || '#409EFF',
  })

  // 监听用户信息变化，保存到localStorage
  watch(
    userInfo,
    (newValue) => {
      if (newValue) {
        localStorage.setItem(USER_INFO_KEY, JSON.stringify(newValue))
      } else {
        localStorage.removeItem(USER_INFO_KEY)
      }
    },
    { deep: true },
  )

  // 监听登录状态变化，保存到localStorage
  watch(isLoggedIn, (newValue) => {
    localStorage.setItem(LOGGED_IN_KEY, String(newValue))
  })

  // 设置用户信息
  const setUserInfo = (info: UserInfo) => {
    console.log('设置用户信息:', info)
    userInfo.value = info

    // 如果info中包含permission，更新permissions值
    if (info.permission) {
      permissions.value = info.permission
    }

    isLoggedIn.value = true
    // 立即保存到localStorage
    localStorage.setItem(USER_INFO_KEY, JSON.stringify(info))
    localStorage.setItem(LOGGED_IN_KEY, 'true')
  }

  // 清除用户信息
  const clearUserInfo = () => {
    userInfo.value = null
    isLoggedIn.value = false
    permissions.value = []
    removeToken()
    token.value = null
    // 立即从localStorage移除
    localStorage.removeItem(USER_INFO_KEY)
    localStorage.removeItem(LOGGED_IN_KEY)
  }

  // 设置用户权限
  const setPermissions = (perms: string[]) => {
    permissions.value = perms
    if (userInfo.value) {
      userInfo.value.permission = perms
      localStorage.setItem(USER_INFO_KEY, JSON.stringify(userInfo.value))
    }
  }

  // 检查用户是否有指定权限
  const hasPermission = (permission: string): boolean => {
    return permissions.value.includes(permission)
  }

  // 检查登录状态
  const checkLoginStatus = () => {
    // 首先检查token是否有效
    if (isTokenExpired()) {
      clearUserInfo()
      return false
    }

    // 然后检查是否有用户信息
    if (!userInfo.value) {
      // 尝试从localStorage恢复
      const savedInfo = getSavedUserInfo()
      if (savedInfo) {
        userInfo.value = savedInfo
        if (savedInfo.permission) {
          permissions.value = savedInfo.permission
        }
        isLoggedIn.value = true
        return true
      }
      return false
    }

    return isLoggedIn.value
  }

  // 获取用户默认页面
  const getDefaultPage = () => {
    return preferences.defaultPage
  }

  // 设置用户偏好
  const setPreference = (key: keyof UserPreferences, value: any) => {
    if (key in preferences) {
      ;(preferences as any)[key] = value
      localStorage.setItem(key, typeof value === 'string' ? value : String(value))
    }
  }

  // 加载用户所有偏好设置
  const loadPreferences = () => {
    const keys: (keyof UserPreferences)[] = [
      'defaultPage',
      'sidebarCollapsed',
      'tableDensity',
      'language',
      'themeColor',
    ]

    keys.forEach((key) => {
      const value = localStorage.getItem(key)
      if (value !== null) {
        if (key === 'sidebarCollapsed') {
          preferences[key] = value === 'true'
        } else {
          ;(preferences as any)[key] = value
        }
      }
    })
  }

  return {
    token,
    userInfo,
    isLoggedIn,
    preferences,
    permissions,
    unreadCount,
    updataUnreadCount,
    setUserInfo,
    clearUserInfo,
    checkLoginStatus,
    getDefaultPage,
    setPreference,
    loadPreferences,
    setPermissions,
    hasPermission,
  }
})
