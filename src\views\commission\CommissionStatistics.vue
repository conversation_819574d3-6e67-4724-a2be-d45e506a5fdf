<template>
  <div class="commission-statistics-container">
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">佣金统计</h1>
        <div class="page-subtitle">分析团长佣金数据和分布情况</div>
      </div>
      <div class="header-right">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :shortcuts="dateShortcuts"
          @change="handleDateChange"
        />
        <el-button type="primary" @click="exportData">导出数据</el-button>
      </div>
    </div>

    <!-- 佣金总览统计卡片 -->
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card shadow="hover" class="stats-card">
          <div class="stats-info">
            <div class="stats-label">累计佣金总额</div>
            <div class="stats-value">¥{{ overview.totalAmount }}</div>
            <div class="stats-trend" :class="overview.totalAmountTrend > 0 ? 'up' : 'down'">
              <el-icon v-if="overview.totalAmountTrend > 0"><ArrowUp /></el-icon>
              <el-icon v-else><ArrowDown /></el-icon>
              {{ Math.abs(overview.totalAmountTrend) }}% 较上期
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="stats-card">
          <div class="stats-info">
            <div class="stats-label">已结算佣金</div>
            <div class="stats-value">¥{{ overview.settledAmount }}</div>
            <div class="stats-trend" :class="overview.settledAmountTrend > 0 ? 'up' : 'down'">
              <el-icon v-if="overview.settledAmountTrend > 0"><ArrowUp /></el-icon>
              <el-icon v-else><ArrowDown /></el-icon>
              {{ Math.abs(overview.settledAmountTrend) }}% 较上期
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="stats-card">
          <div class="stats-info">
            <div class="stats-label">待结算佣金</div>
            <div class="stats-value">¥{{ overview.pendingAmount }}</div>
            <div class="stats-trend" :class="overview.pendingAmountTrend > 0 ? 'up' : 'down'">
              <el-icon v-if="overview.pendingAmountTrend > 0"><ArrowUp /></el-icon>
              <el-icon v-else><ArrowDown /></el-icon>
              {{ Math.abs(overview.pendingAmountTrend) }}% 较上期
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="stats-card">
          <div class="stats-info">
            <div class="stats-label">佣金团长数</div>
            <div class="stats-value">{{ overview.leaderCount }}</div>
            <div class="stats-trend" :class="overview.leaderCountTrend > 0 ? 'up' : 'down'">
              <el-icon v-if="overview.leaderCountTrend > 0"><ArrowUp /></el-icon>
              <el-icon v-else><ArrowDown /></el-icon>
              {{ Math.abs(overview.leaderCountTrend) }}% 较上期
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 佣金趋势图 -->
    <el-card shadow="hover" class="chart-card">
      <template #header>
        <div class="card-header">
          <span>佣金趋势</span>
          <div class="header-actions">
            <el-radio-group v-model="trendPeriod" size="small" @change="handlePeriodChange">
              <el-radio-button label="day">按日</el-radio-button>
              <el-radio-button label="week">按周</el-radio-button>
              <el-radio-button label="month">按月</el-radio-button>
            </el-radio-group>
          </div>
        </div>
      </template>
      <div class="chart-container" style="height: 350px">
        <!-- 这里实际项目中会使用ECharts等图表库 -->
        <div class="chart-placeholder">
          <div class="chart-line"></div>
          <div class="chart-labels">
            <div v-for="(month, index) in trendData.months" :key="index" class="chart-label">
              {{ month }}
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 佣金类型分布 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>佣金类型分布</span>
            </div>
          </template>
          <div class="flex-container">
            <div class="pie-chart-container">
              <!-- 这里实际项目中会使用ECharts等图表库 -->
              <div class="pie-chart"></div>
            </div>
            <div class="chart-legend">
              <div v-for="(item, index) in typeDistribution" :key="index" class="legend-item">
                <div class="legend-color" :style="{ backgroundColor: item.color }"></div>
                <div class="legend-info">
                  <div class="legend-name">{{ item.name }}</div>
                  <div class="legend-value">¥{{ item.value }} ({{ item.percentage }}%)</div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>团长类型佣金分布</span>
            </div>
          </template>
          <div class="flex-container">
            <div class="pie-chart-container">
              <!-- 这里实际项目中会使用ECharts等图表库 -->
              <div class="pie-chart leader-chart"></div>
            </div>
            <div class="chart-legend">
              <div v-for="(item, index) in leaderDistribution" :key="index" class="legend-item">
                <div class="legend-color" :style="{ backgroundColor: item.color }"></div>
                <div class="legend-info">
                  <div class="legend-name">{{ item.name }}</div>
                  <div class="legend-value">¥{{ item.value }} ({{ item.percentage }}%)</div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 佣金排行榜 -->
    <el-card shadow="hover" class="ranking-card">
      <template #header>
        <div class="card-header">
          <span>团长佣金排行榜</span>
          <div class="header-actions">
            <el-select
              v-model="rankingType"
              placeholder="排序方式"
              size="small"
              @change="handleRankingChange"
            >
              <el-option label="累计佣金" value="total" />
              <el-option label="本月佣金" value="month" />
              <el-option label="待结算佣金" value="pending" />
            </el-select>
          </div>
        </div>
      </template>
      <el-table :data="leaderRanking" style="width: 100%" v-loading="loading">
        <el-table-column type="index" label="排名" width="60" />
        <el-table-column prop="name" label="团长姓名" min-width="120" />
        <el-table-column prop="type" label="团长类型" width="120">
          <template #default="scope">
            <el-tag :type="scope.row.type === '商家招募' ? 'success' : 'primary'" size="small">
              {{ scope.row.type }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="totalAmount" label="累计佣金" width="150" sortable>
          <template #default="scope">
            <span class="amount">¥{{ scope.row.totalAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="monthAmount" label="本月佣金" width="150" sortable>
          <template #default="scope">
            <span class="amount">¥{{ scope.row.monthAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="pendingAmount" label="待结算佣金" width="150" sortable>
          <template #default="scope">
            <span class="amount">¥{{ scope.row.pendingAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="orderCount" label="推广订单数" width="120" sortable />
        <el-table-column prop="lastSettleTime" label="最近结算时间" min-width="160" />
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" link @click="viewLeaderDetail(scope.row.id)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ArrowUp, ArrowDown } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const router = useRouter()
const loading = ref(false)
const dateRange = ref([])
const trendPeriod = ref('month')
const rankingType = ref('total')

// 日期快捷选项
const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    },
  },
]

// 佣金总览数据
const overview = reactive({
  totalAmount: '182,560.75',
  totalAmountTrend: 15.2,
  settledAmount: '135,680.40',
  settledAmountTrend: 12.8,
  pendingAmount: '46,880.35',
  pendingAmountTrend: -3.5,
  leaderCount: 68,
  leaderCountTrend: 8.6,
})

// 趋势图数据
const trendData = reactive({
  months: ['1月', '2月', '3月', '4月', '5月', '6月'],
  amounts: [12500, 18600, 15400, 22800, 20500, 25000],
})

// 佣金类型分布数据
const typeDistribution = reactive([
  { name: '商品佣金', value: '125,680.50', percentage: 68.8, color: '#409EFF' },
  { name: '推广佣金', value: '42,560.25', percentage: 23.3, color: '#67C23A' },
  { name: '活动奖励', value: '14,320.00', percentage: 7.9, color: '#E6A23C' },
])

// 团长类型佣金分布
const leaderDistribution = reactive([
  { name: '商家招募团长', value: '112,345.50', percentage: 61.5, color: '#409EFF' },
  { name: '用户招募团长', value: '70,215.25', percentage: 38.5, color: '#67C23A' },
])

// 团长佣金排行榜
const leaderRanking = ref([
  {
    id: 1,
    name: '张三',
    type: '商家招募',
    totalAmount: '15,680.50',
    monthAmount: '2,560.25',
    pendingAmount: '1,320.75',
    orderCount: 128,
    lastSettleTime: '2023-08-10 10:30:22',
  },
  {
    id: 2,
    name: '李四',
    type: '用户招募',
    totalAmount: '12,430.25',
    monthAmount: '1,860.50',
    pendingAmount: '960.30',
    orderCount: 95,
    lastSettleTime: '2023-08-15 14:25:18',
  },
  {
    id: 3,
    name: '王五',
    type: '商家招募',
    totalAmount: '10,520.75',
    monthAmount: '1,750.20',
    pendingAmount: '850.45',
    orderCount: 82,
    lastSettleTime: '2023-08-12 09:15:40',
  },
  {
    id: 4,
    name: '赵六',
    type: '用户招募',
    totalAmount: '8,745.80',
    monthAmount: '1,420.30',
    pendingAmount: '720.15',
    orderCount: 64,
    lastSettleTime: '2023-08-18 16:45:30',
  },
  {
    id: 5,
    name: '钱七',
    type: '商家招募',
    totalAmount: '7,890.15',
    monthAmount: '1,280.60',
    pendingAmount: '650.20',
    orderCount: 58,
    lastSettleTime: '2023-08-20 11:30:15',
  },
])

// 处理日期改变
const handleDateChange = () => {
  if (!dateRange.value || dateRange.value.length !== 2) return
  console.log('日期范围改变:', dateRange.value)
  // 这里实际项目中会重新加载数据
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 500)
}

// 处理周期改变
const handlePeriodChange = () => {
  console.log('周期改变:', trendPeriod.value)
  // 这里实际项目中会重新加载趋势图数据
}

// 处理排行榜排序方式改变
const handleRankingChange = () => {
  console.log('排行榜排序改变:', rankingType.value)
  // 这里实际项目中会重新排序
  loading.value = true
  setTimeout(() => {
    // 模拟排序
    if (rankingType.value === 'month') {
      leaderRanking.value.sort((a, b) => {
        return (
          Number(b.monthAmount.replace(/[^\d.]/g, '')) -
          Number(a.monthAmount.replace(/[^\d.]/g, ''))
        )
      })
    } else if (rankingType.value === 'pending') {
      leaderRanking.value.sort((a, b) => {
        return (
          Number(b.pendingAmount.replace(/[^\d.]/g, '')) -
          Number(a.pendingAmount.replace(/[^\d.]/g, ''))
        )
      })
    } else {
      leaderRanking.value.sort((a, b) => {
        return (
          Number(b.totalAmount.replace(/[^\d.]/g, '')) -
          Number(a.totalAmount.replace(/[^\d.]/g, ''))
        )
      })
    }
    loading.value = false
  }, 500)
}

// 导出数据
const exportData = () => {
  ElMessage.info('导出功能将在实际项目中实现')
}

// 查看团长详情
const viewLeaderDetail = (id: number) => {
  router.push(`/main/commission/edit-leader/${id}`)
}

// 获取佣金统计数据
const fetchCommissionStats = () => {
  loading.value = true
  // 实际项目中这里会调用API获取数据
  setTimeout(() => {
    // 模拟请求延迟
    loading.value = false
  }, 500)
}

onMounted(() => {
  // 设置默认日期范围为最近一个月
  const end = new Date()
  const start = new Date()
  start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
  dateRange.value = [start, end]

  // 获取数据
  fetchCommissionStats()
})
</script>

<style scoped lang="scss">
.commission-statistics-container {
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #eef2f5 100%);
  border-radius: 12px;
  min-height: calc(100vh - 40px);

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    .header-left {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        margin: 0 0 8px 0;
        background: linear-gradient(120deg, #3a7bd5, #2c5499);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .page-subtitle {
        font-size: 14px;
        color: #606266;
      }
    }

    .header-right {
      display: flex;
      gap: 16px;
    }
  }

  .stats-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    margin-bottom: 20px;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .stats-info {
      text-align: center;
      padding: 10px;

      .stats-label {
        font-size: 14px;
        color: #606266;
        margin-bottom: 10px;
      }

      .stats-value {
        font-size: 24px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 10px;
      }

      .stats-trend {
        display: inline-flex;
        align-items: center;
        font-size: 14px;
        padding: 2px 8px;
        border-radius: 4px;

        &.up {
          color: #67c23a;
          background-color: rgba(103, 194, 58, 0.1);
        }

        &.down {
          color: #f56c6c;
          background-color: rgba(245, 108, 108, 0.1);
        }
      }
    }
  }

  .chart-card,
  .ranking-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    margin-bottom: 20px;

    &:hover {
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 16px;
      font-weight: 600;

      .header-actions {
        display: flex;
        gap: 16px;
      }
    }
  }

  .chart-container {
    width: 100%;

    .chart-placeholder {
      width: 100%;
      height: 100%;
      position: relative;
      padding: 20px;

      .chart-line {
        position: absolute;
        top: 50%;
        left: 0;
        width: 100%;
        height: 2px;
        background: linear-gradient(
          90deg,
          rgba(64, 158, 255, 0.5),
          rgba(64, 158, 255, 1),
          rgba(103, 194, 58, 1)
        );

        &::before {
          content: '';
          position: absolute;
          top: -100px;
          left: 0;
          width: 100%;
          height: 100px;
          background: linear-gradient(to top, rgba(64, 158, 255, 0.1), transparent);
        }
      }

      .chart-labels {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        display: flex;
        justify-content: space-between;
        padding: 0 5%;
      }
    }
  }

  .flex-container {
    display: flex;
    padding: 20px;

    .pie-chart-container {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;

      .pie-chart {
        width: 200px;
        height: 200px;
        border-radius: 50%;
        background: conic-gradient(#409eff 0% 68.8%, #67c23a 68.8% 92.1%, #e6a23c 92.1% 100%);
        position: relative;

        &::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 100px;
          height: 100px;
          border-radius: 50%;
          background: white;
        }

        &.leader-chart {
          background: conic-gradient(#409eff 0% 61.5%, #67c23a 61.5% 100%);
        }
      }
    }

    .chart-legend {
      flex: 1;
      padding: 0 20px;

      .legend-item {
        display: flex;
        align-items: center;
        margin-bottom: 16px;

        .legend-color {
          width: 16px;
          height: 16px;
          border-radius: 4px;
          margin-right: 12px;
        }

        .legend-info {
          .legend-name {
            font-size: 14px;
            color: #303133;
            margin-bottom: 4px;
          }

          .legend-value {
            font-size: 14px;
            color: #606266;
          }
        }
      }
    }
  }

  .amount {
    font-weight: bold;
    color: #ff6b00;
  }
}

@media screen and (max-width: 768px) {
  .commission-statistics-container {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-right {
        width: 100%;
        flex-direction: column;
      }
    }

    .flex-container {
      flex-direction: column;

      .pie-chart-container {
        margin-bottom: 20px;
      }
    }
  }
}
</style>
