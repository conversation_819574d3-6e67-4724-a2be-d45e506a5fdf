<template>
  <div class="courier-select-fix">
    <el-card>
      <template #header>
        <h3>快递公司选择修复测试</h3>
      </template>

      <div class="test-section">
        <h4>后端返回的原始数据结构：</h4>
        <pre>{{ JSON.stringify(rawCourierData, null, 2) }}</pre>
      </div>

      <div class="test-section">
        <h4>快递公司列表：</h4>
        <div v-if="loading">加载中...</div>
        <div v-else-if="courierList.length === 0">暂无数据</div>
        <div v-else>
          <div v-for="courier in courierList" :key="courier.code" class="courier-item">
            <strong>{{ courier.name }}</strong> ({{ courier.code }})
            <br>
            <small>国家: {{ courier.country }} | 状态: {{ courier.active ? '启用' : '禁用' }}</small>
          </div>
        </div>
      </div>

      <div class="test-section">
        <h4>下拉选择测试：</h4>
        <el-form :model="testForm" label-width="120px">
          <el-form-item label="选择快递公司">
            <el-select
              v-model="testForm.selectedCourier"
              placeholder="请选择快递公司"
              filterable
              clearable
              style="width: 300px"
              @change="handleCourierChange"
            >
              <el-option
                v-for="courier in courierList"
                :key="courier.code"
                :label="`${courier.name} (${courier.country})`"
                :value="courier.code"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="选中的值">
            <el-input v-model="testForm.selectedCourier" readonly />
          </el-form-item>
          
          <el-form-item label="选中的名称">
            <el-input v-model="testForm.selectedName" readonly />
          </el-form-item>
        </el-form>
      </div>

      <div class="test-section">
        <h4>发货表单测试：</h4>
        <el-form :model="shipForm" label-width="120px">
          <el-form-item label="物流公司">
            <el-select
              v-model="shipForm.logisticsCompany"
              placeholder="请选择物流公司"
              filterable
              clearable
              style="width: 300px"
              @change="handleShipCourierChange"
            >
              <el-option
                v-for="courier in courierList"
                :key="courier.code"
                :label="`${courier.name} (${courier.country})`"
                :value="courier.code"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="物流单号">
            <el-input v-model="shipForm.logisticsNumber" placeholder="请输入物流单号" />
          </el-form-item>
          
          <el-form-item label="备注">
            <el-input v-model="shipForm.remark" placeholder="可选" />
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="handleTestShip">测试发货</el-button>
          </el-form-item>
        </el-form>
      </div>

      <div class="test-section">
        <h4>测试结果：</h4>
        <pre>{{ JSON.stringify(testResult, null, 2) }}</pre>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getCourierList, shipOrder } from '@/api/order'
import type { Courier, ShipOrderDTO } from '@/types/order'

// 数据状态
const loading = ref(false)
const courierList = ref<Courier[]>([])
const rawCourierData = ref<any>(null)

// 测试表单
const testForm = reactive({
  selectedCourier: '',
  selectedName: ''
})

// 发货表单
const shipForm = reactive<ShipOrderDTO>({
  orderId: 1,
  logisticsCompany: '',
  logisticsNumber: '',
  remark: ''
})

// 测试结果
const testResult = ref<any>({})

// 获取快递公司列表
const fetchCourierList = async () => {
  loading.value = true
  try {
    const response = await getCourierList()
    console.log('API响应:', response)
    
    // 保存原始数据用于调试
    rawCourierData.value = response
    
    if (response.code === 1) {
      courierList.value = response.data || []
      ElMessage.success(`成功获取 ${courierList.value.length} 个快递公司`)
    } else {
      ElMessage.error(response.msg || '获取快递公司列表失败')
    }
  } catch (error) {
    console.error('获取快递公司列表失败:', error)
    ElMessage.error('获取快递公司列表失败')
  } finally {
    loading.value = false
  }
}

// 快递公司选择变化
const handleCourierChange = (courierCode: string) => {
  console.log('选择的快递公司代码:', courierCode)
  
  const selectedCourier = courierList.value.find(c => c.code === courierCode)
  if (selectedCourier) {
    testForm.selectedName = selectedCourier.name
    console.log('选择的快递公司:', selectedCourier)
    ElMessage.success(`已选择: ${selectedCourier.name}`)
  } else {
    testForm.selectedName = ''
    console.log('未找到对应的快递公司')
  }
}

// 发货快递公司选择变化
const handleShipCourierChange = (courierCode: string) => {
  console.log('发货选择的快递公司代码:', courierCode)
  
  const selectedCourier = courierList.value.find(c => c.code === courierCode)
  if (selectedCourier) {
    // 这里我们直接使用快递公司的名称
    shipForm.logisticsCompany = selectedCourier.name
    console.log('发货快递公司设置为:', selectedCourier.name)
  }
}

// 测试发货
const handleTestShip = async () => {
  if (!shipForm.logisticsCompany || !shipForm.logisticsNumber) {
    ElMessage.warning('请填写必要信息')
    return
  }

  try {
    console.log('发货数据:', shipForm)
    
    // 这里只是模拟，不实际调用API
    testResult.value = {
      action: '模拟发货',
      data: { ...shipForm },
      timestamp: new Date().toISOString()
    }
    
    ElMessage.success('发货数据准备完成（仅模拟）')
  } catch (error) {
    console.error('发货测试失败:', error)
    ElMessage.error('发货测试失败')
  }
}

// 组件挂载
onMounted(() => {
  fetchCourierList()
})
</script>

<style scoped lang="scss">
.courier-select-fix {
  padding: 20px;

  .test-section {
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;

    h4 {
      margin: 0 0 15px 0;
      color: #303133;
      border-bottom: 2px solid #409eff;
      padding-bottom: 8px;
    }

    pre {
      background: white;
      padding: 15px;
      border-radius: 4px;
      border: 1px solid #dcdfe6;
      font-size: 12px;
      line-height: 1.4;
      max-height: 300px;
      overflow-y: auto;
    }

    .courier-item {
      padding: 10px;
      margin-bottom: 10px;
      background: white;
      border-radius: 4px;
      border: 1px solid #dcdfe6;

      strong {
        color: #303133;
      }

      small {
        color: #909399;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .courier-select-fix {
    padding: 10px;

    .test-section {
      padding: 15px;
    }
  }
}
</style>
