<template>
  <div class="add-leader-container">
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">添加团长</h1>
        <div class="page-subtitle">添加新的销售团长，设置佣金比例并生成邀请码</div>
      </div>
      <div class="header-right">
        <el-button @click="goBack">
          <el-icon><Back /></el-icon>返回列表
        </el-button>
      </div>
    </div>

    <el-card shadow="hover" class="form-card">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-position="top"
        status-icon
        require-asterisk-position="end"
      >
        <el-row :gutter="20">
          <el-col :xs="24" :sm="12">
            <el-form-item label="团长类型" prop="type">
              <el-radio-group v-model="form.type">
                <el-radio-button label="merchant">商家招募团长</el-radio-button>
                <el-radio-button label="user">用户招募团长</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :xs="24" :sm="12">
            <el-form-item label="团长姓名" prop="name">
              <el-input v-model="form.name" placeholder="请输入团长姓名" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="form.phone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :xs="24" :sm="12">
            <el-form-item label="性别" prop="gender">
              <el-select v-model="form.gender" placeholder="请选择性别" style="width: 100%">
                <el-option label="男" value="male" />
                <el-option label="女" value="female" />
                <el-option label="保密" value="secret" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12">
            <el-form-item label="身份证号" prop="idCard">
              <el-input v-model="form.idCard" placeholder="请输入身份证号" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :xs="24" :sm="12">
            <el-form-item label="电子邮箱" prop="email">
              <el-input v-model="form.email" placeholder="请输入电子邮箱" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12">
            <el-form-item label="银行卡号" prop="bankAccount">
              <el-input v-model="form.bankAccount" placeholder="请输入佣金结算银行卡号" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider>佣金设置</el-divider>

        <el-row :gutter="20">
          <el-col :xs="24" :sm="12">
            <el-form-item :label="`${typeLabel}佣金比例 (%)`" prop="commissionRate">
              <el-input-number
                v-model="form.commissionRate"
                :min="0"
                :max="20"
                :precision="1"
                :step="0.5"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12">
            <el-form-item label="佣金结算周期" prop="settlementCycle">
              <el-select
                v-model="form.settlementCycle"
                placeholder="请选择结算周期"
                style="width: 100%"
              >
                <el-option label="每周结算" value="weekly" />
                <el-option label="每月结算" value="monthly" />
                <el-option label="每季度结算" value="quarterly" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="佣金说明" prop="commissionDesc">
              <el-input
                v-model="form.commissionDesc"
                type="textarea"
                :rows="3"
                placeholder="请输入佣金说明，将展示给团长"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider>邀请码设置</el-divider>

        <el-row :gutter="20">
          <el-col :xs="24" :sm="8">
            <el-form-item label="邀请码前缀" prop="codePrefix">
              <el-input
                v-model="form.codePrefix"
                placeholder="邀请码前缀"
                :disabled="form.autoGenerateCode"
              >
                <template #append>
                  <el-tooltip content="商家招募团长默认MRCNT，用户招募团长默认USER">
                    <el-icon><QuestionFilled /></el-icon>
                  </el-tooltip>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8">
            <el-form-item label="邀请码" prop="invitationCode">
              <el-input v-model="form.invitationCode" placeholder="自动生成的邀请码" disabled>
                <template #append>
                  <el-button @click="regenerateCode" :disabled="form.autoGenerateCode">
                    <el-icon><RefreshRight /></el-icon>
                  </el-button>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8">
            <el-form-item label=" ">
              <el-checkbox v-model="form.autoGenerateCode" @change="handleAutoGenerateChange">
                自动生成邀请码
              </el-checkbox>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="form.remark"
                type="textarea"
                :rows="3"
                placeholder="请输入备注信息（选填）"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item>
          <el-button type="primary" @click="submitForm" :loading="submitting">保存</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { Back, RefreshRight, QuestionFilled } from '@element-plus/icons-vue'

const router = useRouter()
const formRef = ref<FormInstance>()
const submitting = ref(false)

// 表单数据
const form = reactive({
  type: 'merchant',
  name: '',
  phone: '',
  gender: 'male',
  idCard: '',
  email: '',
  bankAccount: '',
  commissionRate: 5.0,
  settlementCycle: 'monthly',
  commissionDesc: '佣金将按照实际成交订单金额计算，每月结算一次，系统自动结算至您的银行账户。',
  codePrefix: 'MRCNT',
  invitationCode: '',
  autoGenerateCode: true,
  remark: '',
})

// 表单验证规则
const rules = reactive<FormRules>({
  type: [{ required: true, message: '请选择团长类型', trigger: 'change' }],
  name: [{ required: true, message: '请输入团长姓名', trigger: 'blur' }],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' },
  ],
  idCard: [
    {
      pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
      message: '请输入正确的身份证号',
      trigger: 'blur',
    },
  ],
  email: [
    {
      pattern: /^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/,
      message: '请输入正确的邮箱地址',
      trigger: 'blur',
    },
  ],
  commissionRate: [{ required: true, message: '请设置佣金比例', trigger: 'blur' }],
  settlementCycle: [{ required: true, message: '请选择结算周期', trigger: 'change' }],
})

// 计算属性：团长类型标签
const typeLabel = computed(() => {
  return form.type === 'merchant' ? '商家招募' : '用户招募'
})

// 监听团长类型变化，更新邀请码前缀
watch(
  () => form.type,
  (newType) => {
    if (form.autoGenerateCode) {
      form.codePrefix = newType === 'merchant' ? 'MRCNT' : 'USER'
      generateInvitationCode()
    }
  },
)

// 处理自动生成邀请码选项变化
const handleAutoGenerateChange = (val: boolean) => {
  if (val) {
    form.codePrefix = form.type === 'merchant' ? 'MRCNT' : 'USER'
    generateInvitationCode()
  }
}

// 生成邀请码
const generateInvitationCode = () => {
  const prefix = form.codePrefix
  const randomDigits = Math.floor(Math.random() * 10000)
    .toString()
    .padStart(4, '0')
  form.invitationCode = `${prefix}${randomDigits}`
}

// 重新生成邀请码
const regenerateCode = () => {
  generateInvitationCode()
}

// 返回列表页
const goBack = () => {
  router.push('/main/commission/leader-list')
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return

  await formRef.value.validate((valid, fields) => {
    if (valid) {
      submitting.value = true

      // 格式化提交数据
      const submitData = {
        ...form,
        type: form.type === 'merchant' ? '商家招募' : '用户招募',
        gender: form.gender === 'male' ? '男' : form.gender === 'female' ? '女' : '保密',
      }

      // 这里应该调用API保存数据
      console.log('提交表单数据', submitData)

      // 模拟API请求
      setTimeout(() => {
        submitting.value = false
        ElMessage.success('团长添加成功！')
        router.push('/main/commission/leader-list')
      }, 1000)
    } else {
      console.error('表单验证失败', fields)
    }
  })
}

// 重置表单
const resetForm = () => {
  if (!formRef.value) return
  formRef.value.resetFields()
  generateInvitationCode()
}

// 初始化生成邀请码
generateInvitationCode()
</script>

<style scoped lang="scss">
.add-leader-container {
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #eef2f5 100%);
  border-radius: 12px;
  min-height: calc(100vh - 40px);

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    .header-left {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        margin: 0 0 8px 0;
        background: linear-gradient(120deg, #3a7bd5, #2c5499);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .page-subtitle {
        font-size: 14px;
        color: #606266;
      }
    }

    .header-right {
      display: flex;
      gap: 16px;
    }
  }

  .form-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    padding: 24px;

    &:hover {
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }
  }

  :deep(.el-form-item__label) {
    font-weight: 600;
  }

  :deep(.el-divider__text) {
    font-weight: 600;
    color: #409eff;
    font-size: 16px;
  }
}

@media screen and (max-width: 768px) {
  .add-leader-container {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-right {
        width: 100%;
      }
    }
  }
}
</style>
