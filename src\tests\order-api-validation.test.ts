/**
 * 订单API接口验证测试
 * 验证前端API接口与后端接口的一致性
 */

import { describe, it, expect, vi } from 'vitest'
import {
  // 查询相关
  getOrderList,
  getOrderById,
  getOrderByNumber,
  getOrdersByBuyerId,
  getOrderHistory,
  
  // 状态管理
  shipOrder,
  getCourierList,
  completeOrder,
  cancelOrder,
  
  // 批量操作
  batchShip,
  batchCancel,
  batchComplete,
  batchExport,
  
  // 编辑和退款
  editOrder,
  refundOrder,
  
  // 日志和操作
  getOrderLogs,
  getOrderActions,
  
  // 统计和报表
  getOrderStatistics,
  getOrderOverview,
  exportOrders
} from '@/api/order'

// Mock request module
vi.mock('@/utils/request', () => ({
  default: {
    get: vi.fn(),
    put: vi.fn(),
    post: vi.fn()
  }
}))

import request from '@/utils/request'

describe('订单API接口验证', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('订单查询相关接口', () => {
    it('订单分页查询 - /admin/order/conditionSearch', () => {
      const params = { page: 1, pageSize: 20 }
      getOrderList(params)
      expect(request.get).toHaveBeenCalledWith('/admin/order/conditionSearch', { params })
    })

    it('获取订单详情 - /admin/order/details/{id}', () => {
      getOrderById(123)
      expect(request.get).toHaveBeenCalledWith('/admin/order/details/123')
    })

    it('根据订单号查询订单 - /admin/order/number/{number}', () => {
      getOrderByNumber('ORDER123')
      expect(request.get).toHaveBeenCalledWith('/admin/order/number/ORDER123')
    })

    it('根据买家ID查询订单列表 - /admin/order/buyer/{buyerId}', () => {
      getOrdersByBuyerId(456)
      expect(request.get).toHaveBeenCalledWith('/admin/order/buyer/456')
    })

    it('查询历史订单 - /admin/order/history', () => {
      getOrderHistory(789)
      expect(request.get).toHaveBeenCalledWith('/admin/order/history', {
        params: { buyerId: 789 }
      })
    })
  })

  describe('订单状态管理接口', () => {
    it('发货 - /admin/order/delivery', () => {
      const shipData = {
        orderId: 123,
        trackingNumber: 'TN123456',
        courierCode: 'SF',
        courierName: '顺丰速运',
        shipNote: '测试发货'
      }
      shipOrder(shipData)
      expect(request.put).toHaveBeenCalledWith('/admin/order/delivery', shipData)
    })

    it('获取快递公司列表 - /admin/order/couriers', () => {
      getCourierList()
      expect(request.get).toHaveBeenCalledWith('/admin/order/couriers')
    })

    it('完成订单 - /admin/order/complete/{id}', () => {
      completeOrder(123)
      expect(request.put).toHaveBeenCalledWith('/admin/order/complete/123')
    })

    it('取消订单 - /admin/order/cancel', () => {
      const cancelData = { id: 123, cancelReason: '用户取消' }
      cancelOrder(cancelData)
      expect(request.put).toHaveBeenCalledWith('/admin/order/cancel', cancelData)
    })
  })

  describe('批量操作接口', () => {
    it('批量发货 - /admin/order/batch/ship', () => {
      const batchData = {
        orderIds: [1, 2, 3],
        logisticsCompany: '顺丰速运',
        trackingNumber: 'SF123456',
        remark: '批量发货'
      }
      batchShip(batchData)
      expect(request.put).toHaveBeenCalledWith('/admin/order/batch/ship', batchData)
    })

    it('批量取消 - /admin/order/batch/cancel', () => {
      const batchData = {
        orderIds: [1, 2, 3],
        reason: '批量取消原因'
      }
      batchCancel(batchData)
      expect(request.put).toHaveBeenCalledWith('/admin/order/batch/cancel', batchData)
    })

    it('批量完成 - /admin/order/batch/complete', () => {
      const batchData = {
        orderIds: [1, 2, 3]
      }
      batchComplete(batchData)
      expect(request.put).toHaveBeenCalledWith('/admin/order/batch/complete', batchData)
    })

    it('批量导出 - /admin/order/batch/export', () => {
      const batchData = {
        orderIds: [1, 2, 3],
        exportFormat: 'excel',
        exportFields: ['number', 'amount', 'status']
      }
      batchExport(batchData)
      expect(request.post).toHaveBeenCalledWith('/admin/order/batch/export', batchData)
    })
  })

  describe('订单编辑和退款接口', () => {
    it('编辑订单 - /admin/order/edit/{id}', () => {
      const editData = {
        addressInfo: '新地址',
        remark: '新备注',
        editReason: '编辑原因'
      }
      editOrder(123, editData)
      expect(request.put).toHaveBeenCalledWith('/admin/order/edit/123', null, {
        params: {
          addressInfo: editData.addressInfo,
          remark: editData.remark,
          editReason: editData.editReason
        }
      })
    })

    it('退款处理 - /admin/order/refund/{id}', () => {
      const refundData = {
        refundReason: '退款原因',
        refundAmount: '100.00'
      }
      refundOrder(123, refundData)
      expect(request.put).toHaveBeenCalledWith('/admin/order/refund/123', null, {
        params: {
          refundReason: refundData.refundReason,
          refundAmount: refundData.refundAmount
        }
      })
    })
  })

  describe('订单日志和操作记录接口', () => {
    it('获取订单操作日志 - /admin/order/logs/{id}', () => {
      getOrderLogs(123)
      expect(request.get).toHaveBeenCalledWith('/admin/order/logs/123')
    })

    it('获取订单可执行操作 - /admin/order/actions/{id}', () => {
      getOrderActions(123)
      expect(request.get).toHaveBeenCalledWith('/admin/order/actions/123')
    })
  })

  describe('统计和报表接口', () => {
    it('获取订单统计 - /admin/order/statistics', () => {
      getOrderStatistics()
      expect(request.get).toHaveBeenCalledWith('/admin/order/statistics')
    })

    it('获取订单概览 - /admin/order/overview', () => {
      getOrderOverview()
      expect(request.get).toHaveBeenCalledWith('/admin/order/overview')
    })

    it('导出订单 - /admin/order/export', () => {
      const orderIds = [1, 2, 3]
      const format = 'excel'
      exportOrders(orderIds, format)
      expect(request.post).toHaveBeenCalledWith('/admin/order/export', null, {
        params: {
          orderIds,
          format
        }
      })
    })
  })
})
