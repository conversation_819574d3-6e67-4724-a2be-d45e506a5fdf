<template>
  <el-dialog
    v-model="visible"
    title="退款详情"
    width="1000px"
    :before-close="handleClose"
    class="refund-detail-dialog"
  >
    <div v-loading="loading" class="refund-detail-content">
      <div v-if="refundDetail" class="refund-info">
        <!-- 基本信息 -->
        <div class="info-section">
          <h3>基本信息</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">退款单号：</span>
                <span class="value">{{ refundDetail.refundNo }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">订单号：</span>
                <span class="value">{{ refundDetail.orderNumber }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">申请人：</span>
                <span class="value">{{ refundDetail.buyerName || '未知' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">申请时间：</span>
                <span class="value">{{ refundDetail.createTime }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">退款类型：</span>
                <el-tag :type="refundDetail.refundType === 1 ? 'primary' : 'warning'" size="small">
                  {{ getRefundTypeText(refundDetail.refundType) }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">申请状态：</span>
                <el-tag :type="getRefundStatusType(refundDetail.applicationStatus)" size="small">
                  {{ getRefundStatusText(refundDetail.applicationStatus) }}
                </el-tag>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 金额信息 -->
        <div class="info-section">
          <h3>金额信息</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">订单金额：</span>
                <span class="value amount">¥{{ refundDetail.orderAmount.toFixed(2) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">申请退款金额：</span>
                <span class="value amount refund">¥{{ refundDetail.refundAmount.toFixed(2) }}</span>
              </div>
            </el-col>
            <el-col :span="8" v-if="refundDetail.actualRefundAmount">
              <div class="info-item">
                <span class="label">实际退款金额：</span>
                <span class="value amount actual">¥{{ refundDetail.actualRefundAmount.toFixed(2) }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 退款原因 -->
        <div class="info-section">
          <h3>退款原因</h3>
          <div class="reason-content">
            {{ refundDetail.refundReason || '无' }}
          </div>
        </div>

        <!-- 审核信息 -->
        <div class="info-section" v-if="refundDetail.needApproval === 1">
          <h3>审核信息</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">审核状态：</span>
                <el-tag
                  :type="refundDetail.approvalStatus === 1 ? 'warning' : refundDetail.approvalStatus === 2 ? 'success' : 'danger'"
                  size="small"
                >
                  {{ getApprovalStatusText(refundDetail.approvalStatus) }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="8" v-if="refundDetail.approverName">
              <div class="info-item">
                <span class="label">审核人：</span>
                <span class="value">{{ refundDetail.approverName }}</span>
              </div>
            </el-col>
            <el-col :span="8" v-if="refundDetail.approvalTime">
              <div class="info-item">
                <span class="label">审核时间：</span>
                <span class="value">{{ refundDetail.approvalTime }}</span>
              </div>
            </el-col>
            <el-col :span="24" v-if="refundDetail.approvalRemark">
              <div class="info-item">
                <span class="label">审核备注：</span>
                <div class="remark-content">{{ refundDetail.approvalRemark }}</div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 退款方式信息 -->
        <div class="info-section" v-if="refundDetail.refundMethod">
          <h3>退款方式</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">退款方式：</span>
                <span class="value">{{ getRefundMethodText(refundDetail.refundMethod) }}</span>
              </div>
            </el-col>
            <el-col :span="8" v-if="refundDetail.refundTime">
              <div class="info-item">
                <span class="label">退款时间：</span>
                <span class="value">{{ refundDetail.refundTime }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 订单信息 -->
        <div class="info-section" v-if="refundDetail.orderInfo">
          <h3>关联订单信息</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">订单状态：</span>
                <el-tag :type="getOrderStatusType(refundDetail.orderInfo.status)" size="small">
                  {{ getOrderStatusText(refundDetail.orderInfo.status) }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">下单时间：</span>
                <span class="value">{{ refundDetail.orderInfo.orderTime }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">支付方式：</span>
                <span class="value">{{ getPayMethodText(refundDetail.orderInfo.payMethod) }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 审核记录 -->
        <div class="info-section" v-if="refundDetail.approvalRecords && refundDetail.approvalRecords.length > 0">
          <h3>审核记录</h3>
          <el-timeline>
            <el-timeline-item
              v-for="record in refundDetail.approvalRecords"
              :key="record.id"
              :timestamp="record.createTime"
              placement="top"
            >
              <div class="timeline-content">
                <div class="timeline-header">
                  <span class="approver">{{ record.approverName }}</span>
                  <el-tag
                    :type="record.approvalStatus === 2 ? 'success' : 'danger'"
                    size="small"
                  >
                    {{ getApprovalStatusText(record.approvalStatus) }}
                  </el-tag>
                </div>
                <div class="timeline-remark" v-if="record.approvalRemark">
                  {{ record.approvalRemark }}
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          v-if="refundDetail && canApprove(refundDetail)"
          type="success"
          @click="handleApprove"
        >
          审核
        </el-button>
        <el-button
          v-if="refundDetail && canProcessRefund(refundDetail)"
          type="primary"
          @click="handleProcess"
        >
          处理退款
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getRefundById, processRefund } from '@/api/refund'
import type { RefundApplicationVO } from '@/types/refund'
import {
  getRefundStatusText,
  getRefundStatusType,
  getRefundTypeText,
  getApprovalStatusText,
  getRefundMethodText,
  canApprove,
  canProcessRefund
} from '@/types/refund'
import { getStatusText as getOrderStatusText, getStatusType as getOrderStatusType } from '@/utils/orderStatus'

// 支付方式文本映射
const getPayMethodText = (method: number): string => {
  const map: Record<number, string> = {
    1: '微信支付',
    2: '支付宝',
    3: '银行卡'
  }
  return map[method] || '未知'
}

interface Props {
  modelValue: boolean
  refundId: number | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'refresh'): void
  (e: 'approve', refund: RefundApplicationVO): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = ref(false)
const loading = ref(false)
const refundDetail = ref<RefundApplicationVO | null>(null)

// 监听显示状态
watch(
  () => props.modelValue,
  (newVal) => {
    visible.value = newVal
    if (newVal && props.refundId) {
      fetchRefundDetail()
    }
  }
)

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 获取退款详情
const fetchRefundDetail = async () => {
  if (!props.refundId) return

  loading.value = true
  try {
    const response = await getRefundById(props.refundId)
    if (response.code === 1) {
      refundDetail.value = response.data
    } else {
      ElMessage.error(response.msg || '获取退款详情失败')
    }
  } catch (error) {
    console.error('获取退款详情失败:', error)
    ElMessage.error('获取退款详情失败')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  refundDetail.value = null
}

// 审核
const handleApprove = () => {
  if (refundDetail.value) {
    emit('approve', refundDetail.value)
    handleClose()
  }
}

// 处理退款
const handleProcess = async () => {
  if (!refundDetail.value) return

  try {
    await ElMessageBox.confirm('确认处理该退款申请？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const response = await processRefund(refundDetail.value.id)
    if (response.code === 1) {
      ElMessage.success('退款处理成功')
      emit('refresh')
      handleClose()
    } else {
      ElMessage.error(response.msg || '退款处理失败')
    }
  } catch (error) {
    console.error('退款处理失败:', error)
  }
}
</script>

<style scoped lang="scss">
.refund-detail-dialog {
  .refund-detail-content {
    max-height: 70vh;
    overflow-y: auto;
  }

  .refund-info {
    .info-section {
      margin-bottom: 30px;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 8px;

      h3 {
        margin: 0 0 20px 0;
        padding-bottom: 10px;
        border-bottom: 2px solid #409eff;
        color: #303133;
        font-size: 16px;
        font-weight: 600;
      }

      .info-item {
        margin-bottom: 15px;
        display: flex;
        align-items: center;

        .label {
          font-weight: 500;
          color: #606266;
          min-width: 120px;
          margin-right: 10px;
        }

        .value {
          color: #303133;
          flex: 1;

          &.amount {
            font-weight: bold;
            font-size: 16px;

            &.refund {
              color: #f56c6c;
            }

            &.actual {
              color: #67c23a;
            }
          }
        }
      }

      .reason-content,
      .remark-content {
        background: white;
        padding: 15px;
        border-radius: 4px;
        border: 1px solid #dcdfe6;
        color: #606266;
        line-height: 1.6;
        min-height: 60px;
      }
    }

    .timeline-content {
      .timeline-header {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 8px;

        .approver {
          font-weight: 500;
          color: #303133;
        }
      }

      .timeline-remark {
        color: #606266;
        font-size: 14px;
        line-height: 1.5;
        background: #f5f7fa;
        padding: 10px;
        border-radius: 4px;
        border-left: 3px solid #409eff;
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .refund-detail-dialog {
    .refund-info {
      .info-section {
        padding: 15px;

        .info-item {
          flex-direction: column;
          align-items: flex-start;

          .label {
            min-width: auto;
            margin-bottom: 5px;
          }
        }
      }
    }
  }
}
</style>
