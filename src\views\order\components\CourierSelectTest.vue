<template>
  <div class="courier-select-test">
    <el-card>
      <template #header>
        <h3>快递公司选择测试</h3>
      </template>
      
      <div class="test-section">
        <h4>1. 获取快递公司列表</h4>
        <el-button @click="loadCouriers" :loading="loading">
          获取快递公司列表
        </el-button>
        
        <div v-if="courierList.length > 0" class="courier-info">
          <p><strong>获取到 {{ courierList.length }} 个快递公司</strong></p>
          <div class="courier-grid">
            <div v-for="courier in courierList" :key="courier.code" class="courier-item">
              <strong>{{ courier.name }}</strong><br>
              <small>代码: {{ courier.code }}</small><br>
              <small>国家: {{ courier.country }}</small>
            </div>
          </div>
        </div>
      </div>

      <div class="test-section">
        <h4>2. 快递公司选择测试</h4>
        <el-form label-width="120px">
          <el-form-item label="选择快递公司">
            <el-select 
              v-model="selectedCourierCode" 
              placeholder="请选择快递公司"
              filterable
              clearable
              style="width: 300px"
              @change="handleCourierChange"
            >
              <el-option
                v-for="courier in courierList"
                :key="courier.code"
                :label="`${courier.name} (${courier.country})`"
                :value="courier.code"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="选中的代码">
            <el-input v-model="selectedCourierCode" readonly style="width: 300px" />
          </el-form-item>
          
          <el-form-item label="选中的名称">
            <el-input v-model="selectedCourierName" readonly style="width: 300px" />
          </el-form-item>
        </el-form>
      </div>

      <div v-if="error" class="error-section">
        <h4>错误信息：</h4>
        <pre>{{ error }}</pre>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { getCourierList } from '@/api/order'
import type { Courier } from '@/types/order'

const loading = ref(false)
const courierList = ref<Courier[]>([])
const selectedCourierCode = ref('')
const selectedCourierName = ref('')
const error = ref('')

// 获取快递公司列表
const loadCouriers = async () => {
  loading.value = true
  error.value = ''
  
  try {
    console.log('开始获取快递公司列表...')
    const response = await getCourierList()
    console.log('API响应:', response)
    
    if (response.code === 1) {
      courierList.value = response.data || []
      ElMessage.success(`成功获取 ${courierList.value.length} 个快递公司`)
      console.log('快递公司列表:', courierList.value)
    } else {
      error.value = `API错误: ${response.msg || '未知错误'}`
      ElMessage.error(response.msg || '获取快递公司列表失败')
    }
  } catch (err: any) {
    error.value = `请求错误: ${err.message || err}`
    console.error('获取快递公司列表失败:', err)
    ElMessage.error('获取快递公司列表失败')
  } finally {
    loading.value = false
  }
}

// 快递公司选择变化
const handleCourierChange = (courierCode: string) => {
  console.log('选择的快递公司代码:', courierCode)

  const selectedCourier = courierList.value.find(c => c.code === courierCode)
  if (selectedCourier) {
    selectedCourierName.value = selectedCourier.name
    console.log('选择的快递公司:', selectedCourier)
    ElMessage.success(`已选择: ${selectedCourier.name}`)
  } else {
    selectedCourierName.value = ''
    console.log('未找到对应的快递公司')
  }
}
</script>

<style scoped lang="scss">
.courier-select-test {
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;

  h4 {
    margin-top: 0;
    color: #303133;
  }
}

.courier-info {
  margin-top: 15px;
  
  p {
    margin-bottom: 15px;
    color: #606266;
  }
}

.courier-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 10px;
  max-height: 300px;
  overflow-y: auto;
}

.courier-item {
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #f9f9f9;
  
  strong {
    color: #303133;
  }
  
  small {
    color: #909399;
  }
}

.error-section {
  margin-top: 20px;
  padding: 15px;
  background-color: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
  
  h4 {
    margin-top: 0;
    color: #f56c6c;
  }
  
  pre {
    margin: 0;
    color: #f56c6c;
    font-size: 12px;
    white-space: pre-wrap;
    word-break: break-all;
  }
}
</style>
