/**
 * 调试工具
 * 提供日志记录和API调试功能
 */

// 是否启用调试模式
const isDev = import.meta.env.MODE === 'development'

// 日志级别
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

// 当前日志级别
let currentLogLevel = isDev ? LogLevel.DEBUG : LogLevel.INFO

// 设置日志级别
const setLogLevel = (level: LogLevel) => {
  currentLogLevel = level
}

// 日志颜色
const logColors = {
  debug: 'color: #7f8c8d', // 灰色
  info: 'color: #2980b9', // 蓝色
  warn: 'color: #f39c12', // 橙色
  error: 'color: #c0392b', // 红色
  api: 'color: #8e44ad', // 紫色
  success: 'color: #27ae60', // 绿色
}

// 普通日志
const log = (message: string, ...data: any[]) => {
  if (currentLogLevel <= LogLevel.DEBUG) {
    console.log(`%c[DEBUG] ${message}`, logColors.debug, ...data)
  }
}

// 信息日志
const info = (message: string, ...data: any[]) => {
  if (currentLogLevel <= LogLevel.INFO) {
    console.log(`%c[INFO] ${message}`, logColors.info, ...data)
  }
}

// 警告日志
const warn = (message: string, ...data: any[]) => {
  if (currentLogLevel <= LogLevel.WARN) {
    console.warn(`%c[WARN] ${message}`, logColors.warn, ...data)
  }
}

// 错误日志
const error = (message: string, ...data: any[]) => {
  if (currentLogLevel <= LogLevel.ERROR) {
    console.error(`%c[ERROR] ${message}`, logColors.error, ...data)
  }
}

// API日志
const api = {
  request: (url: string, method: string, data: any) => {
    if (import.meta.env.DEV) {
      console.group(`🚀 API请求: ${method.toUpperCase()} ${url}`)
      if (data) {
        console.log('请求数据:', data)
      }
      console.groupEnd()
    }
  },

  response: (url: string, status: number, data: any) => {
    if (import.meta.env.DEV) {
      console.group(`✅ API响应: ${url}`)
      console.log('状态码:', status)
      console.log('响应数据:', data)
      console.groupEnd()
    }
  },

  error: (url: string, error: any) => {
    console.group(`❌ API错误: ${url}`)
    console.error('错误信息:', error)
    if (error.response) {
      console.error('响应状态:', error.response.status)
      console.error('响应数据:', error.response.data)
    }
    console.groupEnd()
  },
}

// 调试工具
const debug = {
  api: {
    // 请求调试
    request: (url: string, method: string, data: any) => {
      if (import.meta.env.DEV) {
        console.group(`🚀 API请求: ${method.toUpperCase()} ${url}`)
        if (data) {
          console.log('请求数据:', data)
        }
        console.groupEnd()
      }
    },

    // 响应调试
    response: (url: string, status: number, data: any) => {
      if (import.meta.env.DEV) {
        console.group(`✅ API响应: ${url}`)
        console.log('状态码:', status)
        console.log('响应数据:', data)
        console.groupEnd()
      }
    },

    // 错误调试
    error: (url: string, error: any) => {
      console.group(`❌ API错误: ${url}`)
      console.error('错误信息:', error)
      if (error.response) {
        console.error('响应状态:', error.response.status)
        console.error('响应数据:', error.response.data)
      }
      console.groupEnd()
    },
  },
}

// 导出调试工具
export default debug
