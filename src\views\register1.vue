<template>
  <div class="register-container">
    <div class="register-content">
      <div class="register-box">
        <div class="register-header">
          <h1 class="register-title">sharewharf <span>商城</span></h1>
          <p class="register-subtitle">欢迎注册，创建您的账户</p>
        </div>

        <div class="step-form-container">
          <div class="step-form">
            <el-form
              ref="formRef"
              :model="form"
              :rules="rules"
              label-position="top"
              @submit.prevent
            >
              <!-- 邮箱 -->
              <el-form-item label="邮箱" prop="email">
                <el-input
                  v-model="form.email"
                  placeholder="请输入您的邮箱"
                  :prefix-icon="Message"
                />
              </el-form-item>

              <!-- 账号名称 -->
              <el-form-item label="账号名称" prop="accountName">
                <el-input
                  v-model="form.accountName"
                  placeholder="请输入账号名称"
                  :prefix-icon="User"
                />
              </el-form-item>

              <!-- 性别 -->
              <el-form-item label="性别" prop="gender">
                <el-radio-group v-model="form.gender">
                  <el-radio label="Male">男</el-radio>
                  <el-radio label="Female">女</el-radio>
                </el-radio-group>
              </el-form-item>

              <!-- 验证码 -->
              <el-form-item label="验证码" prop="verifyCode">
                <div class="verify-code-container">
                  <el-input
                    v-model="form.verifyCode"
                    placeholder="验证码6位数字"
                    :prefix-icon="Key"
                  />
                  <el-button
                    type="primary"
                    class="verify-code-btn"
                    :disabled="countdown > 0"
                    @click="sendVerifyCode"
                  >
                    {{ countdown > 0 ? `${countdown}秒后重新获取` : '获取验证码' }}
                  </el-button>
                </div>
              </el-form-item>

              <!-- 密码 -->
              <el-form-item label="密码" prop="password">
                <el-input
                  v-model="form.password"
                  type="password"
                  placeholder="请设置密码"
                  :prefix-icon="Lock"
                  show-password
                />
              </el-form-item>

              <!-- 确认密码 -->
              <el-form-item label="确认密码" prop="confirmPassword">
                <el-input
                  v-model="form.confirmPassword"
                  type="password"
                  placeholder="请再次输入密码"
                  :prefix-icon="Lock"
                  show-password
                />
              </el-form-item>

              <!-- 协议提示 -->
              <div class="agreement-tip">
                <el-alert
                  title="您已同意《Sharewharf平台供应商协议》"
                  type="success"
                  :closable="false"
                  show-icon
                />
              </div>

              <!-- 下一步按钮 -->
              <el-form-item>
                <el-button type="primary" class="next-btn" @click="nextStep" :loading="loading">
                  下一步
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>

        <!-- 底部链接 -->
        <div class="register-footer">
          <span>已有账号？</span>
          <el-link type="primary" :underline="false" @click="router.push('/')">返回登录</el-link>
        </div>
      </div>

      <!-- 装饰元素 -->
      <div class="decoration-circles">
        <div class="circle circle-1"></div>
        <div class="circle circle-2"></div>
        <div class="circle circle-3"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { Message, Lock, Key, User } from '@element-plus/icons-vue'
import { sendVerificationCode } from '@/api/seller'

const router = useRouter()
const formRef = ref<FormInstance>()
const countdown = ref(0)
const loading = ref(false)
let timer: any = null

// 表单数据
const form = reactive({
  email: '',
  accountName: '',
  gender: 'Unknown',
  verifyCode: '',
  password: '',
  confirmPassword: '',
})

// 添加一个标记，表示验证码是否已发送
const isCodeSent = ref(true)

// 验证规则
const validateAccountName = (rule: any, value: string, callback: any) => {
  if (value === '') {
    callback(new Error('请输入账号名称'))
  } else if (value.length < 2) {
    callback(new Error('账号名称长度不能小于2个字符'))
  } else if (value.length > 20) {
    callback(new Error('账号名称长度不能超过20个字符'))
  } else if (/^\d+$/.test(value)) {
    callback(new Error('账号名称不能只包含数字'))
  } else {
    callback()
  }
}

// 验证密码
const validatePass = (rule: any, value: string, callback: any) => {
  // 更新后的密码规则：必须包含大小写字母和数字，特殊字符可选
  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[\w\W]{6,20}$/
  if (value === '') {
    callback(new Error('请输入密码'))
  } else if (!passwordRegex.test(value)) {
    callback(new Error('密码必须包含大小写字母和数字，长度在6-20位之间'))
  } else if (form.confirmPassword !== '') {
    if (!formRef.value) return
    formRef.value.validateField('confirmPassword')
  }
  callback()
}

// 验证确认密码
const validatePass2 = (rule: any, value: string, callback: any) => {
  if (value === '') {
    callback(new Error('请再次输入密码'))
  } else if (value !== form.password) {
    callback(new Error('两次输入密码不一致'))
  } else {
    callback()
  }
}

const validateEmail = (rule: any, value: string, callback: any) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (value === '') {
    callback(new Error('请输入邮箱'))
  } else if (!emailRegex.test(value)) {
    callback(new Error('请输入有效的邮箱地址'))
  } else {
    callback()
  }
}

// 验证验证码：只在有输入时进行验证
const validateVerifyCode = (rule: any, value: string, callback: any) => {
  if (value === '') {
    // 空值允许通过
    callback()
  } else if (!/^\d{6}$/.test(value)) {
    callback(new Error('验证码必须为6位数字'))
  } else {
    callback()
  }
}

const rules = reactive<FormRules>({
  email: [{ required: true, validator: validateEmail, trigger: 'blur' }],
  accountName: [{ required: true, validator: validateAccountName, trigger: 'blur' }],
  gender: [{ required: true, message: '请选择性别', trigger: 'change' }],
  verifyCode: [{ validator: validateVerifyCode, trigger: 'blur' }],
  password: [{ required: true, validator: validatePass, trigger: 'blur' }],
  confirmPassword: [{ required: true, validator: validatePass2, trigger: 'blur' }],
})

// 发送验证码
const sendVerifyCode = async () => {
  if (!form.email) {
    ElMessage.warning('请先输入邮箱')
    return
  }

  // 验证邮箱格式
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(form.email)) {
    ElMessage.warning('请输入有效的邮箱地址')
    return
  }

  try {
    const response = await sendVerificationCode(form.email)
    console.log('验证码发送响应', response)

    if (response.code === 1) {
      // 成功状态码
      ElMessage.success('验证码已发送，请查收邮箱')
      countdown.value = 60

      // 倒计时
      timer = setInterval(() => {
        countdown.value--
        if (countdown.value <= 0) {
          clearInterval(timer)
        }
      }, 1000)
    } else {
      ElMessage.error(response.msg || '验证码发送失败')
    }
  } catch (error: any) {
    console.error('发送验证码失败:', error)
    ElMessage.error(error.response?.data?.msg || '验证码发送失败，请稍后重试')
    if (timer) {
      clearInterval(timer)
      countdown.value = 0
    }
  }
}

// 下一步
const nextStep = async () => {
  router.push('/register/step2')

  if (!formRef.value) return

  await formRef.value.validate((valid, fields) => {
    if (valid) {
      loading.value = true

      // 保存账号信息到本地存储
      localStorage.setItem(
        'registerAccountInfo',
        JSON.stringify({
          email: form.email,
          accountName: form.accountName,
          gender: form.gender,
          verifyCode: form.verifyCode,
          password: form.password,
        }),
      )

      // 跳转到注册第二步页面
      setTimeout(() => {
        loading.value = false
        router.push('/register/step2')
      }, 500)
    } else {
      console.log('验证失败', fields)
    }
  })
}

// 清除定时器
onMounted(() => {
  // 尝试从本地存储恢复数据
  const savedData = localStorage.getItem('registerAccountInfo')
  if (savedData) {
    try {
      const parsedData = JSON.parse(savedData)
      form.email = parsedData.email || ''
      form.accountName = parsedData.accountName || ''
      form.gender = parsedData.gender || 'Unknown'
      form.verifyCode = parsedData.verifyCode || ''
      form.password = parsedData.password || ''
      form.confirmPassword = parsedData.password || ''
    } catch (e) {
      console.error('解析保存的注册数据失败', e)
    }
  }

  return () => {
    if (timer) {
      clearInterval(timer)
    }
  }
})

// 在组件卸载时清理
onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }
})
</script>

<style scoped lang="scss">
.register-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #eef2f7 100%);
  padding: 0;
  margin: 0;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  overflow: hidden;
}

.register-content {
  position: relative;
  width: 100%;
  max-width: 1200px;
  display: flex;
  justify-content: center;
}

.register-box {
  width: 600px;
  padding: 50px 80px;
  background: white;
  border-radius: 20px;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.08);
  position: relative;
  z-index: 10;
  animation: fadeIn 0.8s ease-out;
  max-height: 90vh; // 最大高度为视口高度的90%
  display: flex;
  flex-direction: column;

  .register-header {
    margin-bottom: 40px;
    text-align: center;
  }

  .register-title {
    font-size: 36px;
    font-weight: 600;
    color: #333;
    margin-bottom: 12px;
    background: linear-gradient(90deg, var(--el-color-primary), #409eff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;

    span {
      font-weight: 400;
      font-size: 32px;
    }
  }

  .register-subtitle {
    font-size: 16px;
    color: #909399;
    margin-top: 10px;
    margin-bottom: 25px;
  }

  .register-footer {
    margin-top: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;

    span {
      color: #909399;
      font-size: 15px;
    }

    .el-link {
      font-size: 15px;
      transition: all 0.3s;

      &:hover {
        transform: translateY(-2px);
      }
    }
  }
}

.step-form-container {
  flex: 1;
  overflow-y: auto; // 添加垂直滚动条
  margin: 0 -10px; // 为滚动条预留空间
  padding: 0 10px; // 内容不受影响

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
  }

  &::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 10px;

    &:hover {
      background: #aaa;
    }
  }
}

.step-form {
  width: 100%;
  animation: fadeIn 0.5s ease-in-out;

  :deep(.el-form-item__label) {
    font-size: 15px;
    font-weight: 500;
    color: #333;
    padding-bottom: 8px;
  }

  :deep(.el-input__wrapper) {
    padding: 0 15px;
    height: 48px;
    box-shadow: 0 0 0 1px #dcdfe6;
    transition: all 0.3s;

    &:hover {
      box-shadow: 0 0 0 1px var(--el-color-primary);
    }

    &.is-focus {
      box-shadow: 0 0 0 1px var(--el-color-primary);
    }
  }

  :deep(.el-input__inner) {
    font-size: 15px;
  }

  :deep(.el-input__icon) {
    font-size: 18px;
    color: #909399;
  }
}

.next-btn {
  width: 100%;
  margin-top: 20px;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 1px;
  border-radius: 8px;
  transition: all 0.3s;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(var(--el-color-primary-rgb), 0.3);
  }
}

.verify-code-container {
  display: flex;
  gap: 15px;

  .el-input {
    flex: 1;
  }

  .verify-code-btn {
    width: 150px;
    white-space: nowrap;
    height: 48px;
    font-size: 14px;
    border-radius: 8px;
  }
}

.agreement-tip {
  margin: 15px 0;
}

// 装饰元素
.decoration-circles {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;

  .circle {
    position: absolute;
    border-radius: 50%;
    opacity: 0.6;
  }

  .circle-1 {
    width: 300px;
    height: 300px;
    background: linear-gradient(
      135deg,
      rgba(var(--el-color-primary-rgb), 0.2),
      rgba(var(--el-color-primary-rgb), 0.05)
    );
    top: -100px;
    right: 10%;
    animation: float 8s ease-in-out infinite;
  }

  .circle-2 {
    width: 200px;
    height: 200px;
    background: linear-gradient(
      135deg,
      rgba(var(--el-color-primary-rgb), 0.15),
      rgba(var(--el-color-primary-rgb), 0.05)
    );
    bottom: -50px;
    left: 10%;
    animation: float 6s ease-in-out infinite 1s;
  }

  .circle-3 {
    width: 150px;
    height: 150px;
    background: linear-gradient(
      135deg,
      rgba(var(--el-color-primary-rgb), 0.1),
      rgba(var(--el-color-primary-rgb), 0.03)
    );
    top: 40%;
    left: 20%;
    animation: float 7s ease-in-out infinite 0.5s;
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 768px) {
  .register-box {
    width: 95% !important;
    padding: 30px !important;
    margin: 15px;
  }

  .register-title {
    font-size: 28px !important;

    span {
      font-size: 24px !important;
    }
  }

  .register-subtitle {
    font-size: 14px !important;
  }

  .verify-code-container {
    flex-direction: column;
    gap: 10px;

    .verify-code-btn {
      width: 100%;
    }
  }
}
</style>
