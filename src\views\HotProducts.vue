<template>
  <div class="hot-products-container">
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">高销售商品分析</h1>
        <div class="page-subtitle">分析热销商品数据，了解销售趋势及商品表现</div>
      </div>
      <div class="header-right">
        <el-select
          v-model="timeRange"
          placeholder="时间范围"
          style="width: 120px"
          @change="fetchData"
        >
          <el-option label="今日" value="today" />
          <el-option label="本周" value="week" />
          <el-option label="本月" value="month" />
          <el-option label="本季度" value="quarter" />
          <el-option label="今年" value="year" />
          <el-option label="自定义" value="custom" />
        </el-select>

        <el-date-picker
          v-if="timeRange === 'custom'"
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="fetchData"
        />

        <el-button type="primary" @click="exportData">导出数据</el-button>
      </div>
    </div>

    <!-- 销售概览 -->
    <div class="overview-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card">
            <div class="overview-icon">
              <el-icon><ShoppingBag /></el-icon>
            </div>
            <div class="overview-content">
              <div class="overview-label">商品总销量</div>
              <div class="overview-value">{{ statistics.totalSales }}</div>
              <div class="overview-trend" :class="statistics.salesTrend >= 0 ? 'up' : 'down'">
                <el-icon v-if="statistics.salesTrend >= 0"><ArrowUp /></el-icon>
                <el-icon v-else><ArrowDown /></el-icon>
                {{ Math.abs(statistics.salesTrend) }}% 较上期
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card">
            <div class="overview-icon">
              <el-icon><Money /></el-icon>
            </div>
            <div class="overview-content">
              <div class="overview-label">销售总额</div>
              <div class="overview-value">¥{{ statistics.totalAmount }}</div>
              <div class="overview-trend" :class="statistics.amountTrend >= 0 ? 'up' : 'down'">
                <el-icon v-if="statistics.amountTrend >= 0"><ArrowUp /></el-icon>
                <el-icon v-else><ArrowDown /></el-icon>
                {{ Math.abs(statistics.amountTrend) }}% 较上期
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card">
            <div class="overview-icon">
              <el-icon><ShoppingCart /></el-icon>
            </div>
            <div class="overview-content">
              <div class="overview-label">销售商品数</div>
              <div class="overview-value">{{ statistics.productCount }}</div>
              <div
                class="overview-trend"
                :class="statistics.productCountTrend >= 0 ? 'up' : 'down'"
              >
                <el-icon v-if="statistics.productCountTrend >= 0"><ArrowUp /></el-icon>
                <el-icon v-else><ArrowDown /></el-icon>
                {{ Math.abs(statistics.productCountTrend) }}% 较上期
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card">
            <div class="overview-icon">
              <el-icon><PriceTag /></el-icon>
            </div>
            <div class="overview-content">
              <div class="overview-label">平均售价</div>
              <div class="overview-value">¥{{ statistics.avgPrice }}</div>
              <div class="overview-trend" :class="statistics.avgPriceTrend >= 0 ? 'up' : 'down'">
                <el-icon v-if="statistics.avgPriceTrend >= 0"><ArrowUp /></el-icon>
                <el-icon v-else><ArrowDown /></el-icon>
                {{ Math.abs(statistics.avgPriceTrend) }}% 较上期
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 商品销售排行 -->
    <el-card shadow="hover" class="rank-card">
      <template #header>
        <div class="card-header">
          <span>商品销售排行</span>
          <div class="header-actions">
            <el-select
              v-model="productFilter"
              placeholder="商品分类"
              style="width: 150px"
              @change="filterProducts"
            >
              <el-option label="全部分类" value="" />
              <el-option
                v-for="item in categories"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-select
              v-model="sortType"
              placeholder="排序方式"
              style="width: 150px"
              @change="sortProducts"
            >
              <el-option label="按销量排序" value="sales" />
              <el-option label="按销售额排序" value="amount" />
              <el-option label="按利润排序" value="profit" />
            </el-select>
          </div>
        </div>
      </template>
      <div class="product-table">
        <el-table :data="products" style="width: 100%" size="large" height="450">
          <el-table-column type="index" label="排名" width="60">
            <template #default="scope">
              <div class="rank-number" :class="getRankClass(scope.$index + 1)">
                {{ scope.$index + 1 }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="商品名称" min-width="200" />
          <el-table-column prop="category" label="分类" width="100" />
          <el-table-column prop="merchant" label="所属商家" min-width="150" />
          <el-table-column prop="sales" label="销量" sortable width="100">
            <template #default="scope">
              <span class="sales-count">{{ scope.row.sales }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="price" label="单价" width="100" />
          <el-table-column prop="amount" label="销售额" sortable width="120" />
          <el-table-column prop="profit" label="利润" sortable width="100" />
          <el-table-column prop="growth" label="环比增长" width="120">
            <template #default="scope">
              <span :class="scope.row.growthTrend >= 0 ? 'trend-up' : 'trend-down'">
                <el-icon v-if="scope.row.growthTrend >= 0"><ArrowUp /></el-icon>
                <el-icon v-else><ArrowDown /></el-icon>
                {{ scope.row.growth }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" fixed="right">
            <template #default="scope">
              <el-button type="primary" link @click="viewProductDetail(scope.row.id)">
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 类别销售分布 -->
    <el-row :gutter="20" class="chart-row">
      <el-col :span="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>类别销售分布</span>
            </div>
          </template>
          <div class="chart-container" style="height: 350px">
            <div class="mock-pie-chart">
              <!-- 这里将使用实际图表组件，如ECharts -->
              <div class="chart-placeholder">类别销售分布饼图</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>销售趋势</span>
              <div class="header-actions">
                <el-radio-group v-model="trendViewType" size="small" @change="updateTrendView">
                  <el-radio-button label="daily">日</el-radio-button>
                  <el-radio-button label="weekly">周</el-radio-button>
                  <el-radio-button label="monthly">月</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </template>
          <div class="chart-container" style="height: 350px">
            <div class="mock-line-chart">
              <!-- 这里将使用实际图表组件，如ECharts -->
              <div class="chart-placeholder">销售趋势折线图</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 商品热销分析表 -->
    <el-card shadow="hover" class="analysis-card">
      <template #header>
        <div class="card-header">
          <span>商品热销分析</span>
        </div>
      </template>
      <div class="analysis-content">
        <div class="analysis-filters">
          <el-select
            v-model="analysisType"
            placeholder="分析维度"
            style="width: 150px"
            @change="changeAnalysisType"
          >
            <el-option label="按商品分类" value="category" />
            <el-option label="按商家" value="merchant" />
            <el-option label="按价格区间" value="price" />
          </el-select>
        </div>
        <div class="analysis-table">
          <el-table :data="analysisData" style="width: 100%">
            <el-table-column prop="name" :label="analysisTypeLabel" min-width="150" />
            <el-table-column prop="salesCount" label="销售数量" sortable />
            <el-table-column prop="salesAmount" label="销售额" sortable />
            <el-table-column prop="percentage" label="占比">
              <template #default="scope">
                <el-progress
                  :percentage="scope.row.percentage"
                  :color="getPercentageColor(scope.row.percentage)"
                />
              </template>
            </el-table-column>
            <el-table-column prop="topProduct" label="最畅销商品" min-width="150" />
          </el-table>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  ArrowUp,
  ArrowDown,
  ShoppingBag,
  Money,
  ShoppingCart,
  PriceTag,
} from '@element-plus/icons-vue'

const router = useRouter()
const timeRange = ref('month')
const dateRange = ref([])
const productFilter = ref('')
const sortType = ref('sales')
const trendViewType = ref('daily')
const analysisType = ref('category')

// 商品分类选项
const categories = ref([
  { value: 'electronics', label: '电子数码' },
  { value: 'clothing', label: '服装服饰' },
  { value: 'home', label: '家居家装' },
  { value: 'beauty', label: '美妆个护' },
  { value: 'food', label: '食品生鲜' },
])

// 销售统计数据
const statistics = reactive({
  totalSales: '12,856',
  salesTrend: 8.5,
  totalAmount: '3,256,897',
  amountTrend: 12.3,
  productCount: 258,
  productCountTrend: 5.8,
  avgPrice: '253.34',
  avgPriceTrend: 3.6,
})

// 商品销售数据
const products = ref([
  {
    id: 1,
    name: 'Apple 苹果 iPhone 14 Pro Max 智能手机',
    category: '电子数码',
    merchant: '电子科技专营店',
    sales: 586,
    price: '¥7,999',
    amount: '¥4,687,414',
    profit: '¥843,734',
    growth: '15.8%',
    growthTrend: 15.8,
  },
  {
    id: 2,
    name: '小米12 Pro 5G 智能手机',
    category: '电子数码',
    merchant: '电子科技专营店',
    sales: 423,
    price: '¥4,699',
    amount: '¥1,987,677',
    profit: '¥357,782',
    growth: '12.3%',
    growthTrend: 12.3,
  },
  {
    id: 3,
    name: 'Apple 苹果 AirPods Pro 2代',
    category: '电子数码',
    merchant: '电子科技专营店',
    sales: 378,
    price: '¥1,799',
    amount: '¥680,022',
    profit: '¥204,006',
    growth: '9.5%',
    growthTrend: 9.5,
  },
  {
    id: 4,
    name: '华为 HUAWEI P50 Pro 5G 智能手机',
    category: '电子数码',
    merchant: '家电数码专卖店',
    sales: 356,
    price: '¥6,488',
    amount: '¥2,309,728',
    profit: '¥415,751',
    growth: '8.2%',
    growthTrend: 8.2,
  },
  {
    id: 5,
    name: '海尔 变频滚筒洗衣机',
    category: '家居家装',
    merchant: '家电数码专卖店',
    sales: 298,
    price: '¥2,999',
    amount: '¥893,702',
    profit: '¥178,740',
    growth: '6.7%',
    growthTrend: 6.7,
  },
  {
    id: 6,
    name: '雅诗兰黛 小棕瓶精华液',
    category: '美妆个护',
    merchant: '全球美妆专卖',
    sales: 287,
    price: '¥850',
    amount: '¥243,950',
    profit: '¥97,580',
    growth: '5.3%',
    growthTrend: 5.3,
  },
  {
    id: 7,
    name: '耐克 NIKE AIR FORCE 1 男子运动鞋',
    category: '服装服饰',
    merchant: '运动户外专营',
    sales: 265,
    price: '¥899',
    amount: '¥238,235',
    profit: '¥83,382',
    growth: '4.8%',
    growthTrend: 4.8,
  },
  {
    id: 8,
    name: 'SK-II 神仙水 护肤精华液',
    category: '美妆个护',
    merchant: '全球美妆专卖',
    sales: 243,
    price: '¥1,540',
    amount: '¥374,220',
    profit: '¥149,688',
    growth: '3.9%',
    growthTrend: 3.9,
  },
  {
    id: 9,
    name: '戴森 Dyson V11 吸尘器',
    category: '家居家装',
    merchant: '优品家居旗舰店',
    sales: 218,
    price: '¥4,290',
    amount: '¥935,220',
    profit: '¥224,452',
    growth: '3.2%',
    growthTrend: 3.2,
  },
  {
    id: 10,
    name: '三星 Galaxy S22 Ultra 智能手机',
    category: '电子数码',
    merchant: '电子科技专营店',
    sales: 205,
    price: '¥7,499',
    amount: '¥1,537,295',
    profit: '¥276,713',
    growth: '-2.5%',
    growthTrend: -2.5,
  },
])

// 分析数据
const analysisData = ref([
  {
    name: '电子数码',
    salesCount: 1948,
    salesAmount: '¥11,202,136',
    percentage: 65.3,
    topProduct: 'iPhone 14 Pro Max',
  },
  {
    name: '家居家装',
    salesCount: 765,
    salesAmount: '¥2,652,367',
    percentage: 15.5,
    topProduct: '戴森 Dyson V11 吸尘器',
  },
  {
    name: '美妆个护',
    salesCount: 698,
    salesAmount: '¥1,230,540',
    percentage: 7.2,
    topProduct: 'SK-II 神仙水',
  },
  {
    name: '服装服饰',
    salesCount: 625,
    salesAmount: '¥978,625',
    percentage: 5.7,
    topProduct: '耐克 AIR FORCE 1',
  },
  {
    name: '食品生鲜',
    salesCount: 587,
    salesAmount: '¥543,589',
    percentage: 3.2,
    topProduct: '五常大米',
  },
])

// 分析维度标签
const analysisTypeLabel = computed(() => {
  switch (analysisType.value) {
    case 'category':
      return '商品分类'
    case 'merchant':
      return '商家名称'
    case 'price':
      return '价格区间'
    default:
      return '分析维度'
  }
})

// 获取排名样式
const getRankClass = (rank: number) => {
  if (rank === 1) return 'rank-first'
  if (rank === 2) return 'rank-second'
  if (rank === 3) return 'rank-third'
  return ''
}

// 获取百分比颜色
const getPercentageColor = (percentage: number) => {
  if (percentage > 50) return '#67C23A'
  if (percentage > 20) return '#409EFF'
  return '#E6A23C'
}

// 查看商品详情
const viewProductDetail = (productId: number) => {
  router.push(`/product-detail/${productId}`)
}

// 获取数据
const fetchData = () => {
  console.log('Fetching data for time range:', timeRange.value, dateRange.value)
  // 将调用API获取数据
}

// 筛选商品
const filterProducts = () => {
  console.log('Filtering products by category:', productFilter.value)
  // 筛选逻辑
}

// 排序商品
const sortProducts = () => {
  console.log('Sorting products by:', sortType.value)
  // 排序逻辑
}

// 更新趋势视图
const updateTrendView = () => {
  console.log('Updating trend view to:', trendViewType.value)
  // 更新图表
}

// 更改分析类型
const changeAnalysisType = () => {
  console.log('Changing analysis type to:', analysisType.value)
  // 更新分析数据
}

// 导出数据
const exportData = () => {
  console.log('Exporting product data')
  // 导出逻辑
}

onMounted(() => {
  // 初始化日期范围为最近30天
  const end = new Date()
  const start = new Date()
  start.setDate(start.getDate() - 30)
  dateRange.value = [start, end]

  // 获取初始数据
  fetchData()
})
</script>

<style scoped lang="scss">
.hot-products-container {
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #eef2f5 100%);
  border-radius: 12px;
  min-height: calc(100vh - 40px);

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    .header-left {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        margin: 0 0 8px 0;
        background: linear-gradient(120deg, #3a7bd5, #2c5499);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .page-subtitle {
        font-size: 14px;
        color: #606266;
      }
    }

    .header-right {
      display: flex;
      gap: 16px;
    }
  }

  .overview-section {
    margin-bottom: 24px;

    .overview-card {
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;
      height: 100%;
      display: flex;
      padding: 20px;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
      }

      .overview-icon {
        font-size: 48px;
        color: #409eff;
        margin-right: 20px;
        display: flex;
        align-items: center;
      }

      .overview-content {
        flex: 1;

        .overview-label {
          font-size: 16px;
          color: #606266;
          margin-bottom: 8px;
        }

        .overview-value {
          font-size: 24px;
          font-weight: 600;
          color: #303133;
          margin-bottom: 8px;
        }

        .overview-trend {
          display: flex;
          align-items: center;
          font-size: 14px;

          &.up {
            color: #67c23a;
          }

          &.down {
            color: #f56c6c;
          }
        }
      }
    }
  }

  .rank-card,
  .chart-card,
  .analysis-card {
    margin-bottom: 24px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 16px;
      font-weight: 600;
      padding: 16px 20px;

      .header-actions {
        display: flex;
        gap: 16px;
      }
    }
  }

  .product-table {
    padding: 0 20px 20px;

    .rank-number {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 28px;
      height: 28px;
      border-radius: 50%;
      background: #ebeef5;
      color: #606266;
      font-weight: 600;

      &.rank-first {
        background: #f56c6c;
        color: white;
      }

      &.rank-second {
        background: #e6a23c;
        color: white;
      }

      &.rank-third {
        background: #67c23a;
        color: white;
      }
    }

    .sales-count {
      font-weight: 600;
    }

    .trend-up {
      display: flex;
      align-items: center;
      color: #67c23a;
    }

    .trend-down {
      display: flex;
      align-items: center;
      color: #f56c6c;
    }
  }

  .chart-row {
    margin-bottom: 24px;
  }

  .chart-container {
    padding: 20px;
    display: flex;
    justify-content: center;
    align-items: center;

    .mock-pie-chart,
    .mock-line-chart {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(64, 158, 255, 0.05);
      border-radius: 12px;
    }

    .chart-placeholder {
      color: #909399;
      font-size: 16px;
    }
  }

  .analysis-content {
    padding: 0 20px 20px;

    .analysis-filters {
      margin-bottom: 20px;
    }
  }
}

@media screen and (max-width: 768px) {
  .hot-products-container {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-right {
        width: 100%;
      }
    }

    .overview-section .el-col {
      margin-bottom: 16px;
    }

    .chart-row .el-col {
      margin-bottom: 16px;
    }
  }
}
</style>
