<template>
  <div class="merchant-sales-container">
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">商家营业额分析</h1>
        <div class="page-subtitle">查看各商家营业额情况，进行多维度分析</div>
      </div>
      <div class="header-right">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="fetchMerchantSalesData"
        />
        <el-button type="primary" @click="exportData">导出数据</el-button>
      </div>
    </div>

    <!-- 营业额统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card shadow="hover" class="stats-card">
            <div class="stats-icon">
              <el-icon><Money /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-label">总营业额</div>
              <div class="stats-value">¥{{ salesStats.totalSales }}</div>
              <div
                class="stats-trend"
                :class="salesStats.salesTrend >= 0 ? 'trend-up' : 'trend-down'"
              >
                <el-icon v-if="salesStats.salesTrend >= 0"><ArrowUp /></el-icon>
                <el-icon v-else><ArrowDown /></el-icon>
                {{ Math.abs(salesStats.salesTrend) }}% 较上期
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="hover" class="stats-card">
            <div class="stats-icon">
              <el-icon><ShoppingCart /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-label">总订单数</div>
              <div class="stats-value">{{ salesStats.totalOrders }}</div>
              <div
                class="stats-trend"
                :class="salesStats.ordersTrend >= 0 ? 'trend-up' : 'trend-down'"
              >
                <el-icon v-if="salesStats.ordersTrend >= 0"><ArrowUp /></el-icon>
                <el-icon v-else><ArrowDown /></el-icon>
                {{ Math.abs(salesStats.ordersTrend) }}% 较上期
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="hover" class="stats-card">
            <div class="stats-icon">
              <el-icon><Wallet /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-label">平均客单价</div>
              <div class="stats-value">¥{{ salesStats.avgOrderValue }}</div>
              <div
                class="stats-trend"
                :class="salesStats.avgOrderTrend >= 0 ? 'trend-up' : 'trend-down'"
              >
                <el-icon v-if="salesStats.avgOrderTrend >= 0"><ArrowUp /></el-icon>
                <el-icon v-else><ArrowDown /></el-icon>
                {{ Math.abs(salesStats.avgOrderTrend) }}% 较上期
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 商家营业额排行 -->
    <el-card shadow="hover" class="rank-card">
      <template #header>
        <div class="card-header">
          <span>商家营业额排行</span>
          <div class="header-actions">
            <el-select v-model="rankLimit" placeholder="显示数量" @change="updateRankList">
              <el-option label="TOP 10" :value="10" />
              <el-option label="TOP 20" :value="20" />
              <el-option label="TOP 50" :value="50" />
              <el-option label="显示全部" :value="0" />
            </el-select>
            <el-select v-model="sortOrder" placeholder="排序方式" @change="updateRankList">
              <el-option label="营业额从高到低" value="desc" />
              <el-option label="营业额从低到高" value="asc" />
              <el-option label="增长率从高到低" value="growth_desc" />
            </el-select>
          </div>
        </div>
      </template>
      <div class="rank-table-container">
        <el-table :data="merchantRanks" style="width: 100%" size="large" :max-height="500">
          <el-table-column type="index" label="排名" width="80">
            <template #default="scope">
              <div class="rank-number" :class="getRankClass(scope.$index + 1)">
                {{ scope.$index + 1 }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="商家名称" min-width="180" />
          <el-table-column prop="sales" label="营业额" sortable min-width="120">
            <template #default="scope">
              <span class="sales-amount">{{ scope.row.sales }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="orderCount" label="订单数" sortable min-width="100" />
          <el-table-column prop="avgOrderValue" label="客单价" sortable min-width="100">
            <template #default="scope">
              {{ scope.row.avgOrderValue }}
            </template>
          </el-table-column>
          <el-table-column prop="growth" label="环比增长" min-width="120">
            <template #default="scope">
              <span :class="scope.row.isUp ? 'trend-up' : 'trend-down'">
                <el-icon v-if="scope.row.isUp"><ArrowUp /></el-icon>
                <el-icon v-else><ArrowDown /></el-icon>
                {{ scope.row.growth }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="操作" fixed="right" width="150">
            <template #default="scope">
              <el-button type="primary" size="small" @click="viewMerchantDetail(scope.row.id)">
                详情
              </el-button>
              <el-button type="info" size="small" @click="viewMerchantCharts(scope.row.id)">
                图表
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 每日营业额趋势 -->
    <el-card shadow="hover" class="trend-card">
      <template #header>
        <div class="card-header">
          <span>每日营业额趋势</span>
          <div class="header-actions">
            <el-radio-group v-model="trendType" size="small" @change="updateTrendChart">
              <el-radio-button label="daily">日视图</el-radio-button>
              <el-radio-button label="weekly">周视图</el-radio-button>
              <el-radio-button label="monthly">月视图</el-radio-button>
            </el-radio-group>
            <el-button type="text" @click="showTrendSettings">
              <el-icon><Setting /></el-icon>
            </el-button>
          </div>
        </div>
      </template>
      <div class="trend-chart-container" style="height: 350px; margin-top: 20px">
        <!-- 这里将放置图表组件，暂用模拟UI -->
        <div class="mock-chart">
          <div class="chart-lines">
            <div
              class="chart-line"
              v-for="(height, index) in trendData"
              :key="index"
              :style="{ height: height + '%', left: index * 5 + '%' }"
            ></div>
          </div>
          <div class="chart-axis-x">
            <div
              class="axis-label"
              v-for="(label, index) in trendLabels"
              :key="index"
              :style="{ left: index * 10 + '%' }"
            >
              {{ label }}
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 商家对比分析 -->
    <el-card shadow="hover" class="compare-card">
      <template #header>
        <div class="card-header">
          <span>商家对比分析</span>
          <div class="header-actions">
            <el-select
              v-model="selectedMerchants"
              multiple
              placeholder="选择要对比的商家"
              style="width: 400px"
            >
              <el-option
                v-for="item in merchantOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-button type="primary" @click="compareMerchants">对比</el-button>
          </div>
        </div>
      </template>
      <div class="compare-chart-container" v-if="showCompareChart">
        <div class="compare-chart" style="height: 300px">
          <!-- 这里将放置对比图表组件，暂用模拟UI -->
          <div class="mock-compare-chart">
            <div class="compare-bars">
              <div
                v-for="(merchant, index) in compareData"
                :key="index"
                class="merchant-compare-group"
              >
                <div class="merchant-name">{{ merchant.name }}</div>
                <div class="bar-group">
                  <div class="bar-item">
                    <div class="bar-label">营业额</div>
                    <div class="bar" :style="{ height: merchant.salesHeight + '%' }"></div>
                    <div class="bar-value">{{ merchant.sales }}</div>
                  </div>
                  <div class="bar-item">
                    <div class="bar-label">订单数</div>
                    <div
                      class="bar orders-bar"
                      :style="{ height: merchant.ordersHeight + '%' }"
                    ></div>
                    <div class="bar-value">{{ merchant.orderCount }}</div>
                  </div>
                  <div class="bar-item">
                    <div class="bar-label">客单价</div>
                    <div class="bar avg-bar" :style="{ height: merchant.avgHeight + '%' }"></div>
                    <div class="bar-value">{{ merchant.avgOrderValue }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="no-compare-data" v-else>
        <el-empty description="请选择要对比的商家"></el-empty>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ArrowUp, ArrowDown, Money, ShoppingCart, Wallet, Setting } from '@element-plus/icons-vue'

const router = useRouter()
const dateRange = ref([])
const rankLimit = ref(10)
const sortOrder = ref('desc')
const trendType = ref('daily')
const selectedMerchants = ref([])
const showCompareChart = ref(false)

// 统计数据
const salesStats = reactive({
  totalSales: '1,289,560',
  salesTrend: 8.5,
  totalOrders: '5,862',
  ordersTrend: 12.3,
  avgOrderValue: '219.8',
  avgOrderTrend: -3.2,
})

// 商家营业额排行数据 - 示例数据
const merchantRanks = ref([
  {
    id: 1,
    name: '电子科技专营店',
    sales: '¥128,560',
    orderCount: 586,
    avgOrderValue: '¥219.4',
    growth: '15.8%',
    isUp: true,
  },
  {
    id: 2,
    name: '优品家居旗舰店',
    sales: '¥98,320',
    orderCount: 453,
    avgOrderValue: '¥217.0',
    growth: '12.3%',
    isUp: true,
  },
  {
    id: 3,
    name: '时尚服饰品牌店',
    sales: '¥89,750',
    orderCount: 412,
    avgOrderValue: '¥217.8',
    growth: '9.5%',
    isUp: true,
  },
  {
    id: 4,
    name: '全球美妆专卖',
    sales: '¥78,450',
    orderCount: 389,
    avgOrderValue: '¥201.7',
    growth: '8.2%',
    isUp: true,
  },
  {
    id: 5,
    name: '健康食品直营店',
    sales: '¥65,280',
    orderCount: 298,
    avgOrderValue: '¥219.1',
    growth: '6.7%',
    isUp: true,
  },
  {
    id: 6,
    name: '办公用品商城',
    sales: '¥58,920',
    orderCount: 276,
    avgOrderValue: '¥213.5',
    growth: '5.3%',
    isUp: true,
  },
  {
    id: 7,
    name: '运动户外专营',
    sales: '¥52,680',
    orderCount: 247,
    avgOrderValue: '¥213.3',
    growth: '4.8%',
    isUp: true,
  },
  {
    id: 8,
    name: '母婴用品旗舰店',
    sales: '¥48,350',
    orderCount: 229,
    avgOrderValue: '¥211.1',
    growth: '3.9%',
    isUp: true,
  },
  {
    id: 9,
    name: '家电数码专卖店',
    sales: '¥45,780',
    orderCount: 215,
    avgOrderValue: '¥212.9',
    growth: '3.2%',
    isUp: true,
  },
  {
    id: 10,
    name: '图书文具商城',
    sales: '¥42,150',
    orderCount: 198,
    avgOrderValue: '¥212.9',
    growth: '-2.5%',
    isUp: false,
  },
  {
    id: 11,
    name: '休闲食品专卖',
    sales: '¥39,860',
    orderCount: 185,
    avgOrderValue: '¥215.5',
    growth: '-3.1%',
    isUp: false,
  },
  {
    id: 12,
    name: '宠物用品直营',
    sales: '¥35,720',
    orderCount: 168,
    avgOrderValue: '¥212.6',
    growth: '2.8%',
    isUp: true,
  },
])

// 趋势图数据 - 示例数据
const trendData = ref([
  30, 45, 35, 50, 40, 60, 55, 45, 50, 65, 55, 70, 60, 75, 65, 80, 70, 85, 75, 90,
])
const trendLabels = ref(['6/1', '6/3', '6/5', '6/7', '6/9', '6/11', '6/13', '6/15', '6/17', '6/19'])

// 商家对比选项
const merchantOptions = ref([
  { value: 1, label: '电子科技专营店' },
  { value: 2, label: '优品家居旗舰店' },
  { value: 3, label: '时尚服饰品牌店' },
  { value: 4, label: '全球美妆专卖' },
  { value: 5, label: '健康食品直营店' },
  { value: 6, label: '办公用品商城' },
  { value: 7, label: '运动户外专营' },
  { value: 8, label: '母婴用品旗舰店' },
  { value: 9, label: '家电数码专卖店' },
  { value: 10, label: '图书文具商城' },
])

// 对比数据
const compareData = ref([
  {
    name: '电子科技专营店',
    sales: '¥128,560',
    salesHeight: 90,
    orderCount: '586',
    ordersHeight: 75,
    avgOrderValue: '¥219.4',
    avgHeight: 80,
  },
  {
    name: '优品家居旗舰店',
    sales: '¥98,320',
    salesHeight: 70,
    orderCount: '453',
    ordersHeight: 60,
    avgOrderValue: '¥217.0',
    avgHeight: 79,
  },
  {
    name: '时尚服饰品牌店',
    sales: '¥89,750',
    salesHeight: 65,
    orderCount: '412',
    ordersHeight: 55,
    avgOrderValue: '¥217.8',
    avgHeight: 79,
  },
])

// 获取排名样式
const getRankClass = (rank: number) => {
  if (rank === 1) return 'rank-first'
  if (rank === 2) return 'rank-second'
  if (rank === 3) return 'rank-third'
  return ''
}

// 查看商家详情
const viewMerchantDetail = (merchantId: number) => {
  router.push(`/merchant-detail/${merchantId}`)
}

// 查看商家图表
const viewMerchantCharts = (merchantId: number) => {
  router.push(`/merchant-charts/${merchantId}`)
}

// 获取商家销售数据
const fetchMerchantSalesData = () => {
  console.log('Fetching merchant sales data for date range:', dateRange.value)
  // 这里将调用API获取真实数据
}

// 更新排行榜
const updateRankList = () => {
  console.log('Updating rank list with limit:', rankLimit.value, 'sort order:', sortOrder.value)
  // 这里将根据选择的条件更新数据
}

// 更新趋势图
const updateTrendChart = () => {
  console.log('Updating trend chart to type:', trendType.value)
  // 这里将根据选择的视图类型更新图表
}

// 显示趋势图设置
const showTrendSettings = () => {
  console.log('Show trend settings dialog')
  // 这里将显示设置对话框
}

// 对比商家
const compareMerchants = () => {
  console.log('Comparing merchants:', selectedMerchants.value)
  if (selectedMerchants.value.length > 0) {
    showCompareChart.value = true
    // 这里将获取并更新对比数据
  } else {
    showCompareChart.value = false
  }
}

// 导出数据
const exportData = () => {
  console.log('Exporting data')
  // 这里将实现导出功能
}

onMounted(() => {
  // 初始化日期范围为最近30天
  const end = new Date()
  const start = new Date()
  start.setDate(start.getDate() - 30)
  dateRange.value = [start, end]

  // 获取初始数据
  fetchMerchantSalesData()
})
</script>

<style scoped lang="scss">
.merchant-sales-container {
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #eef2f5 100%);
  border-radius: 12px;
  min-height: calc(100vh - 40px);

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    .header-left {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        margin: 0 0 8px 0;
        background: linear-gradient(120deg, #3a7bd5, #2c5499);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .page-subtitle {
        font-size: 14px;
        color: #606266;
      }
    }

    .header-right {
      display: flex;
      gap: 16px;
    }
  }

  .stats-cards {
    margin-bottom: 24px;

    .stats-card {
      display: flex;
      align-items: center;
      padding: 20px;
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
      }

      .stats-icon {
        font-size: 48px;
        color: #409eff;
        margin-right: 20px;
      }

      .stats-info {
        flex: 1;

        .stats-label {
          font-size: 16px;
          color: #606266;
          margin-bottom: 8px;
        }

        .stats-value {
          font-size: 28px;
          font-weight: 600;
          color: #303133;
          margin-bottom: 8px;
        }

        .stats-trend {
          display: flex;
          align-items: center;
          font-size: 14px;

          &.trend-up {
            color: #67c23a;
          }

          &.trend-down {
            color: #f56c6c;
          }
        }
      }
    }
  }

  .rank-card,
  .trend-card,
  .compare-card {
    margin-bottom: 24px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 16px;
      font-weight: 600;
      padding: 16px 20px;

      .header-actions {
        display: flex;
        gap: 16px;
      }
    }
  }

  .rank-table-container {
    padding: 0 20px 20px;

    .rank-number {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 28px;
      height: 28px;
      border-radius: 50%;
      background: #ebeef5;
      color: #606266;
      font-weight: 600;

      &.rank-first {
        background: #f56c6c;
        color: white;
      }

      &.rank-second {
        background: #e6a23c;
        color: white;
      }

      &.rank-third {
        background: #67c23a;
        color: white;
      }
    }

    .sales-amount {
      font-weight: 600;
      color: #303133;
    }

    .trend-up {
      display: flex;
      align-items: center;
      color: #67c23a;
    }

    .trend-down {
      display: flex;
      align-items: center;
      color: #f56c6c;
    }
  }

  .trend-chart-container {
    padding: 0 20px 20px;

    .mock-chart {
      position: relative;
      width: 100%;
      height: 100%;
      border-bottom: 1px solid #ebeef5;

      .chart-lines {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: flex-end;
        padding-bottom: 30px;

        .chart-line {
          position: relative;
          width: 10px;
          background: linear-gradient(to top, #409eff, #64b5f6);
          border-radius: 4px 4px 0 0;
          margin-right: 10px;
          transition: all 0.3s;

          &:hover {
            transform: scaleY(1.05);
            background: linear-gradient(to top, #3a7bd5, #64b5f6);
          }
        }
      }

      .chart-axis-x {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 30px;
        display: flex;
        align-items: center;

        .axis-label {
          position: absolute;
          transform: translateX(-50%);
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }

  .compare-chart-container {
    padding: 20px;

    .mock-compare-chart {
      width: 100%;
      height: 100%;

      .compare-bars {
        display: flex;
        justify-content: space-around;
        height: 100%;

        .merchant-compare-group {
          display: flex;
          flex-direction: column;
          align-items: center;
          width: 200px;

          .merchant-name {
            font-weight: 600;
            margin-bottom: 16px;
            text-align: center;
          }

          .bar-group {
            display: flex;
            gap: 20px;
            height: 250px;
            align-items: flex-end;
            width: 100%;

            .bar-item {
              flex: 1;
              display: flex;
              flex-direction: column;
              align-items: center;
              height: 100%;

              .bar-label {
                font-size: 12px;
                color: #909399;
                margin-bottom: 8px;
              }

              .bar {
                width: 30px;
                background: linear-gradient(to top, #409eff, #64b5f6);
                border-radius: 4px 4px 0 0;
                transition: all 0.3s;

                &:hover {
                  transform: scaleY(1.05);
                }
              }

              .orders-bar {
                background: linear-gradient(to top, #67c23a, #95d475);
              }

              .avg-bar {
                background: linear-gradient(to top, #e6a23c, #f5d19c);
              }

              .bar-value {
                font-size: 12px;
                margin-top: 8px;
                font-weight: 600;
              }
            }
          }
        }
      }
    }
  }

  .no-compare-data {
    padding: 50px 0;
  }
}

@media screen and (max-width: 1200px) {
  .merchant-sales-container {
    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-right {
        width: 100%;
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .merchant-sales-container {
    padding: 16px;

    .stats-cards {
      .el-row {
        margin: 0 !important;
      }

      .el-col {
        padding: 0 !important;
        margin-bottom: 16px;
      }
    }

    .rank-card,
    .trend-card,
    .compare-card {
      .card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;

        .header-actions {
          width: 100%;
          flex-wrap: wrap;
        }
      }
    }
  }
}
</style>
