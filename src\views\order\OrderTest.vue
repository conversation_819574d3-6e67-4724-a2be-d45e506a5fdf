<template>
  <div class="order-test-page">
    <el-card>
      <template #header>
        <h2>订单功能测试页面</h2>
      </template>
      
      <div class="test-sections">
        <!-- 状态测试 -->
        <el-card class="test-section">
          <template #header>
            <h3>订单状态测试</h3>
          </template>
          <div class="status-tests">
            <div v-for="status in orderStatuses" :key="status.value" class="status-item">
              <el-tag :type="getStatusType(status.value)">
                {{ getStatusText(status.value) }}
              </el-tag>
              <span class="status-info">
                状态码: {{ status.value }} | 
                可执行操作: {{ getAvailableActions(status.value).join(', ') || '无' }}
              </span>
            </div>
          </div>
        </el-card>

        <!-- 支付方式测试 -->
        <el-card class="test-section">
          <template #header>
            <h3>支付方式测试</h3>
          </template>
          <div class="payment-tests">
            <div v-for="method in payMethods" :key="method.value" class="payment-item">
              <span class="payment-code">{{ method.value }}</span>
              <span class="payment-name">{{ method.label }}</span>
            </div>
          </div>
        </el-card>

        <!-- API接口测试 -->
        <el-card class="test-section">
          <template #header>
            <h3>API接口测试</h3>
          </template>
          <div class="api-tests">
            <el-button @click="testOrderList" :loading="loading.orderList">
              测试订单列表
            </el-button>
            <el-button @click="testOrderStatistics" :loading="loading.statistics">
              测试订单统计
            </el-button>
            <el-button @click="testTodayStatistics" :loading="loading.today">
              测试今日统计
            </el-button>
            <el-button @click="testMonthStatistics" :loading="loading.month">
              测试本月统计
            </el-button>
            <el-button @click="testStatusDistribution" :loading="loading.distribution">
              测试状态分布
            </el-button>
          </div>
        </el-card>

        <!-- 测试结果 -->
        <el-card class="test-section">
          <template #header>
            <h3>测试结果</h3>
          </template>
          <div class="test-results">
            <pre>{{ JSON.stringify(testResults, null, 2) }}</pre>
          </div>
        </el-card>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  getStatusText, 
  getStatusType, 
  getAvailableActions 
} from '@/utils/orderStatus'
import {
  getOrderList,
  getOrderStatisticsData,
  getTodayStatistics,
  getMonthStatistics,
  getStatusDistribution
} from '@/api/order'

// 订单状态列表
const orderStatuses = [
  { value: 1, label: '待支付' },
  { value: 2, label: '已支付' },
  { value: 3, label: '已取消' },
  { value: 4, label: '已发货' },
  { value: 5, label: '已完成' },
  { value: 6, label: '已关闭' }
]

// 支付方式列表
const payMethods = [
  { value: 1, label: '微信支付' },
  { value: 2, label: '支付宝' },
  { value: 3, label: '银行卡' }
]

// 加载状态
const loading = reactive({
  orderList: false,
  statistics: false,
  today: false,
  month: false,
  distribution: false
})

// 测试结果
const testResults = ref<any>({})

// 测试订单列表
const testOrderList = async () => {
  loading.orderList = true
  try {
    const response = await getOrderList({
      page: 1,
      pageSize: 10
    })
    testResults.value.orderList = response
    ElMessage.success('订单列表测试成功')
  } catch (error) {
    testResults.value.orderList = { error: error.message }
    ElMessage.error('订单列表测试失败')
  } finally {
    loading.orderList = false
  }
}

// 测试订单统计
const testOrderStatistics = async () => {
  loading.statistics = true
  try {
    const response = await getOrderStatisticsData()
    testResults.value.statistics = response
    ElMessage.success('订单统计测试成功')
  } catch (error) {
    testResults.value.statistics = { error: error.message }
    ElMessage.error('订单统计测试失败')
  } finally {
    loading.statistics = false
  }
}

// 测试今日统计
const testTodayStatistics = async () => {
  loading.today = true
  try {
    const response = await getTodayStatistics()
    testResults.value.today = response
    ElMessage.success('今日统计测试成功')
  } catch (error) {
    testResults.value.today = { error: error.message }
    ElMessage.error('今日统计测试失败')
  } finally {
    loading.today = false
  }
}

// 测试本月统计
const testMonthStatistics = async () => {
  loading.month = true
  try {
    const response = await getMonthStatistics()
    testResults.value.month = response
    ElMessage.success('本月统计测试成功')
  } catch (error) {
    testResults.value.month = { error: error.message }
    ElMessage.error('本月统计测试失败')
  } finally {
    loading.month = false
  }
}

// 测试状态分布
const testStatusDistribution = async () => {
  loading.distribution = true
  try {
    const response = await getStatusDistribution()
    testResults.value.distribution = response
    ElMessage.success('状态分布测试成功')
  } catch (error) {
    testResults.value.distribution = { error: error.message }
    ElMessage.error('状态分布测试失败')
  } finally {
    loading.distribution = false
  }
}
</script>

<style scoped lang="scss">
.order-test-page {
  padding: 20px;
  
  .test-sections {
    .test-section {
      margin-bottom: 20px;
      
      .status-tests {
        .status-item {
          display: flex;
          align-items: center;
          margin-bottom: 10px;
          
          .el-tag {
            margin-right: 10px;
            min-width: 80px;
          }
          
          .status-info {
            color: #666;
            font-size: 14px;
          }
        }
      }
      
      .payment-tests {
        .payment-item {
          display: flex;
          align-items: center;
          margin-bottom: 10px;
          
          .payment-code {
            font-weight: bold;
            margin-right: 10px;
            min-width: 30px;
          }
          
          .payment-name {
            color: #666;
          }
        }
      }
      
      .api-tests {
        .el-button {
          margin-right: 10px;
          margin-bottom: 10px;
        }
      }
      
      .test-results {
        pre {
          background: #f5f5f5;
          padding: 15px;
          border-radius: 4px;
          max-height: 400px;
          overflow-y: auto;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
