// 声明模块，解决 TypeScript 模块查找问题
declare module '@/api/category' {
  // 获取分类树
  export function getCategoryTree(): Promise<any>

  // 添加分类
  export function addCategory(data: any): Promise<any>

  // 更新分类
  export function updateCategory(id: number, data: any): Promise<any>

  // 删除分类
  export function deleteCategory(id: number): Promise<any>
}

declare module '@/api/sellerAdmin' {
  // 商家信息接口
  export interface Seller {
    id: number
    accountName: string
    gender: string
    phone: string
    email: string
    accountStatus: number
    photoUrl: string
    shopName: string
    companyName: string
    businessLicense: string
    licenseValidity: string
    companyIntro?: string
    contactPerson: string
    contactPhone: string
    province: string
    city: string
    district: string
    addressDetail: string
    createTime: string
    updateTime: string
    licenseUrl?: string
    warehouseUrl1?: string
    warehouseUrl2?: string
    password?: string
    lastLoginTime?: string
    verificationCode?: string
    roles?: any[]
  }

  // 获取所有商家信息
  export function getAllSellers(): Promise<any>

  // 获取指定ID的商家信息
  export function getSellerById(id: number): Promise<any>

  // 更新商家信息
  export function updateSeller(seller: Seller): Promise<any>

  // 删除指定ID的商家
  export function deleteSeller(id: number): Promise<any>

  // 更新商家审核状态
  export function updateSellerStatus(id: number, status: number, reason?: string): Promise<any>
}

declare module '@/utils/auth' {
  export function getToken(): string
}

declare module '@/utils/validate' {
  export function isExternal(path: string): boolean
}

declare module '@/utils/modal' {
  export function msgSuccess(msg: string): void
  export function msgError(msg: string): void
  export function msgInfo(msg: string): void
  export function msgWarning(msg: string): void
  export function alert(content: string, title?: string, callback?: Function): void
  export function confirm(content: string, title?: string, callback?: Function): void
}
