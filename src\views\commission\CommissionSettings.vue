<template>
  <div class="commission-settings-container">
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">佣金设置</h1>
        <div class="page-subtitle">设置系统默认佣金比例和规则</div>
      </div>
    </div>

    <el-row :gutter="20">
      <!-- 基础设置 -->
      <el-col :span="24" :lg="12">
        <el-card shadow="hover" class="settings-card">
          <template #header>
            <div class="card-header">
              <span>基础佣金设置</span>
              <el-button type="primary" @click="saveBasicSettings" :loading="savingBasic">
                保存设置
              </el-button>
            </div>
          </template>

          <el-form
            ref="basicFormRef"
            :model="basicSettings"
            :rules="basicRules"
            label-position="top"
            status-icon
          >
            <el-form-item label="商家招募默认佣金比例 (%)" prop="merchantDefaultRate">
              <el-input-number
                v-model="basicSettings.merchantDefaultRate"
                :min="0"
                :max="20"
                :precision="1"
                :step="0.5"
                style="width: 100%"
              />
              <div class="form-tip">商家招募团长的默认佣金比例，可为每个团长单独设置</div>
            </el-form-item>

            <el-form-item label="用户招募默认佣金比例 (%)" prop="userDefaultRate">
              <el-input-number
                v-model="basicSettings.userDefaultRate"
                :min="0"
                :max="20"
                :precision="1"
                :step="0.5"
                style="width: 100%"
              />
              <div class="form-tip">用户招募团长的默认佣金比例，可为每个团长单独设置</div>
            </el-form-item>

            <el-form-item label="佣金结算周期" prop="settlementCycle">
              <el-select
                v-model="basicSettings.settlementCycle"
                placeholder="请选择结算周期"
                style="width: 100%"
              >
                <el-option label="每周结算" value="weekly" />
                <el-option label="每月结算" value="monthly" />
                <el-option label="每季度结算" value="quarterly" />
              </el-select>
              <div class="form-tip">系统默认的佣金结算周期</div>
            </el-form-item>

            <el-form-item label="结算日" prop="settlementDay">
              <el-input-number
                v-model="basicSettings.settlementDay"
                :min="1"
                :max="28"
                style="width: 100%"
              />
              <div class="form-tip">每月的结算处理日期，建议设置为1-28之间的数字</div>
            </el-form-item>

            <el-form-item label="最低结算金额" prop="minSettlementAmount">
              <el-input-number
                v-model="basicSettings.minSettlementAmount"
                :min="0"
                :precision="2"
                :step="10"
                style="width: 100%"
              />
              <div class="form-tip">
                当团长累计佣金达到此金额时才会触发结算，设置为0表示无最低限制
              </div>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 规则设置 -->
      <el-col :span="24" :lg="12">
        <el-card shadow="hover" class="settings-card">
          <template #header>
            <div class="card-header">
              <span>佣金规则设置</span>
              <el-button type="primary" @click="saveRules" :loading="savingRules">
                保存规则
              </el-button>
            </div>
          </template>

          <el-form ref="rulesFormRef" :model="commissionRules" label-position="top" status-icon>
            <el-form-item label="计算方式">
              <el-radio-group v-model="commissionRules.calculationType">
                <el-radio label="order">按订单金额计算</el-radio>
                <el-radio label="profit">按利润计算</el-radio>
              </el-radio-group>
              <div class="form-tip">决定佣金基于订单总金额还是订单利润计算</div>
            </el-form-item>

            <el-form-item label="递增规则">
              <el-switch
                v-model="commissionRules.enableTieredRates"
                @change="handleTieredRatesChange"
              />
              <div class="form-tip">启用后，可以设置不同业绩等级的佣金比例</div>
            </el-form-item>

            <template v-if="commissionRules.enableTieredRates">
              <div class="tiered-rates-header">
                <span>业绩等级佣金设置</span>
                <el-button type="primary" link @click="addTier">
                  <el-icon><Plus /></el-icon>添加等级
                </el-button>
              </div>

              <div v-for="(tier, index) in commissionRules.tiers" :key="index" class="tier-item">
                <div class="tier-header">
                  <span class="tier-title">等级 {{ index + 1 }}</span>
                  <el-button
                    type="danger"
                    link
                    @click="removeTier(index)"
                    :disabled="commissionRules.tiers.length <= 1"
                  >
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>

                <el-row :gutter="12">
                  <el-col :span="8">
                    <el-form-item :label="`${index === 0 ? '起始' : '最低'}金额`">
                      <el-input-number
                        v-model="tier.minAmount"
                        :min="0"
                        :precision="2"
                        :step="1000"
                        style="width: 100%"
                        :disabled="index === 0"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="最高金额">
                      <el-input-number
                        v-model="tier.maxAmount"
                        :min="tier.minAmount"
                        :precision="2"
                        :step="1000"
                        style="width: 100%"
                        :disabled="index === commissionRules.tiers.length - 1"
                        placeholder="无上限"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="佣金比例 (%)">
                      <el-input-number
                        v-model="tier.rate"
                        :min="0"
                        :max="20"
                        :precision="1"
                        :step="0.5"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </template>

            <el-divider />

            <el-form-item label="佣金说明">
              <el-input
                v-model="commissionRules.description"
                type="textarea"
                :rows="4"
                placeholder="在此输入佣金规则说明，将展示给团长"
              />
              <div class="form-tip">向团长说明佣金计算规则和结算方式</div>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue'

// 表单引用
const basicFormRef = ref<FormInstance>()
const rulesFormRef = ref<FormInstance>()

// 保存状态
const savingBasic = ref(false)
const savingRules = ref(false)

// 基础设置
const basicSettings = reactive({
  merchantDefaultRate: 5.0,
  userDefaultRate: 3.0,
  settlementCycle: 'monthly',
  settlementDay: 10,
  minSettlementAmount: 100,
})

// 基础设置验证规则
const basicRules = reactive<FormRules>({
  merchantDefaultRate: [{ required: true, message: '请设置商家招募默认佣金比例', trigger: 'blur' }],
  userDefaultRate: [{ required: true, message: '请设置用户招募默认佣金比例', trigger: 'blur' }],
  settlementCycle: [{ required: true, message: '请选择结算周期', trigger: 'change' }],
  settlementDay: [{ required: true, message: '请设置结算日', trigger: 'blur' }],
})

// 佣金规则设置
const commissionRules = reactive({
  calculationType: 'order',
  enableTieredRates: false,
  tiers: [
    {
      minAmount: 0,
      maxAmount: 10000,
      rate: 3.0,
    },
    {
      minAmount: 10000,
      maxAmount: 50000,
      rate: 4.0,
    },
    {
      minAmount: 50000,
      maxAmount: 0, // 0表示无上限
      rate: 5.0,
    },
  ],
  description:
    '佣金将按照实际成交订单金额计算，每月10号统一结算一次，系统自动结算至您的银行账户。当月佣金达到100元以上才会触发结算，否则累计到下月一起结算。',
})

// 处理梯度佣金设置变化
const handleTieredRatesChange = (val: boolean) => {
  if (val && commissionRules.tiers.length === 0) {
    // 如果启用梯度佣金但没有设置，则添加默认设置
    commissionRules.tiers = [
      {
        minAmount: 0,
        maxAmount: 10000,
        rate: 3.0,
      },
      {
        minAmount: 10000,
        maxAmount: 50000,
        rate: 4.0,
      },
      {
        minAmount: 50000,
        maxAmount: 0,
        rate: 5.0,
      },
    ]
  }
}

// 添加梯度等级
const addTier = () => {
  const lastTier = commissionRules.tiers[commissionRules.tiers.length - 1]
  // 设置新梯度的最小值为上一个梯度的最大值
  const newMinAmount = lastTier.maxAmount || 0

  // 将上一个梯度的最大值设为新梯度的最小值
  if (lastTier.maxAmount === 0) {
    lastTier.maxAmount = newMinAmount
  }

  // 添加新梯度
  commissionRules.tiers.push({
    minAmount: newMinAmount,
    maxAmount: 0, // 0表示无上限
    rate: lastTier.rate + 1.0, // 默认比上一级高1个百分点
  })
}

// 移除梯度等级
const removeTier = (index: number) => {
  if (commissionRules.tiers.length > 1) {
    commissionRules.tiers.splice(index, 1)

    // 更新梯度连接关系
    for (let i = 1; i < commissionRules.tiers.length; i++) {
      commissionRules.tiers[i].minAmount = commissionRules.tiers[i - 1].maxAmount || 0
    }

    // 确保最后一个梯度的最大值为0（无上限）
    commissionRules.tiers[commissionRules.tiers.length - 1].maxAmount = 0
  }
}

// 保存基础设置
const saveBasicSettings = async () => {
  if (!basicFormRef.value) return

  await basicFormRef.value.validate((valid) => {
    if (valid) {
      savingBasic.value = true

      // 这里应该调用API保存数据
      console.log('保存基础设置', basicSettings)

      // 模拟API请求
      setTimeout(() => {
        savingBasic.value = false
        ElMessage.success('基础佣金设置已保存！')
      }, 1000)
    }
  })
}

// 保存规则设置
const saveRules = () => {
  savingRules.value = true

  // 这里应该调用API保存数据
  console.log('保存佣金规则', commissionRules)

  // 模拟API请求
  setTimeout(() => {
    savingRules.value = false
    ElMessage.success('佣金规则设置已保存！')
  }, 1000)
}

// 初始加载数据
const loadSettings = () => {
  // 这里应该调用API获取设置
  console.log('加载佣金设置')

  // 模拟API请求
  setTimeout(() => {
    // 实际项目中会用API返回的数据更新本地状态
    console.log('设置加载完成')
  }, 500)
}

// 初始化
loadSettings()
</script>

<style scoped lang="scss">
.commission-settings-container {
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #eef2f5 100%);
  border-radius: 12px;
  min-height: calc(100vh - 40px);

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    .header-left {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        margin: 0 0 8px 0;
        background: linear-gradient(120deg, #3a7bd5, #2c5499);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .page-subtitle {
        font-size: 14px;
        color: #606266;
      }
    }
  }

  .settings-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    margin-bottom: 20px;

    &:hover {
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 16px;
      font-weight: 600;
    }

    .form-tip {
      font-size: 12px;
      color: #909399;
      margin-top: 4px;
    }

    .tiered-rates-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      font-weight: 600;
      color: #303133;
    }

    .tier-item {
      background-color: rgba(64, 158, 255, 0.05);
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 16px;

      .tier-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .tier-title {
          font-weight: 600;
          color: #409eff;
        }
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .commission-settings-container {
    padding: 16px;
  }
}
</style>
