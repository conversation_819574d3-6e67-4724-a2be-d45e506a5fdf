<template>
  <div class="order-completed-container">
    <div class="page-header">
      <h2>已完成订单</h2>
      <p>查看已完成的订单记录</p>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="search-section">
      <el-card>
        <el-form :model="searchForm" inline>
          <el-form-item label="订单号">
            <el-input
              v-model="searchForm.orderNumber"
              placeholder="请输入订单号"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="买家ID">
            <el-input
              v-model="searchForm.buyerId"
              placeholder="请输入买家ID"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="完成时间">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch" :loading="loading">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 统计信息 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon completed">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ pagination.total }}</div>
                <div class="stats-label">已完成订单</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon sales">
                <el-icon><Money /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">¥{{ formatAmount(totalSales) }}</div>
                <div class="stats-label">总销售额</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon avg">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">¥{{ formatAmount(avgOrderAmount) }}</div>
                <div class="stats-label">平均订单金额</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 订单列表 -->
    <div class="table-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>已完成订单列表</span>
            <div class="header-actions">
              <el-button @click="handleExport" :loading="exportLoading">
                <el-icon><Download /></el-icon>
                导出
              </el-button>
              <el-button @click="handleRefresh" :loading="loading">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </div>
        </template>

        <el-table
          :data="orderList"
          v-loading="loading"
          stripe
          style="width: 100%"
          @selection-change="handleSelectionChange"
          class="completed-table"
        >
          <el-table-column type="selection" width="55" fixed="left" />
          <el-table-column prop="number" label="订单号" min-width="180" fixed="left" show-overflow-tooltip />
          <el-table-column prop="buyerId" label="买家ID" width="100" />
          <el-table-column prop="amount" label="订单金额" width="120" sortable>
            <template #default="{ row }">
              <span class="amount">¥{{ formatAmount(row.amount) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="orderTime" label="下单时间" width="160" sortable>
            <template #default="{ row }">
              {{ formatDateTime(row.orderTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="payTime" label="支付时间" width="160" sortable>
            <template #default="{ row }">
              {{ formatDateTime(row.payTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="completeTime" label="完成时间" width="160" sortable>
            <template #default="{ row }">
              {{ formatDateTime(row.completeTime) }}
            </template>
          </el-table-column>
          <el-table-column label="处理时长" width="120">
            <template #default="{ row }">
              <span class="duration">{{ getProcessingDuration(row.orderTime, row.completeTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="payMethod" label="支付方式" width="120">
            <template #default="{ row }">
              {{ getPayMethodText(row.payMethod) }}
            </template>
          </el-table-column>
          <el-table-column prop="logisticsCompany" label="物流公司" width="120" show-overflow-tooltip>
            <template #default="{ row }">
              {{ row.logisticsCompany || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="trackingNumber" label="快递单号" width="140" show-overflow-tooltip>
            <template #default="{ row }">
              {{ row.trackingNumber || '-' }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="160" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button size="small" @click="handleViewDetail(row)">
                  详情
                </el-button>
                <el-button size="small" type="info" @click="handleRefund(row)">
                  退款
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.size"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 订单详情对话框 -->
    <OrderDetailDialog
      v-model="detailDialogVisible"
      :order-id="selectedOrderId"
      @refresh="handleRefresh"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Search, 
  Refresh, 
  Download, 
  CircleCheck, 
  Money, 
  TrendCharts 
} from '@element-plus/icons-vue'
import OrderDetailDialog from './components/OrderDetailDialog.vue'
import { getOrderList } from '@/api/order'
import type { OrderVO, OrderPageQueryDTO } from '@/types/order'

// 搜索表单
const searchForm = reactive<OrderPageQueryDTO>({
  number: '',
  buyerId: undefined,
  status: 5, // 已完成状态（根据新的状态码）
  beginTime: '',
  endTime: '',
  dateRange: null,
  page: 1,
  pageSize: 20
})

// 订单列表数据
const orderList = ref<OrderVO[]>([])
const loading = ref(false)
const exportLoading = ref(false)
const selectedOrders = ref<OrderVO[]>([])

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 对话框状态
const detailDialogVisible = ref(false)
const selectedOrderId = ref<number | null>(null)

// 计算统计数据
const totalSales = computed(() => {
  return orderList.value.reduce((sum, order) => sum + (order.amount || 0), 0)
})

const avgOrderAmount = computed(() => {
  if (orderList.value.length === 0) return 0
  return totalSales.value / orderList.value.length
})

// 获取订单列表
const fetchOrderList = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      page: pagination.page,
      pageSize: pagination.size
    }
    
    // 处理日期范围
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.beginTime = searchForm.dateRange[0]
      params.endTime = searchForm.dateRange[1]
    }
    
    const response = await getOrderList(params)
    if (response.code === 1) {
      orderList.value = response.data.list || []
      pagination.total = response.data.total || 0
    } else {
      ElMessage.error(response.msg || '获取订单列表失败')
    }
  } catch (error) {
    console.error('获取订单列表失败:', error)
    ElMessage.error('获取订单列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchOrderList()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    number: '',
    buyerId: null,
    status: 7, // 已完成状态（根据新的状态码）
    beginTime: '',
    endTime: '',
    dateRange: null
  })
  pagination.page = 1
  fetchOrderList()
}

// 刷新
const handleRefresh = () => {
  fetchOrderList()
}

// 导出
const handleExport = () => {
  exportLoading.value = true
  // 模拟导出过程
  setTimeout(() => {
    exportLoading.value = false
    ElMessage.success('导出成功')
  }, 2000)
}

// 选择变化
const handleSelectionChange = (selection: OrderVO[]) => {
  selectedOrders.value = selection
}

// 分页变化
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchOrderList()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchOrderList()
}

// 查看详情
const handleViewDetail = (order: OrderVO) => {
  selectedOrderId.value = order.id
  detailDialogVisible.value = true
}

// 退款处理
const handleRefund = (order: OrderVO) => {
  // TODO: 实现退款功能
  ElMessage.info('暂不支持商家主动退款')
}

// 获取支付方式文本 - 根据API文档更新
const getPayMethodText = (payMethod: number) => {
  const payMethodMap: Record<number, string> = {
    1: '微信支付',
    2: '支付宝支付',
    3: '信用卡支付',
    4: '货到付款'
  }
  return payMethodMap[payMethod] || '未知'
}

// 获取处理时长
const getProcessingDuration = (orderTime: string, checkoutTime: string) => {
  if (!orderTime || !checkoutTime) return '-'
  
  const start = new Date(orderTime)
  const end = new Date(checkoutTime)
  const diffMs = end.getTime() - start.getTime()
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffHours / 24)
  
  if (diffDays > 0) {
    return `${diffDays}天${diffHours % 24}小时`
  } else {
    return `${diffHours}小时`
  }
}

// 格式化金额
const formatAmount = (amount: number) => {
  if (!amount) return '0.00'
  return amount.toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

onMounted(() => {
  fetchOrderList()
})
</script>

<style scoped lang="scss">
.order-completed-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;

  h2 {
    margin: 0 0 8px 0;
    color: #303133;
    font-size: 24px;
    font-weight: 600;
  }

  p {
    margin: 0;
    color: #606266;
    font-size: 14px;
  }
}

.search-section {
  margin-bottom: 20px;
}

.stats-section {
  margin-bottom: 20px;
}

.stats-card .stats-content {
  display: flex;
  align-items: center;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;

  .el-icon {
    font-size: 24px;
    color: white;
  }

  &.completed {
    background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
  }

  &.sales {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  }

  &.avg {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }
}

.stats-info {
  .stats-value {
    font-size: 28px;
    font-weight: 600;
    color: #303133;
    line-height: 1;
  }

  .stats-label {
    font-size: 14px;
    color: #909399;
    margin-top: 4px;
  }
}

.table-section .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.completed-table {
  .amount {
    color: #f56c6c;
    font-weight: 600;
  }

  .duration {
    color: #67c23a;
    font-size: 12px;
    font-weight: 600;
  }

  .action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;

    .el-button {
      margin: 0;
      padding: 4px 8px;
      font-size: 12px;
    }
  }
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

// 响应式设计
@media (max-width: 768px) {
  .order-completed-container {
    padding: 10px;
  }

  .stats-section .el-col {
    margin-bottom: 15px;
  }

  .table-section .card-header {
    flex-direction: column;
    gap: 10px;
  }

  .header-actions {
    width: 100%;
    justify-content: center;
  }

  .completed-table .action-buttons {
    flex-direction: column;

    .el-button {
      width: 100%;
      margin-bottom: 2px;
    }
  }
}

@media (max-width: 480px) {
  .stats-card .stats-content {
    flex-direction: column;
    text-align: center;
  }

  .stats-icon {
    margin-bottom: 10px;
  }
}
</style>
