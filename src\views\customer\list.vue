<template>
  <div class="page-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="page-header">
          <h2 class="page-title">客户管理</h2>
          <div class="page-actions">
            <el-button type="primary" @click="handleAdd">
              <el-icon><Plus /></el-icon>
              <span>添加客户</span>
            </el-button>
            <el-button>
              <el-icon><Download /></el-icon>
              <span>导出客户</span>
            </el-button>
          </div>
        </div>
      </template>

      <div class="page-content">
        <!-- 搜索过滤区域 -->
        <div class="filter-container">
          <el-form :inline="true" :model="filterForm" class="filter-form">
            <el-form-item label="客户名称">
              <el-input v-model="filterForm.name" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="手机号码">
              <el-input v-model="filterForm.phone" placeholder="请输入手机号码" clearable />
            </el-form-item>
            <el-form-item label="会员等级">
              <el-select v-model="filterForm.level" placeholder="请选择会员等级" clearable>
                <el-option label="全部等级" value="" />
                <el-option label="普通会员" value="normal" />
                <el-option label="银卡会员" value="silver" />
                <el-option label="金卡会员" value="gold" />
                <el-option label="钻石会员" value="diamond" />
              </el-select>
            </el-form-item>
            <el-form-item label="注册时间">
              <el-date-picker
                v-model="filterForm.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :shortcuts="dateShortcuts"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">搜索</el-button>
              <el-button @click="resetFilter">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 表格区域 -->
        <el-table
          :data="tableData"
          style="width: 100%"
          v-loading="loading"
          border
          :header-cell-style="{ background: '#f5f7fa' }"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column prop="id" label="客户ID" width="80" />
          <el-table-column label="客户信息" width="200">
            <template #default="scope">
              <div class="customer-info">
                <el-avatar :size="40" :src="scope.row.avatar"></el-avatar>
                <div class="customer-detail">
                  <div class="customer-name">{{ scope.row.name }}</div>
                  <div class="customer-phone">{{ scope.row.phone }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="会员等级" width="120">
            <template #default="scope">
              <div class="membership-level">
                <el-tag :type="getMemberLevelType(scope.row.level)" effect="plain">
                  {{ getMemberLevelText(scope.row.level) }}
                </el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="totalSpent" label="累计消费" width="120" sortable>
            <template #default="scope">
              <span class="amount">¥{{ Number(scope.row.totalSpent).toFixed(2) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="orderCount" label="订单数" width="100" sortable />
          <el-table-column prop="lastOrderTime" label="最近下单" width="150" sortable />
          <el-table-column prop="registerTime" label="注册时间" width="150" sortable />
          <el-table-column prop="source" label="来源渠道" min-width="120" />
          <el-table-column label="操作" width="180" fixed="right">
            <template #default="scope">
              <el-button size="small" type="primary" text @click="handleView(scope.row)">
                查看
              </el-button>
              <el-button size="small" type="primary" text @click="handleEdit(scope.row)">
                编辑
              </el-button>
              <el-dropdown trigger="click">
                <el-button size="small" text>
                  更多<el-icon><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="handleAddress(scope.row)">
                      <el-icon><Location /></el-icon>地址管理
                    </el-dropdown-item>
                    <el-dropdown-item @click="handleOrders(scope.row)">
                      <el-icon><ShoppingBag /></el-icon>查看订单
                    </el-dropdown-item>
                    <el-dropdown-item divided @click="handleDelete(scope.row)">
                      <el-icon><Delete /></el-icon>删除客户
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页区域 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 客户编辑/添加对话框 -->
    <el-dialog
      :title="dialogType === 'add' ? '添加客户' : '编辑客户'"
      v-model="dialogVisible"
      width="500px"
      destroy-on-close
    >
      <el-form :model="form" label-width="100px" :rules="rules" ref="customerForm">
        <el-form-item label="客户头像">
          <el-upload
            class="avatar-uploader"
            action="#"
            :auto-upload="false"
            :show-file-list="false"
            :on-change="handleAvatarChange"
          >
            <img v-if="form.avatar" :src="form.avatar" class="avatar" />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item>
        <el-form-item label="客户姓名" prop="name">
          <el-input v-model="form.name" placeholder="请输入客户姓名" />
        </el-form-item>
        <el-form-item label="手机号码" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入手机号码" />
        </el-form-item>
        <el-form-item label="电子邮箱" prop="email">
          <el-input v-model="form.email" placeholder="请输入电子邮箱" />
        </el-form-item>
        <el-form-item label="会员等级">
          <el-select v-model="form.level" placeholder="请选择会员等级" style="width: 100%">
            <el-option label="普通会员" value="normal" />
            <el-option label="银卡会员" value="silver" />
            <el-option label="金卡会员" value="gold" />
            <el-option label="钻石会员" value="diamond" />
          </el-select>
        </el-form-item>
        <el-form-item label="生日">
          <el-date-picker
            v-model="form.birthday"
            type="date"
            placeholder="请选择生日"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="性别">
          <el-radio-group v-model="form.gender">
            <el-radio label="male">男</el-radio>
            <el-radio label="female">女</el-radio>
            <el-radio label="other">其他</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注信息" rows="3" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitLoading">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Download, Delete, ArrowDown, Location, ShoppingBag } from '@element-plus/icons-vue'

// 筛选表单
const filterForm = reactive({
  name: '',
  phone: '',
  level: '',
  dateRange: [],
})

// 时间快捷选项
const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    },
  },
]

// 重置筛选条件
const resetFilter = () => {
  filterForm.name = ''
  filterForm.phone = ''
  filterForm.level = ''
  filterForm.dateRange = []
  handleSearch()
}

// 表格数据
const tableData = ref([
  {
    id: 10001,
    name: '张三',
    phone: '13812345678',
    avatar: 'https://placeholder.pics/svg/50/DEDEDE/555555/头像',
    level: 'gold',
    totalSpent: 5890.0,
    orderCount: 12,
    lastOrderTime: '2023-10-10 15:23:45',
    registerTime: '2022-05-15 09:34:12',
    source: '官网注册',
    email: '<EMAIL>',
    birthday: '1990-01-01',
    gender: 'male',
    remark: '重要客户',
  },
  {
    id: 10002,
    name: '李四',
    phone: '13998765432',
    avatar: 'https://placeholder.pics/svg/50/DEDEDE/555555/头像',
    level: 'silver',
    totalSpent: 1253.5,
    orderCount: 5,
    lastOrderTime: '2023-10-08 10:12:38',
    registerTime: '2022-08-20 14:45:23',
    source: '微信小程序',
    email: '<EMAIL>',
    birthday: '1985-03-15',
    gender: 'male',
    remark: '',
  },
  {
    id: 10003,
    name: '王五',
    phone: '18612345678',
    avatar: 'https://placeholder.pics/svg/50/DEDEDE/555555/头像',
    level: 'diamond',
    totalSpent: 12328.0,
    orderCount: 25,
    lastOrderTime: '2023-10-12 16:45:22',
    registerTime: '2021-12-01 11:34:56',
    source: '官网注册',
    email: '<EMAIL>',
    birthday: '1988-07-22',
    gender: 'male',
    remark: 'VIP客户',
  },
  {
    id: 10004,
    name: '赵丽',
    phone: '17798765432',
    avatar: 'https://placeholder.pics/svg/50/DEDEDE/555555/头像',
    level: 'normal',
    totalSpent: 289.9,
    orderCount: 2,
    lastOrderTime: '2023-09-25 09:34:12',
    registerTime: '2023-09-15 16:23:45',
    source: '手机APP',
    email: '<EMAIL>',
    birthday: '1992-11-11',
    gender: 'female',
    remark: '新客户',
  },
  {
    id: 10005,
    name: '孙琪',
    phone: '13612345678',
    avatar: 'https://placeholder.pics/svg/50/DEDEDE/555555/头像',
    level: 'gold',
    totalSpent: 6456.0,
    orderCount: 15,
    lastOrderTime: '2023-10-09 18:23:51',
    registerTime: '2022-01-15 10:12:34',
    source: '线下门店',
    email: '<EMAIL>',
    birthday: '1980-05-05',
    gender: 'female',
    remark: '',
  },
])

// 表单数据和验证规则
const customerForm = ref()
const dialogType = ref('add') // 'add' 或 'edit'
const dialogVisible = ref(false)
const submitLoading = ref(false)
const form = reactive({
  id: 0,
  name: '',
  phone: '',
  avatar: '',
  email: '',
  level: 'normal',
  birthday: '',
  gender: 'male',
  remark: '',
})

const rules = {
  name: [
    { required: true, message: '请输入客户姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' },
  ],
  phone: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' },
  ],
  email: [
    { required: false, message: '请输入电子邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' },
  ],
}

// 分页相关
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)

// 获取会员等级标签类型
const getMemberLevelType = (level: string) => {
  switch (level) {
    case 'normal':
      return 'info'
    case 'silver':
      return ''
    case 'gold':
      return 'warning'
    case 'diamond':
      return 'success'
    default:
      return 'info'
  }
}

// 获取会员等级文本
const getMemberLevelText = (level: string) => {
  switch (level) {
    case 'normal':
      return '普通会员'
    case 'silver':
      return '银卡会员'
    case 'gold':
      return '金卡会员'
    case 'diamond':
      return '钻石会员'
    default:
      return '普通会员'
  }
}

// 处理搜索
const handleSearch = () => {
  loading.value = true
  currentPage.value = 1
  // 模拟API请求
  setTimeout(() => {
    // 这里应该是实际的API请求，根据筛选条件获取数据
    loading.value = false
  }, 500)
}

// 添加客户
const handleAdd = () => {
  dialogType.value = 'add'
  // 重置表单
  form.id = 0
  form.name = ''
  form.phone = ''
  form.avatar = ''
  form.email = ''
  form.level = 'normal'
  form.birthday = ''
  form.gender = 'male'
  form.remark = ''

  dialogVisible.value = true
}

// 查看客户详情
const handleView = (row: any) => {
  console.log('查看客户:', row)
  // 跳转到客户详情页面
}

// 编辑客户
const handleEdit = (row: any) => {
  dialogType.value = 'edit'

  // 填充表单数据
  form.id = row.id
  form.name = row.name
  form.phone = row.phone
  form.avatar = row.avatar
  form.email = row.email
  form.level = row.level
  form.birthday = row.birthday
  form.gender = row.gender
  form.remark = row.remark

  dialogVisible.value = true
}

// 地址管理
const handleAddress = (row: any) => {
  console.log('管理地址:', row)
  ElMessage('地址管理功能开发中')
  // 跳转到地址管理页面或打开地址管理对话框
}

// 查看订单
const handleOrders = (row: any) => {
  console.log('查看订单:', row)
  ElMessage('即将跳转到订单列表')
  // 跳转到该客户的订单列表页面
}

// 处理删除
const handleDelete = (row: any) => {
  ElMessageBox.confirm(`确定要删除客户 ${row.name} 吗?`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      // 模拟API请求
      loading.value = true
      setTimeout(() => {
        tableData.value = tableData.value.filter((item) => item.id !== row.id)
        loading.value = false
        ElMessage.success('删除成功')
      }, 500)
    })
    .catch(() => {
      // 用户取消操作
    })
}

// 提交表单
const submitForm = () => {
  customerForm.value.validate((valid: boolean) => {
    if (valid) {
      submitLoading.value = true

      // 模拟API请求
      setTimeout(() => {
        if (dialogType.value === 'add') {
          // 添加新客户
          const newCustomer = {
            ...form,
            id: Date.now(),
            totalSpent: 0,
            orderCount: 0,
            lastOrderTime: '-',
            registerTime: new Date().toLocaleString(),
            source: '手动添加',
          }
          tableData.value.unshift(newCustomer)
          ElMessage.success('添加成功')
        } else {
          // 更新客户信息
          const index = tableData.value.findIndex((item) => item.id === form.id)
          if (index !== -1) {
            tableData.value[index] = {
              ...tableData.value[index],
              name: form.name,
              phone: form.phone,
              avatar: form.avatar,
              email: form.email,
              level: form.level,
              birthday: form.birthday,
              gender: form.gender,
              remark: form.remark,
            }
            ElMessage.success('更新成功')
          }
        }

        submitLoading.value = false
        dialogVisible.value = false
      }, 500)
    } else {
      return false
    }
  })
}

// 处理头像变更
const handleAvatarChange = (file: any) => {
  // 这里应该是实际的上传逻辑
  // 为了演示，我们只是设置本地预览
  const reader = new FileReader()
  reader.onload = (e: any) => {
    form.avatar = e.target.result
  }
  reader.readAsDataURL(file.raw)
}

// 处理分页大小变更
const handleSizeChange = (val: number) => {
  pageSize.value = val
  handleSearch()
}

// 处理页码变更
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  handleSearch()
}

// 页面加载时获取数据
onMounted(() => {
  handleSearch()
})
</script>

<style scoped lang="scss">
.page-container {
  padding: 16px;

  .page-card {
    :deep(.el-card__header) {
      padding: 16px 20px;
    }

    :deep(.el-card__body) {
      padding: 20px;
    }
  }

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .page-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }

    .page-actions {
      display: flex;
      gap: 12px;
    }
  }

  .filter-container {
    margin-bottom: 20px;
    padding: 16px;
    background-color: #f9f9f9;
    border-radius: 4px;

    .filter-form {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;

      :deep(.el-form-item) {
        margin-bottom: 0;
        margin-right: 16px;
      }

      :deep(.el-date-editor--daterange) {
        width: 260px;
      }
    }
  }

  .customer-info {
    display: flex;
    align-items: center;

    .customer-detail {
      margin-left: 8px;

      .customer-name {
        font-size: 14px;
        font-weight: 500;
        line-height: 1.2;
      }

      .customer-phone {
        font-size: 12px;
        color: #909399;
      }
    }
  }

  .amount {
    font-weight: 600;
    color: #303133;
  }

  .membership-level {
    .el-tag {
      font-weight: 500;
    }
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }

  .avatar-uploader {
    display: flex;
    justify-content: center;

    .avatar {
      width: 100px;
      height: 100px;
      border-radius: 50%;
      object-fit: cover;
    }

    .avatar-uploader-icon {
      width: 100px;
      height: 100px;
      border: 1px dashed #d9d9d9;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 28px;
      color: #8c939d;
      background-color: #f5f7fa;

      &:hover {
        border-color: #409eff;
        color: #409eff;
      }
    }
  }
}

// 响应式调整
@media screen and (max-width: 768px) {
  .page-container {
    .filter-container {
      .filter-form {
        flex-direction: column;

        :deep(.el-form-item) {
          margin-right: 0;
          margin-bottom: 10px;
          width: 100%;
        }

        :deep(.el-date-editor--daterange) {
          width: 100%;
        }
      }
    }
  }
}
</style>
