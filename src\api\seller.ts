import request from '@/utils/request'

// 登录参数类型定义
export interface SellerLoginDTO {
  id?: number
  accountName: string
  password: string
  email: string
  code: string
}

// 注册参数类型定义
export interface SellerRegisterDTO {
  email: string
  verificationCode: string
  password: string
  shopName: string
  companyName: string
  businessLicense: string
  licenseValidity: string
  companyIntro?: string
  contactPerson: string
  contactPhone: string
  province: string
  city: string
  district: string
  addressDetail: string
  accountName: string
  phone: string
  gender: string
  accountStatus: string
  license: File
  warehouseFiles1?: File
  warehouseFiles2?: File
  warehouseFiles3?: File
}

// 商家详细信息接口
export interface SellerDetailInfo {
  id: number
  accountName: string
  gender: string
  phone: string
  email: string
  accountStatus: number
  photoUrl: string
  shopName?: string
  companyName?: string
}

// 密码修改参数类型定义
export interface PasswordUpdateDTO {
  id: number
  oldPassword: string
  newPassword: string
}

// 登录接口
export function login(data: SellerLoginDTO) {
  return request({
    url: '/seller/login',
    method: 'post',
    data,
  })
}

// 注册接口
export function register(data: FormData) {
  console.log('注册数据：',data);
  
  return request({
    url: '/seller/register',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}

// 获取当前登录卖家信息
export function getSellerInfo() {
  return request({
    url: '/seller/info',
    method: 'get',
  })
}

// 获取指定ID的卖家详细信息
export function getSellerById(id: number) {
  return request({
    url: `/sellers/id/${id}`,
    method: 'get',
  })
}

// 登出接口
export function logout() {
  return request({
    url: '/seller/logout',
    method: 'post',
  })
}

// 更新卖家信息
export function updateSellerInfo(data: any) {
  return request({
    url: '/seller/update',
    method: 'put',
    data,
  })
}

// 修改密码
export function updatePassword(data: PasswordUpdateDTO) {
  return request({
    url: '/seller/updatePassword',
    method: 'put',
    data,
  })
}

// 获取统计数据
export function getStatistics() {
  return request({
    url: '/seller/statistics',
    method: 'get',
  })
}

// 发送验证码接口
export function sendVerificationCode(address: string) {
  return request({
    url: '/seller/register/sendCode',
    method: 'post',
    data: { address },
  })
}

// 邮箱验证码登录接口
export function loginByEmail(data: SellerLoginDTO) {
  return request({
    url: '/seller/sendEmail',
    method: 'post',
    data,
  })
}

// 邮箱验证码确认登录接口
export function emailLogin(data: SellerLoginDTO) {
  return request({
    url: '/seller/EmailLogin',
    method: 'post',
    data,
  })
}
