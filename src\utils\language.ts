// 语言服务
export type LanguageType = "english" | "chinese_simplified" | "chinese_traditional";

// 语言显示名称映射
export const LANGUAGE_NAMES: Record<LanguageType, string> = {
  english: 'English',
  chinese_simplified: '简体中文',
  chinese_traditional: '繁體中文'
};

// 统一的localStorage键名
export const LANGUAGE_STORAGE_KEY = 'preferredLanguage';

/**
 * 获取当前语言
 */
export function getCurrentLanguage(): LanguageType {
  const savedLanguage = localStorage.getItem(LANGUAGE_STORAGE_KEY);
  if (savedLanguage && (
    savedLanguage === 'english' || 
    savedLanguage === 'chinese_simplified' || 
    savedLanguage === 'chinese_traditional'
  )) {
    return savedLanguage as LanguageType;
  }
  
  // 默认语言
  return 'chinese_simplified';
}

/**
 * 获取语言显示名称
 */
export function getLanguageDisplayName(lang: LanguageType): string {
  return LANGUAGE_NAMES[lang];
}

/**
 * 切换语言
 */
export function changeLanguage(lang: LanguageType): boolean {
  // 保存到localStorage
  localStorage.setItem(LANGUAGE_STORAGE_KEY, lang);
  
  // 设置HTML属性，方便全局样式和组件判断
  document.documentElement.setAttribute('data-language', lang);
  
  // 应用翻译
  if (window.translate && typeof window.translate.changeLanguage === 'function') {
    // 确保清理旧翻译节点，避免重复翻译
    const nodes = document.querySelectorAll('.translate-src');
    nodes.forEach(node => {
      if (node.parentNode) {
        node.parentNode.removeChild(node);
      }
    });
    
    // 应用翻译
    window.translate.changeLanguage(lang);
    
    // 发送全局事件
    window.dispatchEvent(new CustomEvent('language-changed', { 
      detail: { language: lang } 
    }));
    
    return true;
  }
  
  return false;
}

/**
 * 初始化或重新应用翻译
 */
export function applyTranslation(): void {
  const lang = getCurrentLanguage();
  
  if (window.translate && typeof window.translate.changeLanguage === 'function') {
    // 应用保存的语言
    window.translate.changeLanguage(lang);
  }
  
  // 设置HTML属性
  document.documentElement.setAttribute('data-language', lang);
}

/**
 * 在数据加载后手动触发翻译
 * 用于异步加载数据后，确保新加载的内容被翻译
 * @param selector 可选的CSS选择器，用于限定翻译范围
 * @param delay 可选的延迟时间(毫秒)，默认为100ms
 */
export function refreshTranslation(selector?: string, delay: number = 100): Promise<void> {
  return new Promise((resolve) => {
    setTimeout(() => {
      if (window.translate && typeof window.translate.changeLanguage === 'function') {
        const currentLang = getCurrentLanguage();
        console.log(`手动刷新翻译 (${selector ? '选择器:' + selector : '全局'})，当前语言:`, currentLang);
        
        // 如果提供了选择器，尝试只刷新指定区域
        if (selector) {
          try {
            // 先清理选择器范围内的翻译节点
            const container = document.querySelector(selector);
            if (container) {
              const oldNodes = container.querySelectorAll('.translate-src');
              oldNodes.forEach(node => {
                if (node.parentNode) {
                  node.parentNode.removeChild(node);
                }
              });
            }
          } catch (e) {
            console.warn('清理选择器范围内的翻译节点失败:', e);
          }
        }
        
        // 应用翻译
        window.translate.changeLanguage(currentLang);
        resolve();
      } else {
        console.warn('翻译对象不可用，无法刷新翻译');
        resolve();
      }
    }, delay);
  });
}

// 使用any类型暂时绕过类型冲突
declare global {
  interface Window {
    // 使用any类型避免类型冲突
    translate: any;
    appLanguage?: {
      changeLanguage: (language: string) => void;
      getCurrentLanguage: () => string | null;
      refreshTranslation: () => void;
    };
  }
} 