// API请求参数类型

export interface LoginParams {
  accountName: string
  password: string
}

export interface RegisterParams {
  email: string
  verificationCode: string
  password: string
  shopName: string
  companyName: string
  businessLicense: string
  licenseValidity: string
  companyIntro?: string
  contactPerson: string
  contactPhone: string
  province: string
  city: string
  district: string
  addressDetail: string
  accountName: string
  phone: string
  gender: string
  accountStatus: string
}

// API响应类型
export interface ApiResponse<T = any> {
  code: number
  msg: string | null
  data: T | null
}

export interface LoginResponseData {
  id: number
  token: string
  accountName: string
  accountType?: string
  expireTime?: number
}

export interface UserInfo {
  id: number
  accountName: string
  shopName: string
  companyName: string
  contactPerson: string
  phone: string
  email: string
  province: string
  city: string
  district: string
  addressDetail: string
  accountStatus: number
  createTime: string
  updateTime: string
}

export interface StatisticsData {
  orderCount: number
  productCount: number
  monthSales: number
  visitorCount: number
  pendingOrderCount: number
} 