<template>
  <div class="refund-pending">
    <el-card class="header-card">
      <template #header>
        <div class="card-header">
          <span>待审核退款</span>
          <div class="header-actions">
            <el-button type="primary" @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <!-- 统计信息 -->
      <div class="stats-info">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-number">{{ pendingCount }}</div>
              <div class="stat-label">待审核数量</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-number">¥{{ totalAmount.toFixed(2) }}</div>
              <div class="stat-label">待审核金额</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-number">{{ todayCount }}</div>
              <div class="stat-label">今日新增</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <template #header>
        <div class="table-header">
          <span>待审核列表</span>
          <div class="batch-actions" v-if="selectedRefunds.length > 0">
            <el-button type="success" size="small" @click="handleBatchApprove(2)">
              批量通过
            </el-button>
            <el-button type="danger" size="small" @click="handleBatchApprove(3)">
              批量拒绝
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="refundList"
        @selection-change="handleSelectionChange"
        stripe
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="refundNo" label="退款单号" width="180" />
        <el-table-column prop="orderNumber" label="订单号" width="180" />
        <el-table-column label="退款金额" width="120">
          <template #default="{ row }">
            <span class="amount">¥{{ row.refundAmount.toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="buyerName" label="申请人" width="120" />
        <el-table-column label="退款类型" width="100">
          <template #default="{ row }">
            <el-tag :type="row.refundType === 1 ? 'primary' : 'warning'" size="small">
              {{ getRefundTypeText(row.refundType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="refundReason" label="退款原因" width="200" show-overflow-tooltip />
        <el-table-column prop="createTime" label="申请时间" width="160" />
        <el-table-column label="等待时间" width="100">
          <template #default="{ row }">
            <span class="waiting-time">{{ getWaitingTime(row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleViewDetail(row)">
              详情
            </el-button>
            <el-button type="success" size="small" @click="handleApprove(row)">
              审核
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 退款详情对话框 -->
    <RefundDetailDialog
      v-model="detailDialogVisible"
      :refund-id="selectedRefundId"
      @refresh="handleRefresh"
      @approve="handleApproveFromDetail"
    />

    <!-- 退款审核对话框 -->
    <RefundApprovalDialog
      v-model="approvalDialogVisible"
      :refund="selectedRefund"
      @refresh="handleRefresh"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import {
  getPendingRefunds,
  batchApproveRefunds
} from '@/api/refund'
import type { RefundApplicationVO } from '@/types/refund'
import {
  getRefundTypeText
} from '@/types/refund'
import RefundDetailDialog from './components/RefundDetailDialog.vue'
import RefundApprovalDialog from './components/RefundApprovalDialog.vue'

// 数据状态
const loading = ref(false)
const refundList = ref<RefundApplicationVO[]>([])
const selectedRefunds = ref<RefundApplicationVO[]>([])

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 对话框状态
const detailDialogVisible = ref(false)
const approvalDialogVisible = ref(false)
const selectedRefundId = ref<number | null>(null)
const selectedRefund = ref<RefundApplicationVO | null>(null)

// 统计信息
const pendingCount = computed(() => refundList.value.length)
const totalAmount = computed(() => 
  refundList.value.reduce((sum, item) => sum + item.refundAmount, 0)
)
const todayCount = computed(() => {
  const today = new Date().toDateString()
  return refundList.value.filter(item => 
    new Date(item.createTime).toDateString() === today
  ).length
})

// 获取等待时间
const getWaitingTime = (createTime: string): string => {
  const now = new Date()
  const create = new Date(createTime)
  const diff = now.getTime() - create.getTime()
  const hours = Math.floor(diff / (1000 * 60 * 60))
  
  if (hours < 1) {
    const minutes = Math.floor(diff / (1000 * 60))
    return `${minutes}分钟`
  } else if (hours < 24) {
    return `${hours}小时`
  } else {
    const days = Math.floor(hours / 24)
    return `${days}天`
  }
}

// 获取待审核退款列表
const fetchPendingRefunds = async () => {
  loading.value = true
  try {
    const response = await getPendingRefunds(pagination.page, pagination.pageSize)
    if (response.code === 1) {
      refundList.value = response.data.list || []
      pagination.total = response.data.total || 0
    } else {
      ElMessage.error(response.msg || '获取待审核退款列表失败')
    }
  } catch (error) {
    console.error('获取待审核退款列表失败:', error)
    ElMessage.error('获取待审核退款列表失败')
  } finally {
    loading.value = false
  }
}

// 刷新
const handleRefresh = () => {
  fetchPendingRefunds()
}

// 分页变化
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
  fetchPendingRefunds()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchPendingRefunds()
}

// 选择变化
const handleSelectionChange = (selection: RefundApplicationVO[]) => {
  selectedRefunds.value = selection
}

// 查看详情
const handleViewDetail = (refund: RefundApplicationVO) => {
  selectedRefundId.value = refund.id
  detailDialogVisible.value = true
}

// 审核
const handleApprove = (refund: RefundApplicationVO) => {
  selectedRefund.value = refund
  approvalDialogVisible.value = true
}

// 从详情页面触发审核
const handleApproveFromDetail = (refund: RefundApplicationVO) => {
  selectedRefund.value = refund
  approvalDialogVisible.value = true
}

// 批量审核
const handleBatchApprove = async (approvalStatus: number) => {
  if (selectedRefunds.value.length === 0) {
    ElMessage.warning('请选择要审核的退款申请')
    return
  }

  const statusText = approvalStatus === 2 ? '通过' : '拒绝'
  try {
    await ElMessageBox.confirm(`确认批量${statusText}选中的 ${selectedRefunds.value.length} 个退款申请？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const refundIds = selectedRefunds.value.map(r => r.id)
    const response = await batchApproveRefunds(refundIds, approvalStatus)
    
    if (response.code === 1) {
      ElMessage.success(`批量${statusText}成功`)
      handleRefresh()
    } else {
      ElMessage.error(response.msg || `批量${statusText}失败`)
    }
  } catch (error) {
    console.error(`批量${statusText}失败:`, error)
  }
}

// 组件挂载
onMounted(() => {
  fetchPendingRefunds()
})
</script>

<style scoped lang="scss">
.refund-pending {
  padding: 20px;

  .header-card {
    margin-bottom: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-actions {
        display: flex;
        gap: 10px;
      }
    }

    .stats-info {
      .stat-item {
        text-align: center;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 8px;
        border-left: 4px solid #e6a23c;

        .stat-number {
          font-size: 24px;
          font-weight: bold;
          color: #e6a23c;
          margin-bottom: 5px;
        }

        .stat-label {
          font-size: 14px;
          color: #909399;
        }
      }
    }
  }

  .table-card {
    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .batch-actions {
        display: flex;
        gap: 10px;
      }
    }

    .amount {
      font-weight: bold;
      color: #f56c6c;
    }

    .waiting-time {
      color: #e6a23c;
      font-weight: 500;
    }

    .pagination-container {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .refund-pending {
    padding: 10px;

    .stats-info {
      .el-col {
        margin-bottom: 15px;
      }
    }

    .table-card {
      .el-table {
        font-size: 12px;
      }
    }
  }
}
</style>
