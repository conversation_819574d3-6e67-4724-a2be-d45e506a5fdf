/**
 * 验证工具函数
 */

/**
 * 判断是否为外部链接
 * @param {string} path 路径
 * @returns {boolean}
 */
export function isExternal(path: string): boolean {
  const reg = /^(https?:|mailto:|tel:)/
  return reg.test(path)
}

/**
 * 判断url是否是http或https
 * @param {string} url 链接
 * @returns {boolean}
 */
export function isHttp(url: string): boolean {
  return url.indexOf('http://') !== -1 || url.indexOf('https://') !== -1
}

/**
 * 判断是否为有效的邮箱地址
 * @param {string} email 邮箱
 * @returns {boolean}
 */
export function isEmail(email: string): boolean {
  const reg =
    /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
  return reg.test(email)
}

/**
 * 判断是否为有效的手机号
 * @param {string} mobile 手机号
 * @returns {boolean}
 */
export function isMobile(mobile: string): boolean {
  const reg = /^1[3-9]\d{9}$/
  return reg.test(mobile)
}

/**
 * 判断是否为空
 * @param {any} value 待验证值
 * @returns {boolean}
 */
export function isEmpty(value: any): boolean {
  if (value === null || value === undefined || value === '') {
    return true
  }
  if (Array.isArray(value) && value.length === 0) {
    return true
  }
  if (typeof value === 'object' && Object.keys(value).length === 0) {
    return true
  }
  return false
}
