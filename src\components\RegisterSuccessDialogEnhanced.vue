<template>
    <el-dialog
      v-model="visible"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="500px"
      center
      class="register-success-dialog"
      top="20vh"
    >
      <div class="success-container">
        <!-- 顶部图案装饰 -->
        <div class="decoration-top">
          <div class="decoration-circle circle-1"></div>
          <div class="decoration-circle circle-2"></div>
          <div class="decoration-circle circle-3"></div>
        </div>
  
        <div class="success-icon-wrapper">
          <el-icon class="success-icon-bg">
            <circle-check />
          </el-icon>
        </div>
  
        <h2 class="success-title">注册申请已提交</h2>
  
        <div class="success-content">
          <p>您的注册信息已收到，平台会在<span class="highlight">24小时内</span>审核完成</p>
          <p>感谢您耐心等待，审核结果将通过短信通知您</p>
        </div>
  
        <!-- 进度指示器 -->
        <div class="progress-indicator">
          <div class="step completed">
            <div class="step-icon">
              <el-icon><check /></el-icon>
            </div>
            <div class="step-text">提交申请</div>
          </div>
          <div class="step-line"></div>
          <div class="step active">
            <div class="step-icon">
              <el-icon><loading /></el-icon>
            </div>
            <div class="step-text">审核中</div>
          </div>
          <div class="step-line"></div>
          <div class="step">
            <div class="step-icon">
              <el-icon><shop /></el-icon>
            </div>
            <div class="step-text">开店成功</div>
          </div>
        </div>
  
        <div class="success-info">
          <div class="info-item">
            <el-icon><timer /></el-icon>
            <span>审核时间：24小时内</span>
          </div>
          <div class="info-item">
            <el-icon><question-filled /></el-icon>
            <span>遇到问题？联系客服 <EMAIL></span>
          </div>
        </div>
  
       
        <el-button type="primary" class="confirm-button" @click="handleConfirm">
          <el-icon><back /></el-icon>
          返回登录页面
        </el-button>
  
        <div class="countdown">
          <span class="countdown-text">{{ countdown }}秒后自动跳转</span>
          <div class="countdown-progress">
            <div class="countdown-progress-inner" :style="{ width: countdownProgress + '%' }"></div>
          </div>
        </div>
      </div>
    </el-dialog>
  </template>
  
  <script setup lang="ts">
  import { ref, computed, onMounted, onBeforeUnmount, watch } from 'vue'
  import { 
    CircleCheck, 
    Timer, 
    Message, 
    QuestionFilled, 
    Check, 
    Loading, 
    Shop, 
    PictureFilled,
    Back
  } from '@element-plus/icons-vue'
  
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
  })
  
  const emit = defineEmits(['confirm'])
  
  const countdown = ref(5)
  const countdownProgress = computed(() => (countdown.value / 5) * 100)
  let timer: number | null = null
  
  const handleConfirm = () => {
    if (timer) {
      window.clearInterval(timer)
      timer = null
    }
    emit('confirm')
  }
  
  watch(() => props.visible, (newVal) => {
    if (newVal) {
      countdown.value = 5
      startCountdown()
    } else if (timer) {
      window.clearInterval(timer)
      timer = null
    }
  })
  
  onMounted(() => {
    if (props.visible) {
      startCountdown()
    }
  })
  
  const startCountdown = () => {
    timer = window.setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        if (timer) {
          window.clearInterval(timer)
          timer = null
        }
        handleConfirm()
      }
    }, 1000)
  }
  
  onBeforeUnmount(() => {
    if (timer) {
      window.clearInterval(timer)
      timer = null
    }
  })
  </script>
  
  <style scoped lang="scss">
  .register-success-dialog {
    :deep(.el-dialog) {
      border-radius: 16px;
      overflow: hidden;
      box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
      border: 1px solid rgba(var(--el-color-primary-rgb), 0.1);
    }
  
    :deep(.el-dialog__header) {
      display: none;
    }
  
    :deep(.el-dialog__body) {
      padding: 0;
    }
  }
  
  .success-container {
    padding: 30px;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    overflow: hidden;
  }
  
  // 顶部装饰
  .decoration-top {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100px;
    overflow: hidden;
    pointer-events: none;
    z-index: 0;
    
    .decoration-circle {
      position: absolute;
      border-radius: 50%;
      opacity: 0.07;
    }
    
    .circle-1 {
      width: 120px;
      height: 120px;
      top: -60px;
      right: -30px;
      background: var(--el-color-primary);
    }
    
    .circle-2 {
      width: 80px;
      height: 80px;
      top: -15px;
      right: 50px;
      background: var(--el-color-success);
    }
    
    .circle-3 {
      width: 60px;
      height: 60px;
      top: 10px;
      right: 10px;
      background: var(--el-color-warning);
    }
  }
  
  .success-icon-wrapper {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(var(--el-color-success-rgb), 0.1) 0%, rgba(var(--el-color-success-rgb), 0.2) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 24px;
    animation: pulse 2s infinite;
    position: relative;
    z-index: 1;
  }
  
  .success-icon-bg {
    font-size: 60px;
    color: var(--el-color-success);
    animation: appear 0.5s ease-out;
  }
  
  .success-title {
    font-size: 28px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 20px;
    animation: slideDown 0.5s ease-out;
    position: relative;
    z-index: 1;
  }
  
  .success-content {
    margin-bottom: 25px;
    position: relative;
    z-index: 1;
  
    p {
      font-size: 16px;
      line-height: 1.8;
      color: #606266;
      margin: 5px 0;
    }
  
    .highlight {
      color: var(--el-color-primary);
      font-weight: 600;
    }
  }
  
  // 进度指示器
  .progress-indicator {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 25px;
    padding: 0 10px;
    
    .step {
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;
      flex: 1;
      
      &.completed .step-icon {
        background-color: var(--el-color-success);
        color: white;
      }
      
      &.active .step-icon {
        border-color: var(--el-color-primary);
        color: var(--el-color-primary);
        animation: spin 2s linear infinite;
      }
      
      .step-icon {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2px solid #e0e0e0;
        background-color: white;
        color: #909399;
        margin-bottom: 8px;
        transition: all 0.3s ease;
      }
      
      .step-text {
        font-size: 13px;
        color: #606266;
        white-space: nowrap;
      }
    }
    
    .step-line {
      height: 2px;
      background: #e0e0e0;
      flex: 1;
      margin: 0 10px;
      margin-bottom: 25px;
    }
  }
  
  .success-info {
    background-color: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    width: 100%;
    margin-bottom: 25px;
    animation: fadeIn 0.8s ease-out;
    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.03);
    border: 1px solid #ebeef5;
  
    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      text-align: left;
  
      &:last-child {
        margin-bottom: 0;
      }
  
      .el-icon {
        font-size: 18px;
        color: var(--el-color-primary);
        margin-right: 10px;
      }
  
      span {
        font-size: 14px;
        color: #606266;
      }
    }
  }
  
  // 关注公众号区域
  .follow-section {
    width: 100%;
    margin-bottom: 25px;
    
    .follow-title {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 15px;
      
      .line {
        height: 1px;
        flex: 1;
        background: #ebeef5;
      }
      
      span {
        padding: 0 15px;
        font-size: 14px;
        color: #909399;
      }
    }
    
    .qrcode-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      
      .qrcode-placeholder {
        width: 100px;
        height: 100px;
        border: 1px dashed #dcdfe6;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f8f9fa;
        margin-bottom: 10px;
        
        .qrcode-icon {
          font-size: 40px;
          color: #c0c4cc;
        }
      }
      
      .qrcode-text {
        font-size: 13px;
        color: #909399;
      }
    }
  }
  
  .confirm-button {
    width: 100%;
    height: 46px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 15px;
    transition: all 0.3s;
    background: linear-gradient(90deg, var(--el-color-primary), #79bbff);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(var(--el-color-primary-rgb), 0.3);
    }
    
    .el-icon {
      margin-right: 4px;
      font-size: 18px;
    }
  }
  
  .countdown {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .countdown-text {
      font-size: 14px;
      color: #909399;
      margin-bottom: 8px;
    }
    
    .countdown-progress {
      width: 100%;
      height: 4px;
      background-color: #f2f2f2;
      border-radius: 2px;
      overflow: hidden;
      
      .countdown-progress-inner {
        height: 100%;
        background: linear-gradient(90deg, var(--el-color-primary), #79bbff);
        transition: width 1s linear;
      }
    }
  }
  
  @keyframes pulse {
    0% {
      box-shadow: 0 0 0 0 rgba(103, 194, 58, 0.4);
    }
    70% {
      box-shadow: 0 0 0 15px rgba(103, 194, 58, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(103, 194, 58, 0);
    }
  }
  
  @keyframes appear {
    from {
      opacity: 0;
      transform: scale(0.5);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
  
  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  
  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
  </style>