<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <link rel="icon" href="/favicon.ico">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Sharewharf商城管理系统</title>
  <script>
    // 为 translate.js 添加类型声明
    window.translate = window.translate || {
      changeLanguage: function (language) {
        // 基础的changeLanguage方法
        this._changeLanguage = this._changeLanguage || function () {}
      },
      _changeLanguage: null,
      selectLanguageTag: {
        show: false
      },
      service: {
        use: function (service) {}
      },
      execute: function () {}
    };
  </script>
</head>

<body>
  <div id="app"></div>
  <script type="module" src="/src/main.ts"></script>
</body>

</html>