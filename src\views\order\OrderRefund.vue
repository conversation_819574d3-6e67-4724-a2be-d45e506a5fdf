<template>
  <div class="order-refund-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>退款处理</h3>
          <div class="header-actions">
            <el-button @click="handleRefresh" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <!-- 功能说明 -->
      <el-alert
        title="功能说明"
        type="info"
        :closable="false"
        style="margin-bottom: 20px"
      >
        <template #default>
          <p>此页面用于查看需要退款处理的订单。实际的退款申请创建和审核请前往 <strong>退款管理</strong> 模块。</p>
          <p>点击"同意退款"或"拒绝退款"按钮将引导您到退款管理页面进行具体操作。</p>
        </template>
      </el-alert>

      <!-- 搜索筛选区域 -->
      <div class="search-section">
        <el-form :model="searchForm" inline>
          <el-form-item label="订单号">
            <el-input
              v-model="searchForm.number"
              placeholder="请输入订单号"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="买家ID">
            <el-input-number
              v-model="searchForm.buyerId"
              placeholder="请输入买家ID"
              :min="1"
              style="width: 150px"
            />
          </el-form-item>
          <el-form-item label="订单状态">
            <el-select v-model="searchForm.status" placeholder="请选择订单状态" clearable>
              <el-option label="申请退款" :value="6" />
              <el-option label="已退款" :value="7" />
              <el-option label="已完成" :value="4" />
              <el-option label="已取消" :value="5" />
            </el-select>
          </el-form-item>
          <el-form-item label="申请时间">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch" :loading="loading">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 统计信息 -->
      <div class="stats-section">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-statistic title="待处理退款" :value="stats.pending" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="今日处理" :value="stats.todayProcessed" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="本月退款金额" :value="stats.monthlyAmount" prefix="¥" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="退款成功率" :value="stats.successRate" suffix="%" />
          </el-col>
        </el-row>
      </div>

      <!-- 退款申请列表 -->
      <div class="table-section">
        <el-table
          :data="refundList"
          :loading="loading"
          stripe
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="number" label="订单号" width="180" show-overflow-tooltip />
          <el-table-column prop="buyerId" label="买家ID" width="100" />
          <el-table-column prop="amount" label="订单金额" width="120">
            <template #default="{ row }">
              <span class="amount">¥{{ row.amount }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="备注" min-width="200" show-overflow-tooltip />
          <el-table-column prop="status" label="订单状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getOrderStatusType(row.status)">
                {{ getOrderStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="orderTime" label="下单时间" width="160" />
          <el-table-column prop="payTime" label="支付时间" width="160">
            <template #default="{ row }">
              {{ row.payTime || '-' }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button size="small" @click="handleViewDetail(row)">
                  详情
                </el-button>
                <el-button
                  v-if="canRefund(row)"
                  size="small"
                  type="success"
                  @click="handleApprove(row)"
                >
                  同意退款
                </el-button>
                <el-button
                  v-if="canRefund(row)"
                  size="small"
                  type="danger"
                  @click="handleReject(row)"
                >
                  拒绝退款
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-section">
          <el-pagination
            v-model:current-page="searchForm.page"
            v-model:page-size="searchForm.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 退款详情对话框 -->
    <!-- <RefundDetailDialog
      v-model="detailDialogVisible"
      :refund-info="selectedRefund"
      @refresh="handleRefresh"
    /> -->

    <!-- 退款处理对话框 -->
    <!-- <RefundProcessDialog
      v-model="processDialogVisible"
      :refund-info="selectedRefund"
      :action="processAction"
      @success="handleRefresh"
    /> -->
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'
// import RefundDetailDialog from './components/RefundDetailDialog.vue'
// import RefundProcessDialog from './components/RefundProcessDialog.vue'

// 路由实例
const router = useRouter()

// 搜索表单
const searchForm = reactive({
  number: '',
  buyerId: undefined as number | undefined,
  status: undefined as number | undefined,
  dateRange: null as string[] | null,
  page: 1,
  pageSize: 20
})

// 状态管理
const loading = ref(false)
const refundList = ref([])
const selectedRefunds = ref([])
const total = ref(0)

// 对话框状态
const detailDialogVisible = ref(false)
const processDialogVisible = ref(false)
const selectedRefund = ref(null)
const processAction = ref('')

// 统计数据
const stats = reactive({
  pending: 0,
  todayProcessed: 0,
  monthlyAmount: 0,
  successRate: 0
})

// 获取订单状态文本
const getOrderStatusText = (status: number) => {
  const statusMap = {
    1: '待付款',
    2: '待发货',
    3: '已发货',
    4: '已完成',
    5: '已取消',
    6: '申请退款',
    7: '已退款'
  }
  return statusMap[status] || '未知状态'
}

// 获取订单状态类型
const getOrderStatusType = (status: number) => {
  const typeMap = {
    1: 'warning',
    2: 'primary',
    3: 'info',
    4: 'success',
    5: 'danger',
    6: 'warning',
    7: 'info'
  }
  return typeMap[status] || 'info'
}

// 判断是否可以退款处理
const canRefund = (order: any) => {
  // 假设状态6表示申请退款，需要管理员处理
  return order.status === 6
}

// 查看详情
const handleViewDetail = (order: any) => {
  ElMessage.info(`查看订单 ${order.number} 的详情`)
  // 可以跳转到订单详情页面或打开详情对话框
}

// 同意退款 - 跳转到退款管理页面
const handleApprove = async (order: any) => {
  try {
    await ElMessageBox.confirm(
      `确认要为订单 ${order.number} 创建退款申请吗？`,
      '创建退款申请',
      {
        confirmButtonText: '前往退款管理',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    // 跳转到退款管理页面
    router.push(`/main/refund/list?orderNumber=${order.number}`)

  } catch (error) {
    // 用户取消操作
  }
}

// 拒绝退款 - 跳转到退款管理页面
const handleReject = async (order: any) => {
  try {
    await ElMessageBox.confirm(
      `确认要拒绝订单 ${order.number} 的退款申请吗？`,
      '拒绝退款申请',
      {
        confirmButtonText: '前往退款管理',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 跳转到退款管理页面
    router.push(`/main/refund/list?orderNumber=${order.number}`)

  } catch (error) {
    // 用户取消操作
  }
}

// 搜索
const handleSearch = () => {
  searchForm.page = 1
  loadRefundList()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    number: '',
    buyerId: undefined,
    status: undefined,
    dateRange: null,
    page: 1,
    pageSize: 20
  })
  loadRefundList()
}

// 刷新
const handleRefresh = () => {
  loadRefundList()
  loadStats()
}

// 选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedRefunds.value = selection
}

// 分页变化
const handleSizeChange = (size: number) => {
  searchForm.pageSize = size
  loadRefundList()
}

const handleCurrentChange = (page: number) => {
  searchForm.page = page
  loadRefundList()
}

// 加载退款列表（使用订单列表接口，筛选需要退款处理的订单）
const loadRefundList = async () => {
  loading.value = true
  try {
    // 模拟数据 - 实际应该调用获取需要退款处理的订单的API
    await new Promise(resolve => setTimeout(resolve, 500)) // 模拟网络延迟

    const mockData = [
      {
        id: 1,
        number: 'ORD001',
        amount: 299.99,
        buyerId: 1001,
        buyerName: '张三',
        status: 2, // 已支付
        createTime: '2024-01-15 10:30:00',
        refundReason: '商品质量问题'
      },
      {
        id: 2,
        number: 'ORD002',
        amount: 199.50,
        buyerId: 1002,
        buyerName: '李四',
        status: 2, // 已支付
        createTime: '2024-01-14 15:20:00',
        refundReason: '不满意商品'
      }
    ]

    refundList.value = mockData
    total.value = mockData.length
  } catch (error) {
    console.error('加载订单列表失败:', error)
    ElMessage.error('加载订单列表失败')
  } finally {
    loading.value = false
  }
}

// 加载统计数据（暂时使用模拟数据，后端可以基于订单统计接口扩展）
const loadStats = async () => {
  try {
    // 暂时使用模拟数据，实际可以调用 getOrderStatistics() 并处理退款相关统计
    Object.assign(stats, {
      pending: 5,
      todayProcessed: 12,
      monthlyAmount: 15680.50,
      successRate: 95.2
    })
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadRefundList()
  loadStats()
})
</script>

<style scoped lang="scss">
.order-refund-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  h3 {
    margin: 0;
    color: #303133;
  }
}

.search-section {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.stats-section {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.table-section {
  .amount {
    color: #f56c6c;
    font-weight: 600;
  }

  .action-buttons {
    display: flex;
    gap: 8px;
  }
}

.pagination-section {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
