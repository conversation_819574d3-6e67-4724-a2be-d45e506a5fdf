<template>
  <div class="commission-test-page">
    <h1>佣金模式路由测试页面</h1>
    
    <div class="test-section">
      <h2>直接路由跳转测试</h2>
      <div class="button-group">
        <el-button type="primary" @click="testRoute('/main/commission/leader-list')">
          团长管理
        </el-button>
        <el-button type="primary" @click="testRoute('/main/commission/add-leader')">
          添加团长
        </el-button>
        <el-button type="primary" @click="testRoute('/main/commission/commission-settings')">
          佣金设置
        </el-button>
        <el-button type="primary" @click="testRoute('/main/commission/invitation-codes')">
          邀请码管理
        </el-button>
        <el-button type="primary" @click="testRoute('/main/commission/commission-statistics')">
          佣金统计
        </el-button>
      </div>
    </div>

    <div class="test-section">
      <h2>使用router.push跳转测试</h2>
      <div class="button-group">
        <el-button type="success" @click="routerPush('/main/commission/leader-list')">
          团长管理
        </el-button>
        <el-button type="success" @click="routerPush('/main/commission/add-leader')">
          添加团长
        </el-button>
        <el-button type="success" @click="routerPush('/main/commission/commission-settings')">
          佣金设置
        </el-button>
        <el-button type="success" @click="routerPush('/main/commission/invitation-codes')">
          邀请码管理
        </el-button>
        <el-button type="success" @click="routerPush('/main/commission/commission-statistics')">
          佣金统计
        </el-button>
      </div>
    </div>

    <div class="test-section">
      <h2>使用router.replace跳转测试</h2>
      <div class="button-group">
        <el-button type="warning" @click="routerReplace('/main/commission/leader-list')">
          团长管理
        </el-button>
        <el-button type="warning" @click="routerReplace('/main/commission/add-leader')">
          添加团长
        </el-button>
        <el-button type="warning" @click="routerReplace('/main/commission/commission-settings')">
          佣金设置
        </el-button>
        <el-button type="warning" @click="routerReplace('/main/commission/invitation-codes')">
          邀请码管理
        </el-button>
        <el-button type="warning" @click="routerReplace('/main/commission/commission-statistics')">
          佣金统计
        </el-button>
      </div>
    </div>

    <div class="test-section">
      <h2>使用window.location跳转测试</h2>
      <div class="button-group">
        <el-button type="danger" @click="windowLocation('/main/commission/leader-list')">
          团长管理
        </el-button>
        <el-button type="danger" @click="windowLocation('/main/commission/add-leader')">
          添加团长
        </el-button>
        <el-button type="danger" @click="windowLocation('/main/commission/commission-settings')">
          佣金设置
        </el-button>
        <el-button type="danger" @click="windowLocation('/main/commission/invitation-codes')">
          邀请码管理
        </el-button>
        <el-button type="danger" @click="windowLocation('/main/commission/commission-statistics')">
          佣金统计
        </el-button>
      </div>
    </div>

    <div class="test-section">
      <h2>当前路由信息</h2>
      <div class="route-info">
        <p><strong>当前路径:</strong> {{ route.path }}</p>
        <p><strong>完整路径:</strong> {{ route.fullPath }}</p>
        <p><strong>路由名称:</strong> {{ route.name }}</p>
        <p><strong>路由参数:</strong> {{ JSON.stringify(route.params) }}</p>
        <p><strong>查询参数:</strong> {{ JSON.stringify(route.query) }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'

const router = useRouter()
const route = useRoute()

// 测试路由跳转
const testRoute = (path: string) => {
  try {
    console.log(`测试路由跳转到: ${path}`)
    window.location.href = path
  } catch (error) {
    console.error('路由跳转失败:', error)
    ElMessage.error(`跳转失败: ${error}`)
  }
}

// 使用router.push跳转
const routerPush = (path: string) => {
  try {
    console.log(`使用router.push跳转到: ${path}`)
    router.push(path)
      .then(() => {
        console.log('跳转成功')
        ElMessage.success('跳转成功')
      })
      .catch(error => {
        console.error('跳转失败:', error)
        ElMessage.error(`跳转失败: ${error}`)
      })
  } catch (error) {
    console.error('跳转异常:', error)
    ElMessage.error(`跳转异常: ${error}`)
  }
}

// 使用router.replace跳转
const routerReplace = (path: string) => {
  try {
    console.log(`使用router.replace跳转到: ${path}`)
    router.replace(path)
      .then(() => {
        console.log('跳转成功')
        ElMessage.success('跳转成功')
      })
      .catch(error => {
        console.error('跳转失败:', error)
        ElMessage.error(`跳转失败: ${error}`)
      })
  } catch (error) {
    console.error('跳转异常:', error)
    ElMessage.error(`跳转异常: ${error}`)
  }
}

// 使用window.location跳转
const windowLocation = (path: string) => {
  try {
    console.log(`使用window.location跳转到: ${path}`)
    window.location.href = path
  } catch (error) {
    console.error('跳转失败:', error)
    ElMessage.error(`跳转失败: ${error}`)
  }
}
</script>

<style scoped>
.commission-test-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

h1 {
  text-align: center;
  margin-bottom: 30px;
  color: #409EFF;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border-radius: 8px;
  background-color: #f5f7fa;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

h2 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #303133;
  font-size: 18px;
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.route-info {
  background-color: #fff;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
}

.route-info p {
  margin: 5px 0;
  font-family: monospace;
}
</style> 