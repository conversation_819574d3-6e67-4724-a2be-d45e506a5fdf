import axios from 'axios'
import { ElMessage } from 'element-plus'
import { getToken, removeToken, isTokenExpired } from './auth'
import debug from './debug'

// 创建axios实例
const service = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 15000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json;charset=utf-8',
  },
})

// 输出API基础URL用于调试
console.log('API基础URL:', import.meta.env.VITE_API_BASE_URL)

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    // 输出请求调试信息
    debug.api.request(config.url || '', config.method || 'get', config.data || config.params)

    // 在请求发送前做一些处理
    const token = getToken()
    if (token && !isTokenExpired()) {
      config.headers['Authorization'] = `${token}`
    } else {
      removeToken()
    }

    // 请求参数打印与处理
    try {
      // 处理FormData请求
      if (config.data && typeof config.data === 'object' && config.data.constructor === FormData) {
        // 不要为FormData手动设置Content-Type，让浏览器自动处理
        delete config.headers['Content-Type']

        // 生成FormData内容的摘要用于日志
        const formDataSummary: Record<string, any> = {
          files: [],
          otherKeys: [],
        }

        const formData = config.data as FormData
        formData.forEach((value: any, key: string) => {
          if (
            value &&
            typeof value === 'object' &&
            (value.constructor === File || value.constructor === Blob)
          ) {
            formDataSummary.files.push({
              key,
              name: value.constructor === File ? (value as File).name : 'Blob',
              type: (value as Blob).type,
              size: `${((value as Blob).size / 1024).toFixed(2)}KB`,
            })
          } else {
            // 对于非文件类型，记录键名
            formDataSummary.otherKeys.push(key)
          }
        })

        console.log(`FormData请求: ${config.url}`, formDataSummary)
      } else if (config.data && typeof config.data === 'object') {
        console.log(`请求参数: ${config.url}`, config.data)
      }
    } catch (error) {
      console.error('打印请求参数时出错:', error)
    }

    return config
  },
  (error) => {
    // 处理请求错误
    console.error('Request error:', error)
    return Promise.reject(error)
  },
)

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    // 输出响应调试信息
    debug.api.response(response.config.url || '', response.status, response.data)

    // 对响应数据做处理
    const res = response.data
    // 如果响应状态码不是200，认为请求有错误
    if (response.status !== 200) {
      ElMessage.error('网络请求错误')
      return Promise.reject(new Error('网络请求错误'))
    }

    // 针对业务状态码的处理
    if (res.code && res.code !== 1) {
      ElMessage.error(res.msg || '服务器返回错误')

      // 401: 未授权，需要登录
      if (res.code === 401) {
        removeToken()
        location.href = '/'
      }

      return Promise.reject(new Error(res.msg || '服务器返回错误'))
    }

    return res
  },
  (error) => {
    // 输出错误调试信息
    debug.api.error(error.config?.url || 'unknown', error)

    // 处理响应错误
    console.error('Response error:', error)
    let message = '连接服务器失败'

    if (error.response) {
      const status = error.response.status

      // 针对不同HTTP状态码的处理
      switch (status) {
        case 400:
          message = '请求错误'
          break
        case 401:
          message = '未授权，请登录'
          removeToken()
          location.href = '/'
          break
        case 403:
          message = '拒绝访问'
          break
        case 404:
          message = '请求的资源不存在'
          break
        case 500:
          message = '服务器内部错误'
          break
        default:
          message = `请求失败: ${status}`
      }
    } else if (error.message.includes('timeout')) {
      message = '请求超时'
    }

    ElMessage.error(message)
    return Promise.reject(error)
  },
)

export default service
