<template>
  <div class="welcome-container">
    <div class="welcome-content">
      <div class="welcome-header">
        <div class="logo-animation">
          <img src="../assets/logo.svg" alt="Logo" class="welcome-logo" />
        </div>
        <h1 class="welcome-title">欢迎使用 Sharewharf 服务</h1>
        <p class="welcome-subtitle">让商品管理变得简单而高效</p>
      </div>

      <div class="feature-cards">
        <el-row :gutter="24">
          <el-col :span="8">
            <div class="feature-card" @click="goToAddProduct">
              <el-icon><Goods /></el-icon>
              <h3>商品管理</h3>
              <p>轻松管理您的商品信息</p>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="feature-card" @click="goToDashboard">
              <el-icon><DataLine /></el-icon>
              <h3>数据分析</h3>
              <p>实时掌握经营动态</p>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="feature-card" @click="goToHelpDocs">
              <el-icon><Document /></el-icon>
              <h3>帮助文档</h3>
              <p>获取更多使用指南</p>
            </div>
          </el-col>
        </el-row>
      </div>

      

      <div class="welcome-footer">
        <p class="version-info">当前版本：v2.5.0</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { Goods, DataLine, Document } from '@element-plus/icons-vue'

const router = useRouter()

const goToAddProduct = () => {
  router.push('/main/product/add')
}

const goToDashboard = () => {
  router.push('/main/dashboard')
}

const goToHelpDocs = () => {
  router.push('/main/help/docs')
}
</script>

<style scoped lang="scss">
.welcome-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #eef2f7 100%);
  padding: 20px;
}

.welcome-content {
  width: 100%;
  max-width: 1200px;
  text-align: center;
}

.welcome-header {
  margin-bottom: 60px;

  .logo-animation {
    margin-bottom: 30px;

    .welcome-logo {
      width: 120px;
      height: 120px;
      animation: float 6s ease-in-out infinite;
    }
  }

  .welcome-title {
    font-size: 36px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 16px;
    background: linear-gradient(120deg, #3a7bd5, #2c5499);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .welcome-subtitle {
    font-size: 18px;
    color: #5e6d82;
    margin: 0;
    opacity: 0.8;
  }
}

.feature-cards {
  margin-bottom: 60px;

  .feature-card {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 16px;
    padding: 40px 20px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    height: 100%;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(10px);

    &:hover {
      transform: translateY(-10px);
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
      background: rgba(255, 255, 255, 0.95);

      .el-icon {
        transform: scale(1.1);
        color: #3a7bd5;
      }
    }

    .el-icon {
      font-size: 48px;
      color: #409eff;
      margin-bottom: 20px;
      transition: all 0.3s ease;
    }

    h3 {
      font-size: 20px;
      color: #2c3e50;
      margin: 0 0 12px;
      font-weight: 600;
    }

    p {
      font-size: 14px;
      color: #5e6d82;
      margin: 0;
      opacity: 0.8;
    }
  }
}

.commission-test-area {
  margin-top: 20px;
  padding: 15px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #f5f7fa;
}

.test-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
}

.welcome-footer {
  .version-info {
    font-size: 14px;
    color: #909399;
    margin: 0;
    opacity: 0.6;
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(0px);
  }
}

// 响应式调整
@media screen and (max-width: 768px) {
  .welcome-header {
    .welcome-title {
      font-size: 28px;
    }
    .welcome-subtitle {
      font-size: 16px;
    }
  }

  .feature-cards {
    .el-col {
      margin-bottom: 20px;
    }
  }
}
</style>
