<template>
  <el-dialog
    v-model="visible"
    title="退款详情"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="refundInfo" class="refund-detail">
      <!-- 基本信息 -->
      <div class="info-section">
        <h3>退款基本信息</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <span class="label">订单号：</span>
              <span class="value">{{ refundInfo.orderNumber }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">买家ID：</span>
              <span class="value">{{ refundInfo.buyerId }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">退款金额：</span>
              <span class="value amount">¥{{ refundInfo.refundAmount }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">退款状态：</span>
              <el-tag :type="getStatusType(refundInfo.refundStatus)">
                {{ getStatusText(refundInfo.refundStatus) }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">申请时间：</span>
              <span class="value">{{ refundInfo.applyTime }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">处理时间：</span>
              <span class="value">{{ refundInfo.processTime || '未处理' }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 退款原因 -->
      <div class="info-section">
        <h3>退款原因</h3>
        <div class="reason-content">
          {{ refundInfo.refundReason }}
        </div>
      </div>

      <!-- 退款凭证 -->
      <div v-if="refundInfo.attachments && refundInfo.attachments.length > 0" class="info-section">
        <h3>退款凭证</h3>
        <div class="attachments">
          <div v-for="(attachment, index) in refundInfo.attachments" :key="index" class="attachment-item">
            <el-image
              v-if="isImage(attachment.url)"
              :src="attachment.url"
              :preview-src-list="[attachment.url]"
              fit="cover"
              style="width: 100px; height: 100px"
            />
            <div v-else class="file-attachment">
              <el-icon><Document /></el-icon>
              <span>{{ attachment.name }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 处理记录 -->
      <div v-if="refundInfo.processHistory && refundInfo.processHistory.length > 0" class="info-section">
        <h3>处理记录</h3>
        <el-timeline>
          <el-timeline-item
            v-for="(record, index) in refundInfo.processHistory"
            :key="index"
            :timestamp="record.time"
            placement="top"
          >
            <div class="timeline-content">
              <div class="action">{{ record.action }}</div>
              <div class="operator">操作人：{{ record.operator }}</div>
              <div v-if="record.remark" class="remark">备注：{{ record.remark }}</div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>

      <!-- 订单信息 -->
      <div class="info-section">
        <h3>关联订单信息</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <span class="label">订单金额：</span>
              <span class="value">¥{{ refundInfo.orderAmount || '0.00' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">支付方式：</span>
              <span class="value">{{ getPayMethodText(refundInfo.payMethod) }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">下单时间：</span>
              <span class="value">{{ refundInfo.orderTime || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">支付时间：</span>
              <span class="value">{{ refundInfo.payTime || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          v-if="refundInfo && refundInfo.refundStatus === 'pending'"
          type="success"
          @click="handleApprove"
        >
          同意退款
        </el-button>
        <el-button
          v-if="refundInfo && refundInfo.refundStatus === 'pending'"
          type="danger"
          @click="handleReject"
        >
          拒绝退款
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { Document } from '@element-plus/icons-vue'

interface Props {
  modelValue: boolean
  refundInfo?: any
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'refresh'): void
  (e: 'approve', refund: any): void
  (e: 'reject', refund: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = ref(false)

// 监听对话框显示状态
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
})

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    pending: '待处理',
    approved: '已同意',
    rejected: '已拒绝',
    completed: '已完成'
  }
  return statusMap[status] || status
}

// 获取状态类型
const getStatusType = (status: string) => {
  const typeMap = {
    pending: 'warning',
    approved: 'success',
    rejected: 'danger',
    completed: 'info'
  }
  return typeMap[status] || 'info'
}

// 获取支付方式文本
const getPayMethodText = (method: number) => {
  const methodMap = {
    1: '微信支付',
    2: '支付宝',
    3: '银行卡'
  }
  return methodMap[method] || '未知'
}

// 判断是否为图片
const isImage = (url: string) => {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
  return imageExtensions.some(ext => url.toLowerCase().includes(ext))
}

// 同意退款
const handleApprove = () => {
  emit('approve', props.refundInfo)
  handleClose()
}

// 拒绝退款
const handleReject = () => {
  emit('reject', props.refundInfo)
  handleClose()
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
}
</script>

<style scoped lang="scss">
.refund-detail {
  .info-section {
    margin-bottom: 30px;

    h3 {
      margin: 0 0 15px 0;
      color: #303133;
      font-size: 16px;
      border-bottom: 1px solid #e4e7ed;
      padding-bottom: 8px;
    }

    .info-item {
      margin-bottom: 12px;
      display: flex;
      align-items: center;

      .label {
        color: #606266;
        font-weight: 500;
        min-width: 80px;
      }

      .value {
        color: #303133;

        &.amount {
          color: #f56c6c;
          font-weight: 600;
        }
      }
    }
  }

  .reason-content {
    padding: 15px;
    background-color: #f5f7fa;
    border-radius: 4px;
    color: #606266;
    line-height: 1.6;
  }

  .attachments {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;

    .attachment-item {
      .file-attachment {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20px;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        background-color: #f5f7fa;

        .el-icon {
          font-size: 24px;
          color: #909399;
          margin-bottom: 8px;
        }

        span {
          font-size: 12px;
          color: #606266;
        }
      }
    }
  }

  .timeline-content {
    .action {
      font-weight: 600;
      color: #303133;
      margin-bottom: 5px;
    }

    .operator {
      font-size: 12px;
      color: #909399;
      margin-bottom: 3px;
    }

    .remark {
      font-size: 12px;
      color: #606266;
      background-color: #f5f7fa;
      padding: 8px;
      border-radius: 4px;
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
