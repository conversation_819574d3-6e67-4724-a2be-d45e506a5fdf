// 回款功能相关类型定义
import type { PaymentAccountVO } from './paymentAccount'

// 回款状态枚举
export enum SettlementStatus {
  NOT_DUE = 0,      // 未到期
  PENDING = 1,      // 待回款
  COMPLETED = 2     // 已回款
}

// 回款状态描述映射
export const SettlementStatusMap = {
  [SettlementStatus.NOT_DUE]: '未到期',
  [SettlementStatus.PENDING]: '待回款',
  [SettlementStatus.COMPLETED]: '已回款'
}

// 订单回款信息实体
export interface OrderSettlementInfo {
  id: number                    // 主键ID
  orderId: number               // 订单ID
  orderNumber: string           // 订单号
  sellerId: number              // 商家ID
  sellerName: string            // 商家名称
  payTime: string               // 支付时间
  billingDate: string           // 账单日期
  billingCycle: string          // 账单周期
  settlementDate: string        // 回款日期
  orderAmount: number           // 订单金额
  settlementAmount: number      // 回款金额
  settlementStatus: SettlementStatus // 回款状态
  settlementTime?: string       // 实际回款时间
  remark?: string               // 备注信息
  createTime: string            // 创建时间
  updateTime: string            // 更新时间
}

// 回款配置
export interface SettlementConfig {
  id: number                    // 主键ID
  configKey: string             // 配置键
  configValue: string           // 配置值
  configDesc: string            // 配置描述
  createTime: string            // 创建时间
  updateTime: string            // 更新时间
}

// 回款查询条件DTO
export interface SettlementQueryDTO {
  page?: number                 // 页码
  pageSize?: number             // 每页记录数
  sellerId?: number             // 商家ID
  orderNumber?: string          // 订单号
  settlementStatus?: SettlementStatus // 回款状态
  billingCycle?: string         // 账单周期
  settlementStartDate?: string  // 回款开始日期
  settlementEndDate?: string    // 回款结束日期
  sellerName?: string           // 商家名称
}

// 回款信息视图VO
export interface SettlementInfoVO {
  id: number                    // 主键ID
  orderId: number               // 订单ID
  orderNumber: string           // 订单号
  sellerId: number              // 商家ID
  sellerName: string            // 商家名称
  payTime: string               // 支付时间
  billingDate: string           // 账单日期
  billingCycle: string          // 账单周期
  settlementDate: string        // 回款日期
  orderAmount: number           // 订单金额
  settlementAmount: number      // 回款金额
  settlementStatus: SettlementStatus // 回款状态
  settlementStatusDesc: string  // 回款状态描述
  settlementTime?: string       // 实际回款时间
  remark?: string               // 备注信息
  isDue: number                 // 是否到期(0-否,1-是)
  daysToSettlement: number      // 距离回款天数
  paymentAccount?: PaymentAccountVO // 收款账户信息
}

// 回款汇总视图VO
export interface SettlementSummaryVO {
  sellerId?: number             // 商家ID
  sellerName?: string           // 商家名称
  totalOrders: number           // 总订单数
  totalOrderAmount: number      // 总订单金额
  totalSettlementAmount: number // 总回款金额
  notDueOrders: number          // 未到期订单数
  notDueAmount: number          // 未到期金额
  pendingOrders: number         // 待回款订单数
  pendingAmount: number         // 待回款金额
  completedOrders: number       // 已回款订单数
  completedAmount: number       // 已回款金额
}

// 回款完成DTO
export interface SettlementCompleteDTO {
  id: number                    // 回款记录ID
  remark?: string               // 备注
}

// 分页响应
export interface PageResponse<T> {
  list: T[]                     // 数据列表（后端返回的是list）
  total: number                 // 总记录数
  size: number                  // 每页大小
  pageNum: number               // 当前页
  pages: number                 // 总页数
  // 兼容字段
  records?: T[]                 // 兼容records字段
  current?: number              // 兼容current字段
}

// API响应基础结构
export interface ApiResponse<T = any> {
  code: number                  // 响应码
  msg: string                   // 响应消息
  data: T                       // 响应数据
}

// 回款统计数据
export interface SettlementStatistics {
  totalSettlementAmount: number // 总回款金额
  monthlySettlementAmount: number // 本月回款金额
  pendingSettlementAmount: number // 待回款金额
  overdueSettlementAmount: number // 逾期回款金额
  settlementRate: number        // 回款率
  averageSettlementDays: number // 平均回款天数
}

// 回款趋势数据
export interface SettlementTrend {
  date: string                  // 日期
  amount: number                // 金额
  count: number                 // 订单数
}

// 商家回款概览
export interface MerchantSettlementOverview {
  summary: SettlementSummaryVO  // 汇总信息
  statistics: SettlementStatistics // 统计数据
  recentSettlements: SettlementInfoVO[] // 最近回款记录
  trends: SettlementTrend[]     // 趋势数据
}

// 平台回款管理概览
export interface AdminSettlementOverview {
  totalMerchants: number        // 总商家数
  totalSettlementAmount: number // 总回款金额
  pendingSettlementCount: number // 待回款订单数
  pendingSettlementAmount: number // 待回款金额
  todaySettlementCount: number  // 今日回款订单数
  todaySettlementAmount: number // 今日回款金额
  merchantSummaries: SettlementSummaryVO[] // 商家汇总列表
}
