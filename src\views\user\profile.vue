<template>
  <div class="profile-container" v-loading="initialLoading">
    <!-- 顶部装饰元素 -->
    <div class="decorative-shapes">
      <div class="shape shape-1"></div>
      <div class="shape shape-2"></div>
      <div class="shape shape-3"></div>
    </div>

    <!-- 页面标题区域 -->
    <div class="profile-header">
      <div class="header-content">
        <h1 class="header-title">个人信息</h1>
        <p class="header-subtitle">查看并管理您的个人账户详情</p>
        <el-button
          type="primary"
          size="small"
          icon="Refresh"
          :loading="loading"
          @click="getUserInfo"
          class="refresh-btn"
        >
          刷新信息
        </el-button>
      </div>
    </div>

    <el-row :gutter="24">
      <!-- 左侧个人信息卡片 -->
      <el-col :xs="24" :sm="24" :md="8" :lg="7" :xl="6">
        <el-card class="profile-card user-info-card">
          <div class="card-decoration">
            <div class="decoration-circle"></div>
          </div>

          <div class="avatar-container">
            <div class="avatar-ring"></div>
            <div class="avatar-wrapper">
              <el-avatar
                :size="110"
                :src="
                  userInfo.photoUrl ||
                  'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'
                "
                class="user-avatar"
              />
              <div class="status-badge" :class="{ 'status-active': userInfo.accountStatus === 1 }">
                <el-icon><Check /></el-icon>
              </div>
            </div>

            <div class="user-info-text">
              <h2 class="user-name">{{ userInfo.accountName }}</h2>
              <p class="user-id">ID: {{ userInfo.id }}</p>
              <el-tag
                :type="getStatusType(userInfo.accountStatus)"
                effect="dark"
                class="status-tag"
              >
                {{ userInfo.accountStatus === 1 ? '正常' : '已禁用' }}
              </el-tag>
            </div>
          </div>

          <div class="user-stats">
            <div class="stat-item">
              <div class="stat-icon">
                <el-icon><Calendar /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-label">注册时间</div>
                <div class="stat-value">{{ formatDate(userInfo.createTime) }}</div>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon">
                <el-icon><Timer /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-label">最后登录</div>
                <div class="stat-value">{{ formatDate(userInfo.lastLoginTime) }}</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧个人资料展示 -->
      <el-col :xs="24" :sm="24" :md="16" :lg="17" :xl="18">
        <el-card class="profile-card info-card">
          <template #header>
            <div class="card-header">
              <div class="header-icon-wrapper">
                <el-icon><User /></el-icon>
              </div>
              <span>个人资料</span>
            </div>
          </template>

          <div class="info-section">
            <el-row :gutter="30">
              <el-col :xs="24" :sm="12">
                <div class="info-item">
                  <div class="info-label">
                    <div class="label-dot"></div>
                    账户名称
                  </div>
                  <div class="info-value account-name">
                    {{ userInfo.accountName || '未设置' }}
                  </div>
                </div>
              </el-col>

              <el-col :xs="24" :sm="12">
                <div class="info-item">
                  <div class="info-label">
                    <div class="label-dot"></div>
                    性别
                  </div>
                  <div class="info-value">
                    {{ userInfo.gender || '保密' }}
                  </div>
                </div>
              </el-col>

              <el-col :xs="24" :sm="12">
                <div class="info-item">
                  <div class="info-label">
                    <div class="label-dot"></div>
                    手机号码
                  </div>
                  <div class="info-value contact-value">
                    <div class="contact-icon">
                      <el-icon><Phone /></el-icon>
                    </div>
                    <span>{{ userInfo.phone || '未设置' }}</span>
                  </div>
                </div>
              </el-col>

              <el-col :xs="24" :sm="12">
                <div class="info-item">
                  <div class="info-label">
                    <div class="label-dot"></div>
                    电子邮箱
                  </div>
                  <div class="info-value contact-value">
                    <div class="contact-icon">
                      <el-icon><Message /></el-icon>
                    </div>
                    <span>{{ userInfo.email || '未设置' }}</span>
                  </div>
                </div>
              </el-col>

              <el-col :xs="24" :sm="12">
                <div class="info-item">
                  <div class="info-label">
                    <div class="label-dot"></div>
                    账户状态
                  </div>
                  <div class="info-value">
                    <el-tag :type="getStatusType(userInfo.accountStatus)" class="custom-tag">
                      {{ userInfo.accountStatus === 1 ? '正常' : '已禁用' }}
                    </el-tag>
                  </div>
                </div>
              </el-col>

              <el-col :xs="24" :sm="12">
                <div class="info-item">
                  <div class="info-label">
                    <div class="label-dot"></div>
                    认证信息
                  </div>
                  <div class="info-value">
                    {{ userInfo.certificationInfo || '未认证' }}
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>

        <!-- 商铺信息卡片 - 始终显示，但内容可能为"未设置" -->
        <el-card class="profile-card shop-info-card">
          <template #header>
            <div class="card-header">
              <div class="header-icon-wrapper shop-icon">
                <el-icon><Shop /></el-icon>
              </div>
              <span>商铺信息</span>
            </div>
          </template>

          <div class="info-section">
            <el-row :gutter="30">
              <el-col :xs="24" :sm="12">
                <div class="info-item">
                  <div class="info-label">
                    <div class="label-dot shop-dot"></div>
                    商铺名称
                  </div>
                  <div class="info-value shop-value">
                    {{ userInfo.shopName || '未设置' }}
                  </div>
                </div>
              </el-col>

              <el-col :xs="24" :sm="12">
                <div class="info-item">
                  <div class="info-label">
                    <div class="label-dot shop-dot"></div>
                    公司名称
                  </div>
                  <div class="info-value shop-value">
                    {{ userInfo.companyName || '未设置' }}
                  </div>
                </div>
              </el-col>

              <el-col :xs="24" :sm="12">
                <div class="info-item">
                  <div class="info-label">
                    <div class="label-dot shop-dot"></div>
                    认证状态
                  </div>
                  <div class="info-value">
                    <el-tag
                      :type="userInfo.certificationInfo ? 'success' : 'info'"
                      effect="light"
                      class="cert-tag"
                    >
                      {{ userInfo.certificationInfo || '未认证' }}
                    </el-tag>
                  </div>
                </div>
              </el-col>

              <el-col :xs="24" :sm="12">
                <div class="info-item">
                  <div class="info-label">
                    <div class="label-dot shop-dot"></div>
                    注册日期
                  </div>
                  <div class="info-value shop-date">
                    {{ formatDate(userInfo.createTime) }}
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 收款账户管理区域 -->
    <div class="payment-account-section">
      <el-card class="profile-card payment-account-card">
        <template #header>
          <div class="card-header">
            <div class="header-icon">
              <el-icon><CreditCard /></el-icon>
            </div>
            <div class="header-text">
              <h3>收款账户管理</h3>
              <p>管理您的收款账户信息，设置默认收款方式</p>
            </div>
          </div>
        </template>

        <div class="payment-account-content">
          <PaymentAccountManager />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  User,
  Phone,
  Message,
  Check,
  Shop,
  Calendar,
  Timer,
  Refresh,
  CreditCard,
} from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import { getSellerById } from '@/api/sellerAdmin'
import { getAvatarUrl } from '@/utils/imageHelpers'
import PaymentAccountManager from '@/components/PaymentAccountManager.vue'

const userStore = useUserStore()
const loading = ref(false)
const initialLoading = ref(true) // 首次加载状态

// 用户信息
const userInfo = reactive({
  id: 0,
  accountName: '',
  gender: '保密',
  phone: '',
  email: '',
  accountStatus: 1,
  photoUrl: '',
  shopName: '',
  companyName: '',
  createTime: '',
  lastLoginTime: '',
  certificationInfo: '',
  verificationCode: '',
})

// 获取用户信息
const getUserInfo = async () => {
  if (!userStore.userInfo?.id) {
    ElMessage.warning('未获取到用户ID，请重新登录')
    return
  }

  loading.value = true
  try {
    const sellerId = userStore.userInfo.id
    console.log('获取商家详情，ID:', sellerId)

    const response = await getSellerById(sellerId)
    console.log('商家详情原始响应:', response)

    if (response.code === 1 && response.data) {
      // 处理嵌套数据结构
      const data = response.data

      // 检查是否有嵌套的seller和evpi结构
      if (data.seller) {
        // 使用seller数据
        Object.assign(userInfo, {
          id: data.seller.id || 0,
          accountName: data.seller.accountName || '',
          gender: data.seller.gender === 1 ? '男' : data.seller.gender === 2 ? '女' : '保密',
          phone: data.seller.phone || '',
          email: data.seller.email || '',
          accountStatus: data.seller.accountStatus ?? 1,
          photoUrl: data.seller.photoUrl || '',
          createTime: data.seller.createTime || '',
          lastLoginTime: data.seller.lastLoginTime || '',
        })

        // 如果有evpi数据，补充商铺信息
        if (data.evpi) {
          userInfo.shopName = data.evpi.shopName || ''
          userInfo.companyName = data.evpi.companyName || ''
          userInfo.certificationInfo = data.seller.accountStatus === 1 ? '已认证' : '未认证'
        } else {
          // 没有evpi数据时，设置为空值
          userInfo.shopName = ''
          userInfo.companyName = ''
          userInfo.certificationInfo = '未认证'
        }

        // 如果有新的头像，更新到userStore
        if (data.seller.photoUrl && data.seller.photoUrl !== userStore.userInfo.avatar) {
          userStore.setUserInfo({
            ...userStore.userInfo,
            avatar: data.seller.photoUrl,
          })
        }
      } else {
        // 扁平结构数据处理（兼容旧格式）
        Object.assign(userInfo, {
          id: data.id || 0,
          accountName: data.accountName || '',
          gender: data.gender === 1 ? '男' : data.gender === 2 ? '女' : '保密',
          phone: data.phone || '',
          email: data.email || '',
          accountStatus: data.accountStatus ?? 1,
          photoUrl: data.photoUrl || '',
          createTime: data.createTime || '',
          lastLoginTime: data.lastLoginTime || '',
          shopName: data.shopName || '',
          companyName: data.companyName || '',
          certificationInfo: data.accountStatus === 1 ? '已认证' : '未认证',
        })

        // 如果有新的头像，更新到userStore
        if (data.photoUrl && data.photoUrl !== userStore.userInfo.avatar) {
          userStore.setUserInfo({
            ...userStore.userInfo,
            avatar: data.photoUrl,
          })
        }
      }

      console.log('处理后的用户信息:', userInfo)
      ElMessage.success('个人信息加载成功')
    } else {
      ElMessage.warning('获取商家信息失败: ' + (response.msg || '未知错误'))
    }
  } catch (error) {
    console.error('获取商家详情出错:', error)
    ElMessage.error('获取商家信息失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 获取状态类型
const getStatusType = (status: number | string) => {
  if (typeof status === 'number') {
    return status === 1 ? 'success' : 'danger'
  }
  return status === '正常' ? 'success' : 'danger'
}

// 格式化日期
const formatDate = (dateStr?: string) => {
  if (!dateStr) return '未知'
  try {
    const date = new Date(dateStr)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  } catch (e) {
    return dateStr
  }
}

// 组件挂载时获取用户信息
onMounted(async () => {
  console.log('用户store中的ID:', userStore.userInfo?.id)
  console.log('开始加载用户信息')

  initialLoading.value = true
  await getUserInfo()
  initialLoading.value = false

  console.log('用户信息加载完成，当前数据:', userInfo)

  // 如果用户有头像但URL不可用，生成基于名称的头像
  if (userInfo.photoUrl) {
    userInfo.photoUrl = await getAvatarUrl(userInfo.photoUrl, userInfo.accountName)
    console.log('处理后的头像URL:', userInfo.photoUrl)
  }
})
</script>

<style scoped lang="scss">
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.05);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0.8;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.profile-container {
  padding: 30px;
  background-color: #f8fafc;
  min-height: calc(100vh - 60px);
  position: relative;
  overflow: hidden;

  // 装饰元素
  .decorative-shapes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    overflow: hidden;
    pointer-events: none;

    .shape {
      position: absolute;
      border-radius: 50%;
      opacity: 0.4;

      &.shape-1 {
        top: -100px;
        right: -50px;
        width: 300px;
        height: 300px;
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        animation: float 8s ease-in-out infinite;
      }

      &.shape-2 {
        bottom: -80px;
        left: -80px;
        width: 200px;
        height: 200px;
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        animation: float 10s ease-in-out infinite;
      }

      &.shape-3 {
        top: 40%;
        right: 10%;
        width: 100px;
        height: 100px;
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        animation: float 6s ease-in-out infinite;
      }
    }
  }

  .profile-header {
    position: relative;
    z-index: 1;
    margin-bottom: 40px;
    text-align: center;

    .header-content {
      display: inline-block;
      padding: 15px 40px;
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(10px);
      border-radius: 16px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
      animation: slideIn 0.6s ease-out;
      position: relative;

      .refresh-btn {
        position: absolute;
        right: 20px;
        top: 50%;
        transform: translateY(-50%);
        border-radius: 20px;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-50%) scale(1.05);
          background: linear-gradient(120deg, #4facfe, #00f2fe);
        }
      }
    }

    .header-title {
      margin: 0 0 10px 0;
      font-size: 32px;
      font-weight: 700;
      background: linear-gradient(120deg, #4facfe, #00f2fe);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      letter-spacing: 0.5px;
    }

    .header-subtitle {
      margin: 0;
      font-size: 16px;
      color: #5d7290;
      font-weight: 500;
    }
  }

  .profile-card {
    position: relative;
    z-index: 1;
    border-radius: 16px;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    margin-bottom: 30px;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: none;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    animation: slideIn 0.6s ease-out;

    &:hover {
      transform: translateY(-8px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
    }

    .card-header {
      display: flex;
      align-items: center;
      gap: 12px;
      font-size: 18px;
      font-weight: 600;
      color: #2c3e50;
      padding: 16px 20px;
      border-bottom: 1px solid rgba(200, 215, 235, 0.3);
      background: linear-gradient(to right, rgba(255, 255, 255, 0.8), rgba(240, 247, 255, 0.8));

      .header-icon-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        border-radius: 10px;
        background: linear-gradient(135deg, #4facfe, #00f2fe);
        box-shadow: 0 4px 10px rgba(79, 172, 254, 0.3);

        &.shop-icon {
          background: linear-gradient(135deg, #43e97b, #38f9d7);
          box-shadow: 0 4px 10px rgba(67, 233, 123, 0.3);
        }

        .el-icon {
          font-size: 18px;
          color: white;
        }
      }
    }

    &.user-info-card {
      .card-decoration {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 120px;
        background: linear-gradient(135deg, #4facfe, #00f2fe);
        z-index: 0;

        .decoration-circle {
          position: absolute;
          bottom: -50px;
          right: -50px;
          width: 100px;
          height: 100px;
          border-radius: 50%;
          background: rgba(255, 255, 255, 0.2);
        }
      }

      .avatar-container {
        position: relative;
        z-index: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 40px 0 30px;

        .avatar-ring {
          position: absolute;
          top: 40px;
          width: 130px;
          height: 130px;
          border-radius: 50%;
          border: 3px dashed rgba(255, 255, 255, 0.6);
          animation: rotate 20s linear infinite;
        }

        .avatar-wrapper {
          position: relative;
          margin-bottom: 20px;
          z-index: 2;

          .user-avatar {
            border: 5px solid white;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;

            &:hover {
              transform: scale(1.05);
              box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
            }
          }

          .status-badge {
            position: absolute;
            bottom: 5px;
            right: 5px;
            width: 28px;
            height: 28px;
            border-radius: 50%;
            background-color: #f56c6c;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 3px solid white;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;

            &.status-active {
              background-color: #67c23a;
              animation: pulse 2s infinite;
            }
          }
        }

        .user-info-text {
          text-align: center;
          z-index: 2;
        }

        .user-name {
          margin: 8px 0 4px;
          font-size: 22px;
          font-weight: 700;
          color: #2c3e50;
        }

        .user-id {
          margin: 0 0 16px;
          font-size: 14px;
          color: #5d7290;
          font-weight: 500;
        }

        .status-tag {
          margin-bottom: 20px;
          padding: 0 16px;
          font-size: 14px;
          height: 28px;
          line-height: 28px;
          border-radius: 14px;
          font-weight: 500;
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
      }

      .user-stats {
        display: flex;
        flex-direction: column;
        gap: 16px;
        padding: 20px;
        background-color: #f8fafc;
        border-radius: 0 0 16px 16px;

        .stat-item {
          display: flex;
          align-items: center;
          gap: 16px;
          padding: 12px 16px;
          border-radius: 12px;
          background-color: white;
          transition: all 0.3s ease;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

          &:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
          }

          .stat-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 10px;
            background: linear-gradient(135deg, #4facfe, #00f2fe);
            color: white;

            .el-icon {
              font-size: 18px;
            }
          }

          .stat-content {
            flex: 1;
          }

          .stat-label {
            font-size: 14px;
            color: #5d7290;
            margin-bottom: 4px;
            font-weight: 500;
          }

          .stat-value {
            font-size: 15px;
            font-weight: 600;
            color: #2c3e50;
          }
        }
      }
    }

    &.info-card,
    &.shop-info-card {
      .info-section {
        padding: 25px;

        .info-item {
          margin-bottom: 25px;
          position: relative;
          animation: slideIn 0.4s ease-out;
          animation-fill-mode: both;

          @for $i from 1 through 6 {
            &:nth-child(#{$i}) {
              animation-delay: #{$i * 0.1}s;
            }
          }

          .info-label {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 15px;
            color: #5d7290;
            margin-bottom: 10px;
            font-weight: 600;

            .label-dot {
              width: 8px;
              height: 8px;
              border-radius: 50%;
              background: linear-gradient(135deg, #4facfe, #00f2fe);

              &.shop-dot {
                background: linear-gradient(135deg, #43e97b, #38f9d7);
              }
            }
          }

          .info-value {
            font-size: 16px;
            color: #2c3e50;
            font-weight: 500;
            padding: 10px 16px;
            background-color: #f8fafc;
            border-radius: 10px;
            transition: all 0.3s ease;

            &:hover {
              background-color: #f0f7ff;
              transform: translateX(5px);
            }

            &.account-name {
              font-weight: 600;
              color: #4facfe;
            }

            &.shop-value {
              background-color: rgba(67, 233, 123, 0.1);
              color: #43e97b;
              font-weight: 600;
            }

            &.shop-date {
              background-color: rgba(67, 233, 123, 0.05);
            }

            .custom-tag {
              font-size: 14px;
              padding: 0 12px;
              height: 28px;
              line-height: 28px;
              border-radius: 14px;
            }

            .cert-tag {
              font-size: 14px;
              padding: 0 12px;
              height: 28px;
              line-height: 28px;
              border-radius: 14px;
              background-color: rgba(67, 233, 123, 0.1);
            }
          }

          .contact-value {
            display: flex;
            align-items: center;
            gap: 12px;

            .contact-icon {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 36px;
              height: 36px;
              border-radius: 10px;
              background: linear-gradient(135deg, #4facfe, #00f2fe);
              color: white;
              transition: all 0.3s ease;

              &:hover {
                transform: rotate(15deg);
              }

              .el-icon {
                font-size: 18px;
              }
            }
          }
        }
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .profile-container {
    padding: 20px;

    .profile-header {
      margin-bottom: 30px;

      .header-content {
        padding: 15px 25px;
      }

      .header-title {
        font-size: 26px;
      }

      .header-subtitle {
        font-size: 14px;
      }
    }

    .profile-card {
      margin-bottom: 24px;

      .card-header {
        padding: 14px 16px;
        font-size: 16px;
      }

      &.user-info-card {
        .avatar-container {
          padding: 30px 0 20px;

          .avatar-ring {
            width: 120px;
            height: 120px;
          }

          .avatar-wrapper {
            margin-bottom: 16px;

            .status-badge {
              width: 24px;
              height: 24px;
            }
          }

          .user-name {
            font-size: 20px;
          }
        }

        .user-stats {
          padding: 16px;
          gap: 12px;

          .stat-item {
            padding: 10px 12px;

            .stat-icon {
              width: 36px;
              height: 36px;
            }
          }
        }
      }

      &.info-card,
      &.shop-info-card {
        .info-section {
          padding: 20px;

          .info-item {
            margin-bottom: 20px;

            .info-label {
              font-size: 14px;
            }

            .info-value {
              font-size: 15px;
              padding: 8px 12px;
            }
          }
        }
      }
    }
  }
}

// 收款账户管理区域样式
.payment-account-section {
  margin-top: 24px;

  .payment-account-card {
    .card-header {
      display: flex;
      align-items: center;
      gap: 12px;

      .header-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 18px;
      }

      .header-text {
        h3 {
          margin: 0 0 4px 0;
          font-size: 18px;
          font-weight: 600;
          color: #303133;
        }

        p {
          margin: 0;
          font-size: 14px;
          color: #909399;
        }
      }
    }
  }

  .payment-account-content {
    padding: 0;
    margin: -20px;
    margin-top: 0;
  }
}
</style>
