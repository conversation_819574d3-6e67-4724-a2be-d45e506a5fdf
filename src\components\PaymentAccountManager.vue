<template>
  <div class="payment-account-manager">
    <!-- 操作栏 -->
    <div class="action-bar">
      <div class="action-left">
        <span class="account-count">共 {{ accountList.length }} 个收款账户</span>
      </div>
      <div class="action-right">
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          添加账户
        </el-button>
        <el-button @click="loadAccountList">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 账户列表 -->
    <div class="account-list" v-loading="loading">
      <!-- 账户卡片 -->
      <div
        v-for="account in accountList"
        :key="account.id"
        class="account-item"
        :class="{ 'is-default': account.isDefault === 1, 'is-disabled': account.accountStatus !== 1 }"
      >
        <div class="account-content">
          <!-- 账户头部 -->
          <div class="account-header">
            <div class="account-type-section">
              <div class="type-icon">
                <el-icon v-if="account.accountType === 1"><CreditCard /></el-icon>
                <el-icon v-else-if="account.accountType === 2"><Money /></el-icon>
                <el-icon v-else-if="account.accountType === 3"><ChatDotRound /></el-icon>
                <el-icon v-else><Wallet /></el-icon>
              </div>
              <div class="type-info">
                <div class="type-name">{{ account.accountTypeDesc }}</div>
                <div class="account-name">{{ account.accountName }}</div>
              </div>
            </div>
            <div class="account-badges">
              <el-tag v-if="account.isDefault === 1" type="success" size="small" effect="dark">
                默认
              </el-tag>
              <el-tag
                :type="account.verificationStatus === 1 ? 'success' : 'warning'"
                size="small"
                effect="plain"
              >
                {{ account.verificationStatusDesc }}
              </el-tag>
              <el-tag
                :type="account.accountStatus === 1 ? 'success' : 'danger'"
                size="small"
                effect="plain"
              >
                {{ account.accountStatusDesc }}
              </el-tag>
            </div>
          </div>

          <!-- 账户详情 -->
          <div class="account-details">
            <div class="detail-row">
              <span class="detail-label">账户号码</span>
              <span class="detail-value">{{ account.accountNumber }}</span>
            </div>
            <div v-if="account.bankName" class="detail-row">
              <span class="detail-label">银行名称</span>
              <span class="detail-value">{{ account.bankName }}</span>
            </div>
            <div v-if="account.branchName" class="detail-row">
              <span class="detail-label">开户支行</span>
              <span class="detail-value">{{ account.branchName }}</span>
            </div>
            <div v-if="account.platformAccount" class="detail-row">
              <span class="detail-label">平台账号</span>
              <span class="detail-value">{{ account.platformAccount }}</span>
            </div>
            <div v-if="account.remark" class="detail-row">
              <span class="detail-label">备注</span>
              <span class="detail-value">{{ account.remark }}</span>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="account-actions">
            <el-button type="primary" size="small" @click="handleEdit(account)">
              编辑
            </el-button>
            <el-button
              v-if="account.isDefault !== 1"
              type="success"
              size="small"
              @click="handleSetDefault(account.id)"
            >
              设为默认
            </el-button>
            <el-dropdown @command="(command) => handleDropdownCommand(command, account)">
              <el-button size="small">
                更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    :command="`${account.accountStatus === 1 ? 'disable' : 'enable'}-${account.id}`"
                  >
                    {{ account.accountStatus === 1 ? '禁用账户' : '启用账户' }}
                  </el-dropdown-item>
                  <el-dropdown-item :command="`delete-${account.id}`" divided>
                    删除账户
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="accountList.length === 0 && !loading" class="empty-state">
        <el-empty description="暂无收款账户">
          <el-button type="primary" @click="handleAdd">添加第一个收款账户</el-button>
        </el-empty>
      </div>
    </div>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑收款账户' : '添加收款账户'"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item label="账户类型" prop="accountType">
          <el-select v-model="formData.accountType" placeholder="请选择账户类型" @change="handleAccountTypeChange">
            <el-option label="银行卡" :value="1" />
            <el-option label="支付宝" :value="2" />
            <el-option label="微信" :value="3" />
            <el-option label="其他" :value="4" />
          </el-select>
        </el-form-item>

        <el-form-item label="账户名称" prop="accountName">
          <el-input v-model="formData.accountName" placeholder="请输入账户名称（持卡人/账户持有人姓名）" />
        </el-form-item>

        <el-form-item label="账户号码" prop="accountNumber">
          <el-input v-model="formData.accountNumber" placeholder="请输入账户号码" />
        </el-form-item>

        <!-- 银行卡特有字段 -->
        <template v-if="formData.accountType === 1">
          <el-form-item label="银行名称" prop="bankName">
            <el-input v-model="formData.bankName" placeholder="请输入银行名称" />
          </el-form-item>
          <el-form-item label="开户支行" prop="branchName">
            <el-input v-model="formData.branchName" placeholder="请输入开户支行" />
          </el-form-item>
          <el-form-item label="银行代码">
            <el-input v-model="formData.bankCode" placeholder="请输入银行代码（可选）" />
          </el-form-item>
        </template>

        <!-- 支付宝/微信特有字段 -->
        <template v-if="formData.accountType === 2 || formData.accountType === 3">
          <el-form-item label="平台账号">
            <el-input v-model="formData.platformAccount" :placeholder="`请输入${formData.accountType === 2 ? '支付宝' : '微信'}账号`" />
          </el-form-item>
        </template>

        <el-form-item label="身份证号码">
          <el-input v-model="formData.idCardNumber" placeholder="请输入身份证号码（可选）">
            <template #suffix>
              <el-tooltip content="用于账户验证，提高安全性" placement="top">
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="手机号码">
          <el-input v-model="formData.phone" placeholder="请输入手机号码（可选）">
            <template #suffix>
              <el-tooltip content="用于接收重要通知" placement="top">
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="设为默认">
          <el-switch v-model="isDefaultAccount" />
        </el-form-item>

        <el-form-item label="备注">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息（可选）"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
            {{ isEdit ? '更新' : '添加' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Refresh,
  ArrowDown,
  QuestionFilled,
  CreditCard,
  Money,
  ChatDotRound,
  Wallet
} from '@element-plus/icons-vue'
import {
  getPaymentAccountList,
  addPaymentAccount,
  updatePaymentAccount,
  deletePaymentAccount,
  setDefaultPaymentAccount,
  enablePaymentAccount,
  disablePaymentAccount,
  validateBankCardNumber,
  validateIdCardNumber,
  validatePhoneNumber
} from '@/api/paymentAccount'
import type {
  PaymentAccountVO,
  PaymentAccountDTO,
  AccountType
} from '@/types/paymentAccount'

// 响应式数据
const accountList = ref<PaymentAccountVO[]>([])
const loading = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const submitLoading = ref(false)
const formRef = ref()

// 表单数据
const formData = reactive<PaymentAccountDTO>({
  accountType: 1,
  accountName: '',
  accountNumber: '',
  bankName: '',
  bankCode: '',
  branchName: '',
  platformAccount: '',
  idCardNumber: '',
  phone: '',
  remark: ''
})

// 是否设为默认账户
const isDefaultAccount = ref(false)

// 计算属性
const isDefaultAccountValue = computed(() => isDefaultAccount.value ? 1 : 0)

// 表单验证规则
const formRules = {
  accountType: [
    { required: true, message: '请选择账户类型', trigger: 'change' }
  ],
  accountName: [
    { required: true, message: '请输入账户名称', trigger: 'blur' }
  ],
  accountNumber: [
    { required: true, message: '请输入账户号码', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: any) => {
        if (formData.accountType === 1 && value && !validateBankCardNumber(value)) {
          callback(new Error('请输入正确的银行卡号'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  bankName: [
    {
      validator: (rule: any, value: string, callback: any) => {
        if (formData.accountType === 1 && !value) {
          callback(new Error('银行卡类型必须填写银行名称'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  branchName: [
    {
      validator: (rule: any, value: string, callback: any) => {
        if (formData.accountType === 1 && !value) {
          callback(new Error('银行卡类型必须填写开户支行'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 获取账户类型标签类型
const getAccountTypeTagType = (accountType: AccountType): string => {
  switch (accountType) {
    case 1: return 'primary'  // 银行卡
    case 2: return 'success'  // 支付宝
    case 3: return 'warning'  // 微信
    case 4: return 'info'     // 其他
    default: return 'info'
  }
}

// 加载账户列表
const loadAccountList = async () => {
  loading.value = true
  try {
    const response = await getPaymentAccountList()
    if (response.code === 1) {
      accountList.value = response.data || []
    } else {
      ElMessage.error(response.msg || '加载账户列表失败')
    }
  } catch (error) {
    console.error('加载账户列表失败:', error)
    ElMessage.error('加载账户列表失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    accountType: 1,
    accountName: '',
    accountNumber: '',
    bankName: '',
    bankCode: '',
    branchName: '',
    platformAccount: '',
    idCardNumber: '',
    phone: '',
    remark: ''
  })
  isDefaultAccount.value = false
  isEdit.value = false
  formRef.value?.clearValidate()
}

// 处理添加
const handleAdd = () => {
  resetForm()
  dialogVisible.value = true
}

// 处理编辑
const handleEdit = (account: PaymentAccountVO) => {
  isEdit.value = true
  Object.assign(formData, {
    id: account.id,
    accountType: account.accountType,
    accountName: account.accountName,
    accountNumber: account.fullAccountNumber || account.accountNumber,
    bankName: account.bankName || '',
    bankCode: account.bankCode || '',
    branchName: account.branchName || '',
    platformAccount: account.platformAccount || '',
    idCardNumber: account.idCardNumber || '',
    phone: account.phone || '',
    remark: account.remark || ''
  })
  isDefaultAccount.value = account.isDefault === 1
  dialogVisible.value = true
}

// 处理账户类型变化
const handleAccountTypeChange = () => {
  // 清空相关字段
  formData.bankName = ''
  formData.bankCode = ''
  formData.branchName = ''
  formData.platformAccount = ''
}

// 处理提交
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    submitLoading.value = true
    
    const submitData = {
      ...formData,
      isDefault: isDefaultAccountValue.value
    }
    
    const response = isEdit.value
      ? await updatePaymentAccount(submitData)
      : await addPaymentAccount(submitData)
    
    if (response.code === 1) {
      ElMessage.success(isEdit.value ? '更新成功' : '添加成功')
      dialogVisible.value = false
      loadAccountList()
    } else {
      ElMessage.error(response.msg || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败，请稍后重试')
  } finally {
    submitLoading.value = false
  }
}

// 处理设置默认账户
const handleSetDefault = async (id: number) => {
  try {
    const response = await setDefaultPaymentAccount(id)
    if (response.code === 1) {
      ElMessage.success('设置默认账户成功')
      loadAccountList()
    } else {
      ElMessage.error(response.msg || '设置失败')
    }
  } catch (error) {
    console.error('设置默认账户失败:', error)
    ElMessage.error('设置失败，请稍后重试')
  }
}

// 处理下拉菜单命令
const handleDropdownCommand = async (command: string, account: PaymentAccountVO) => {
  const [action, id] = command.split('-')
  const accountId = parseInt(id)
  
  if (action === 'enable' || action === 'disable') {
    try {
      const response = action === 'enable'
        ? await enablePaymentAccount(accountId)
        : await disablePaymentAccount(accountId)
      
      if (response.code === 1) {
        ElMessage.success(`${action === 'enable' ? '启用' : '禁用'}成功`)
        loadAccountList()
      } else {
        ElMessage.error(response.msg || '操作失败')
      }
    } catch (error) {
      console.error('状态变更失败:', error)
      ElMessage.error('操作失败，请稍后重试')
    }
  } else if (action === 'delete') {
    ElMessageBox.confirm(
      `确定要删除账户"${account.accountName}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(async () => {
      try {
        const response = await deletePaymentAccount(accountId)
        if (response.code === 1) {
          ElMessage.success('删除成功')
          loadAccountList()
        } else {
          ElMessage.error(response.msg || '删除失败')
        }
      } catch (error) {
        console.error('删除失败:', error)
        ElMessage.error('删除失败，请稍后重试')
      }
    }).catch(() => {
      // 用户取消删除
    })
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadAccountList()
})
</script>

<style scoped>
.payment-account-manager {
  padding: 0;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.action-left .account-count {
  color: #606266;
  font-size: 14px;
}

.action-right {
  display: flex;
  gap: 12px;
}

.account-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.account-item {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  transition: all 0.3s ease;
  overflow: hidden;
}

.account-item:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border-color: #409eff;
}

.account-item.is-default {
  border-color: #67c23a;
  background: linear-gradient(135deg, #f0f9ff 0%, #ecfdf5 100%);
}

.account-item.is-disabled {
  opacity: 0.6;
  background: #f5f7fa;
}

.account-content {
  padding: 20px;
}

.account-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.account-type-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.type-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.type-info .type-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.type-info .account-name {
  font-size: 14px;
  color: #606266;
}

.account-badges {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.account-details {
  margin-bottom: 16px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.detail-row {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-label {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.detail-value {
  font-size: 14px;
  color: #303133;
  word-break: break-all;
}

.account-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
