<template>
  <div class="admin-settlement">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>回款管理</h2>
      <p>管理所有商家的回款信息</p>
    </div>

    <!-- 统计概览 -->
    <div class="overview-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon total">
                <el-icon><Money /></el-icon>
              </div>
              <div class="card-info">
                <h3>{{ formatCurrency(overview.totalAmount) }}</h3>
                <p>总回款金额</p>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon pending">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="card-info">
                <h3>{{ overview.pendingCount }}</h3>
                <p>待回款订单</p>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon completed">
                <el-icon><Check /></el-icon>
              </div>
              <div class="card-info">
                <h3>{{ overview.completedCount }}</h3>
                <p>已回款订单</p>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon merchants">
                <el-icon><Shop /></el-icon>
              </div>
              <div class="card-info">
                <h3>{{ overview.merchantCount }}</h3>
                <p>商家数量</p>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 查询条件 -->
    <el-card class="search-card">
      <el-form :model="queryForm" :inline="true" class="search-form">
        <el-form-item label="订单号">
          <el-input
            v-model="queryForm.orderNumber"
            placeholder="请输入订单号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="商家名称">
          <el-input
            v-model="queryForm.sellerName"
            placeholder="请输入商家名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="回款状态">
          <el-select v-model="queryForm.settlementStatus" placeholder="请选择状态" clearable style="width: 150px">
            <el-option label="未到期" :value="0" />
            <el-option label="待回款" :value="1" />
            <el-option label="已回款" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="回款日期">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮 -->
    <el-card class="action-card">
      <div class="action-buttons">
        <el-button
          type="success"
          :disabled="selectedRows.length === 0"
          @click="handleBatchComplete"
        >
          <el-icon><Check /></el-icon>
          批量回款完成
        </el-button>
        <el-button type="warning" @click="handleUpdateStatus">
          <el-icon><Refresh /></el-icon>
          更新回款状态
        </el-button>
        <el-button type="primary" @click="handleExport">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </div>
    </el-card>

    <!-- 回款订单表格 -->
    <el-card class="table-card">
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="orderNumber" label="订单号" width="180" />
        <el-table-column prop="sellerName" label="商家名称" width="150" />
        <el-table-column prop="orderAmount" label="订单金额" width="120" sortable>
          <template #default="{ row }">
            <span class="amount">{{ formatCurrency(row.orderAmount) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="settlementAmount" label="回款金额" width="120" sortable>
          <template #default="{ row }">
            <span class="amount settlement">{{ formatCurrency(row.settlementAmount) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="billingDate" label="账单日期" width="120">
          <template #default="{ row }">
            {{ formatDate(row.billingDate) }}
          </template>
        </el-table-column>
        <el-table-column prop="settlementDate" label="回款日期" width="120">
          <template #default="{ row }">
            {{ formatDate(row.settlementDate) }}
          </template>
        </el-table-column>
        <el-table-column prop="settlementStatus" label="回款状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.settlementStatus)">
              {{ row.settlementStatusDesc }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="daysToSettlement" label="距离回款" width="100">
          <template #default="{ row }">
            <span v-if="row.settlementStatus === 0" class="days-info">
              {{ row.daysToSettlement }}天后
            </span>
            <span v-else-if="row.settlementStatus === 1" class="days-info overdue">
              {{ row.daysToSettlement < 0 ? `逾期${Math.abs(row.daysToSettlement)}天` : `${row.daysToSettlement}天内` }}
            </span>
            <span v-else-if="row.settlementStatus === 2" class="days-info completed">
              已完成
            </span>
            <span v-else class="days-info">
              -
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="settlementTime" label="完成时间" width="160">
          <template #default="{ row }">
            {{ row.settlementTime ? formatDateTime(row.settlementTime) : '-' }}
          </template>
        </el-table-column>
        <el-table-column label="收款账户" width="200">
          <template #default="{ row }">
            <div v-if="row.paymentAccount" class="payment-account-info">
              <div class="account-type">
                <el-tag :type="getAccountTypeTagType(row.paymentAccount.accountType)" size="small">
                  {{ row.paymentAccount.accountTypeDesc }}
                </el-tag>
              </div>
              <div class="account-details">
                <div class="account-name">{{ row.paymentAccount.accountName }}</div>
                <div class="account-number">{{ row.paymentAccount.accountNumber }}</div>
                <div v-if="row.paymentAccount.bankName" class="bank-name">{{ row.paymentAccount.bankName }}</div>
              </div>
            </div>
            <span v-else class="no-account">未设置</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button
              v-if="row.settlementStatus === 1"
              type="primary"
              size="small"
              @click="handleConfirmSettlement(row)"
            >
              确认回款
            </el-button>
            <span v-else class="no-action">-</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryForm.page"
          v-model:page-size="queryForm.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 确认回款对话框 -->
    <el-dialog
      v-model="completeDialogVisible"
      title="确认回款"
      width="700px"
      :close-on-click-modal="false"
    >
      <div class="settlement-confirm-content">
        <!-- 订单信息 -->
        <el-card class="order-info-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Document /></el-icon>
              <span>订单信息</span>
            </div>
          </template>
          <div class="order-details">
            <div class="detail-row">
              <span class="label">订单号：</span>
              <span class="value">{{ currentRow.orderNumber }}</span>
            </div>
            <div class="detail-row">
              <span class="label">商家名称：</span>
              <span class="value">{{ currentRow.sellerName }}</span>
            </div>
            <div class="detail-row">
              <span class="label">订单金额：</span>
              <span class="value amount">{{ formatCurrency(currentRow.orderAmount) }}</span>
            </div>
            <div class="detail-row">
              <span class="label">回款金额：</span>
              <span class="value amount settlement">{{ formatCurrency(currentRow.settlementAmount) }}</span>
            </div>
            <div class="detail-row">
              <span class="label">回款日期：</span>
              <span class="value">{{ formatDate(currentRow.settlementDate) }}</span>
            </div>
          </div>
        </el-card>

        <!-- 收款账户信息 -->
        <el-card class="account-info-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><CreditCard /></el-icon>
              <span>收款账户信息</span>
            </div>
          </template>
          <div v-if="currentRow.paymentAccount" class="account-details">
            <div class="detail-row">
              <span class="label">账户类型：</span>
              <el-tag :type="getAccountTypeTagType(currentRow.paymentAccount.accountType)">
                {{ currentRow.paymentAccount.accountTypeDesc }}
              </el-tag>
            </div>
            <div class="detail-row">
              <span class="label">账户名称：</span>
              <span class="value">{{ currentRow.paymentAccount.accountName }}</span>
            </div>
            <div class="detail-row">
              <span class="label">账户号码：</span>
              <span class="value account-number">{{ currentRow.paymentAccount.fullAccountNumber || currentRow.paymentAccount.accountNumber }}</span>
              <el-button
                type="text"
                size="small"
                @click="copyAccountNumber"
                v-if="currentRow.paymentAccount.fullAccountNumber || currentRow.paymentAccount.accountNumber"
              >
                复制
              </el-button>
            </div>
            <div v-if="currentRow.paymentAccount.bankName" class="detail-row">
              <span class="label">银行名称：</span>
              <span class="value">{{ currentRow.paymentAccount.bankName }}</span>
            </div>
            <div v-if="currentRow.paymentAccount.branchName" class="detail-row">
              <span class="label">开户支行：</span>
              <span class="value">{{ currentRow.paymentAccount.branchName }}</span>
            </div>
            <div v-if="currentRow.paymentAccount.platformAccount" class="detail-row">
              <span class="label">平台账号：</span>
              <span class="value">{{ currentRow.paymentAccount.platformAccount }}</span>
            </div>
          </div>
          <div v-else class="no-account-warning">
            <el-alert
              title="该商家未设置收款账户"
              description="请联系商家设置收款账户后再进行回款操作"
              type="warning"
              show-icon
              :closable="false"
            />
          </div>
        </el-card>

        <!-- 打款确认 -->
        <el-card class="payment-confirm-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Check /></el-icon>
              <span>打款确认</span>
            </div>
          </template>
          <div class="payment-confirm-content">
            <el-alert
              title="请按照上述收款账户信息进行转账"
              description="转账完成后，请在下方填写备注信息并点击"完成打款"按钮"
              type="info"
              show-icon
              :closable="false"
            />
            <el-form :model="completeForm" label-width="100px" style="margin-top: 20px;">
              <el-form-item label="打款备注">
                <el-input
                  v-model="completeForm.remark"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入打款相关备注信息，如：转账流水号、打款时间等（可选）"
                />
              </el-form-item>
            </el-form>
          </div>
        </el-card>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="completeDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="confirmComplete"
            :loading="completeLoading"
            :disabled="!currentRow.paymentAccount"
          >
            完成打款
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Check,
  Download,
  Money,
  Clock,
  Shop,
  Document,
  CreditCard
} from '@element-plus/icons-vue'
import {
  getAdminSettlementOrders,
  getAdminSettlementSummary,
  completeSettlement,
  updateSettlementStatus,
  createSettlementForOrder,
  confirmSettlement
} from '@/api/settlement'
import type {
  SettlementQueryDTO,
  SettlementInfoVO,
  SettlementSummaryVO,
  SettlementCompleteDTO
} from '@/types/settlement'
import { getSellerPaymentAccounts } from '@/api/paymentAccount'
import type { PaymentAccountVO } from '@/types/paymentAccount'

// 响应式数据
const tableLoading = ref(false)
const completeLoading = ref(false)
const tableData = ref<SettlementInfoVO[]>([])
const selectedRows = ref<SettlementInfoVO[]>([])
const total = ref(0)
const dateRange = ref<[string, string] | null>(null)
const completeDialogVisible = ref(false)
const currentRow = ref<SettlementInfoVO>({} as SettlementInfoVO)

// 概览数据 - 使用汇总数据的第一条记录作为概览
const overview = ref({
  totalAmount: 0,
  pendingCount: 0,
  completedCount: 0,
  merchantCount: 0
})

// 查询表单
const queryForm = reactive<SettlementQueryDTO>({
  page: 1,
  pageSize: 20,
  orderNumber: '',
  sellerName: '',
  settlementStatus: undefined,
  settlementStartDate: undefined,
  settlementEndDate: undefined
})

// 回款完成表单
const completeForm = reactive<SettlementCompleteDTO>({
  id: 0,
  remark: ''
})

// 监听日期范围变化
watch(dateRange, (newVal) => {
  if (newVal) {
    queryForm.settlementStartDate = newVal[0]
    queryForm.settlementEndDate = newVal[1]
  } else {
    queryForm.settlementStartDate = undefined
    queryForm.settlementEndDate = undefined
  }
})

// 格式化金额
const formatCurrency = (amount: number): string => {
  return `¥${amount.toFixed(2)}`
}

// 格式化日期
const formatDate = (date: string): string => {
  return new Date(date).toLocaleDateString('zh-CN')
}

// 格式化日期时间
const formatDateTime = (dateTime: string): string => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 获取状态类型
const getStatusType = (status: number): string => {
  switch (status) {
    case 0: return 'info'      // 未到期
    case 1: return 'warning'   // 待回款
    case 2: return 'success'   // 已回款
    default: return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: number): string => {
  switch (status) {
    case 0: return '未到期'
    case 1: return '待回款'
    case 2: return '已回款'
    default: return '未知'
  }
}

// 加载概览数据
const loadOverview = async () => {
  try {
    const response = await getAdminSettlementSummary()
    if (response.code === 1 && response.data.list && response.data.list.length > 0) {
      const summary = response.data.list[0]
      overview.value = {
        totalAmount: summary.totalSettlementAmount,
        pendingCount: summary.pendingOrders,
        completedCount: summary.completedOrders,
        merchantCount: response.data.total
      }
    }
  } catch (error) {
    console.error('加载概览数据失败:', error)
  }
}

// 加载表格数据
const loadTableData = async () => {
  tableLoading.value = true
  try {
    const response = await getAdminSettlementOrders(queryForm)
    if (response.code === 1) {
      // 后端返回的是 list 而不是 records
      const settlementList = response.data.list || []

      // 为每个回款记录获取对应商家的收款账户信息
      const enrichedList = await Promise.all(
        settlementList.map(async (settlement: SettlementInfoVO) => {
          try {
            // 获取商家的收款账户信息
            const accountResponse = await getSellerPaymentAccounts(settlement.sellerId, {
              isDefault: 1, // 只获取默认账户
              pageSize: 1
            })

            if (accountResponse.code === 1 && accountResponse.data.list.length > 0) {
              // 添加收款账户信息到回款记录中
              return {
                ...settlement,
                paymentAccount: accountResponse.data.list[0]
              }
            } else {
              // 没有收款账户
              return {
                ...settlement,
                paymentAccount: undefined
              }
            }
          } catch (error) {
            console.error(`获取商家${settlement.sellerId}收款账户失败:`, error)
            return {
              ...settlement,
              paymentAccount: undefined
            }
          }
        })
      )

      tableData.value = enrichedList
      total.value = response.data.total || 0
    } else {
      ElMessage.error(response.msg || '加载数据失败')
    }
  } catch (error) {
    console.error('加载表格数据失败:', error)
    ElMessage.error('加载数据失败，请稍后重试')
  } finally {
    tableLoading.value = false
  }
}

// 事件处理函数
const handleSearch = () => {
  queryForm.page = 1
  loadTableData()
}

const handleReset = () => {
  Object.assign(queryForm, {
    page: 1,
    pageSize: 20,
    orderNumber: '',
    sellerName: '',
    settlementStatus: undefined,
    settlementStartDate: undefined,
    settlementEndDate: undefined
  })
  dateRange.value = null
  loadTableData()
}

const handleSelectionChange = (selection: SettlementInfoVO[]) => {
  selectedRows.value = selection
}

const handleSortChange = ({ prop, order }: any) => {
  console.log('排序变化:', prop, order)
}

const handleSizeChange = (size: number) => {
  queryForm.pageSize = size
  queryForm.page = 1
  loadTableData()
}

const handleCurrentChange = (page: number) => {
  queryForm.page = page
  loadTableData()
}

const handleConfirmSettlement = (row: SettlementInfoVO) => {
  currentRow.value = row
  completeForm.id = row.id
  completeForm.remark = ''
  completeDialogVisible.value = true
}

const confirmComplete = async () => {
  completeLoading.value = true
  try {
    const response = await confirmSettlement(completeForm.id, completeForm.remark)
    if (response.code === 1) {
      ElMessage.success('回款确认成功')
      completeDialogVisible.value = false
      loadTableData()
      loadOverview()
    } else {
      ElMessage.error(response.msg || '操作失败')
    }
  } catch (error) {
    console.error('确认回款失败:', error)
    ElMessage.error('操作失败，请稍后重试')
  } finally {
    completeLoading.value = false
  }
}

const handleBatchComplete = () => {
  ElMessage.info('批量完成功能需要后端支持')
}

const handleUpdateStatus = async () => {
  try {
    const response = await updateSettlementStatus()
    if (response.code === 1) {
      ElMessage.success('回款状态更新成功')
      loadTableData()
      loadOverview()
    } else {
      ElMessage.error(response.msg || '更新失败')
    }
  } catch (error) {
    console.error('更新状态失败:', error)
    ElMessage.error('更新失败，请稍后重试')
  }
}

const handleExport = () => {
  ElMessage.info('导出功能需要后端支持')
}

const handleViewDetail = (row: SettlementInfoVO) => {
  ElMessage.info(`查看订单 ${row.orderNumber} 的详细信息功能开发中...`)
}

const handleRecalculate = (row: SettlementInfoVO) => {
  ElMessage.info(`重新计算订单 ${row.orderNumber} 的回款日期功能需要后端支持`)
}

// 获取账户类型标签类型
const getAccountTypeTagType = (accountType: number): string => {
  switch (accountType) {
    case 1: return 'primary'  // 银行卡
    case 2: return 'success'  // 支付宝
    case 3: return 'warning'  // 微信
    case 4: return 'info'     // 其他
    default: return 'info'
  }
}

// 复制账号功能
const copyAccountNumber = async () => {
  try {
    const accountNumber = currentRow.value.paymentAccount?.fullAccountNumber || currentRow.value.paymentAccount?.accountNumber
    if (accountNumber) {
      await navigator.clipboard.writeText(accountNumber)
      ElMessage.success('账号已复制到剪贴板')
    }
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请手动复制')
  }
}

// 组件挂载时初始化数据
onMounted(() => {
  loadOverview()
  loadTableData()
})
</script>

<style scoped>
.admin-settlement {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.overview-cards {
  margin-bottom: 20px;
}

.overview-card {
  height: 100px;
}

.card-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.card-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
  color: white;
}

.card-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card-icon.pending {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.card-icon.completed {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.card-icon.merchants {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.card-info h3 {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: bold;
  color: #303133;
}

.card-info p {
  margin: 0;
  color: #909399;
  font-size: 12px;
}

.search-card,
.action-card,
.table-card {
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 0;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.amount {
  font-weight: bold;
  color: #E6A23C;
}

.amount.settlement {
  color: #67C23A;
}

.days-info {
  font-size: 12px;
}

.days-info.overdue {
  color: #F56C6C;
}

.days-info.completed {
  color: #67C23A;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 收款账户信息样式 */
.payment-account-info {
  font-size: 12px;
}

.account-type {
  margin-bottom: 4px;
}

.account-details {
  line-height: 1.4;
}

.account-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 2px;
}

.account-number {
  color: #606266;
  margin-bottom: 2px;
}

.bank-name {
  color: #909399;
  font-size: 11px;
}

.no-account {
  color: #c0c4cc;
  font-style: italic;
}

/* 确认回款对话框样式 */
.settlement-confirm-content {
  max-height: 600px;
  overflow-y: auto;
}

.settlement-confirm-content .el-card {
  margin-bottom: 16px;
  border: 1px solid #e4e7ed;
}

.settlement-confirm-content .el-card:last-child {
  margin-bottom: 0;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

.order-details,
.account-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
}

.detail-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-row .label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
  flex-shrink: 0;
}

.detail-row .value {
  color: #303133;
  flex: 1;
}

.detail-row .value.amount {
  font-weight: 600;
  color: #e6a23c;
}

.detail-row .value.amount.settlement {
  color: #67c23a;
}

.detail-row .value.account-number {
  font-family: 'Courier New', monospace;
  background: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.no-account-warning {
  padding: 16px;
}

.payment-confirm-content .el-alert {
  margin-bottom: 16px;
}

.no-action {
  color: #c0c4cc;
  font-style: italic;
}

.dialog-footer {
  text-align: right;
}
</style>
