<template>
  <div class="commission-layout">
    <div class="debug-tools" v-if="showDebugTools">
      <h3>佣金模式调试工具</h3>
      <div class="debug-buttons">
        <el-button type="primary" size="small" @click="goToPage('leader-list')">团长管理</el-button>
        <el-button type="primary" size="small" @click="goToPage('add-leader')">添加团长</el-button>
        <el-button type="primary" size="small" @click="goToPage('commission-settings')">佣金设置</el-button>
        <el-button type="primary" size="small" @click="goToPage('invitation-codes')">邀请码管理</el-button>
        <el-button type="primary" size="small" @click="goToPage('commission-statistics')">佣金统计</el-button>
      </div>
    </div>
    <router-view></router-view>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'

defineOptions({
  name: 'CommissionLayout',
})

// 是否显示调试工具
const showDebugTools = ref(true)
const router = useRouter()

// 跳转到指定页面
const goToPage = (page: string) => {
  console.log('调试工具: 跳转到', page)
  router.push(`/main/commission/${page}`)
}
</script>

<style scoped lang="scss">
.commission-layout {
  width: 100%;
  height: 100%;
  padding: 20px;
  box-sizing: border-box;
  background-color: #f5f7fa;
}

.debug-tools {
  background-color: #fff;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  h3 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 16px;
    color: #409EFF;
  }

  .debug-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }
}
</style>
