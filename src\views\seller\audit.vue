<template>
  <div class="page-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="page-header">
          <h2 class="page-title">商家审核管理</h2>
          <div class="page-actions">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索商家名称/联系人"
              class="search-input"
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-select
              v-model="statusFilter"
              placeholder="审核状态"
              clearable
              @change="handleSearch"
            >
              <el-option label="全部" :value="null" />
              <el-option label="待审核" :value="0" />
              <el-option label="已通过" :value="1" />
              <el-option label="已拒绝" :value="2" />
            </el-select>
            <el-button type="primary" @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <div class="page-content">
        <!-- 添加批量操作按钮 -->
        <div class="batch-actions" v-if="multipleSelection.length > 0">
          <el-divider content-position="left">已选择 {{ multipleSelection.length }} 项</el-divider>
          <el-button-group>
            <el-button type="success" @click="batchApprove" :disabled="!hasPendingSellers">
              <el-icon><Check /></el-icon> 批量审核通过
            </el-button>
            <el-button type="danger" @click="batchReject" :disabled="!hasPendingSellers">
              <el-icon><Close /></el-icon> 批量审核拒绝
            </el-button>
          </el-button-group>
        </div>

        <el-table
          v-loading="loading"
          :data="filteredSellerList"
          style="width: 100%"
          border
          stripe
          highlight-current-row
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column type="index" width="50" align="center" label="#" />
          <el-table-column prop="id" label="ID" width="80" align="center" />
          
          <el-table-column prop="contactPerson" label="联系人" min-width="120" />
          <el-table-column prop="contactPhone" label="联系电话" min-width="130" />
          <el-table-column prop="accountStatus" label="审核状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.accountStatus)">
                {{ getStatusText(row.accountStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="审核操作" width="150" align="center">
            <template #default="{ row }">
              <div v-if="row.accountStatus === 0">
                <el-button-group>
                  <el-button
                    size="small"
                    type="success"
                    @click="handleQuickApprove(row)"
                    :title="'通过商家审核'"
                  >
                    <el-icon><Check /></el-icon>
                  </el-button>
                  <el-button
                    size="small"
                    type="danger"
                    @click="handleReject(row)"
                    :title="'拒绝商家审核'"
                  >
                    <el-icon><Close /></el-icon>
                  </el-button>
                </el-button-group>
              </div>
              <div v-else>
                <el-tooltip
                  :content="row.accountStatus === 1 ? '审核已通过' : '审核已拒绝'"
                  placement="top"
                >
                  <el-switch
                    v-model="row.isActive"
                    :active-value="true"
                    :inactive-value="false"
                    :disabled="true"
                    :active-color="row.accountStatus === 1 ? '#13ce66' : '#ff4949'"
                    :inactive-color="row.accountStatus === 1 ? '#13ce66' : '#ff4949'"
                  ></el-switch>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="注册时间" width="160" align="center">
            <template #default="{ row }">
              {{ formatDate(row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" align="center" fixed="right">
            <template #default="{ row }">
              <el-button-group>
                <el-button size="small" type="primary" @click="viewDetail(row.id)">
                  <el-icon><View /></el-icon>
                  查看
                </el-button>
              </el-button-group>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页组件 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="totalItems"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, View, Check, Close } from '@element-plus/icons-vue'
import request from '@/utils/request'
import { useRouter } from 'vue-router'

const router = useRouter()

// 定义API函数
function getAllSellers() {
  return request({
    url: '/sellers/All',
    method: 'get',
  })
}

function getSellerById(id: number) {
  return request({
    url: `/sellers/${id}`,
    method: 'get',
  })
}

function updateSellerStatus(id: number, status: number, remarks: string) {
  return request({
    url: `/sellers/update`,
    method: 'put',
    data: { id, accountStatus: status, remarks },
  })
}

function deleteSellerById(id: number) {
  return request({
    url: `/sellers/delete/${id}`,
    method: 'delete',
  })
}

// 批量删除卖家
function batchDeleteSellers(ids: number[]) {
  const promises = ids.map((id) => deleteSellerById(id))
  return Promise.all(promises)
}

// 定义卖家接口
interface Seller {
  id: number
  accountName: string
  gender: number
  phone: string
  email: string
  accountStatus: number
  photoUrl?: string
  createTime: string
  shopName: string
  companyName: string
  contactPerson: string
  contactPhone: string
}

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const totalItems = ref(0)

// 表格数据
const sellerList = ref<Seller[]>([])
const loading = ref(true)
const multipleSelection = ref<Seller[]>([]) // 多选数据

// 搜索和筛选
const searchKeyword = ref('')
const statusFilter = ref<number | null>(null)

// 是否有待审核的商家
const hasPendingSellers = computed(() => {
  return multipleSelection.value.some((seller) => seller.accountStatus === 0)
})

// 计算过滤后的商家列表
const filteredSellerList = computed(() => {
  let filtered = [...sellerList.value]

  // 根据关键词过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(
      (seller) =>
        seller.shopName.toLowerCase().includes(keyword) ||
        seller.contactPerson.toLowerCase().includes(keyword) ||
        seller.companyName.toLowerCase().includes(keyword),
    )
  }

  // 根据状态过滤
  if (statusFilter.value !== null) {
    filtered = filtered.filter((seller) => seller.accountStatus === statusFilter.value)
  }

  totalItems.value = filtered.length

  // 分页
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value

  return filtered.slice(start, end)
})

// 获取状态类型对应的标签类型
const getStatusType = (status: number) => {
  switch (status) {
    case 0:
      return 'warning' // 待审核
    case 1:
      return 'success' // 已通过
    case 2:
      return 'danger' // 已拒绝
    default:
      return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: number) => {
  switch (status) {
    case 0:
      return '待审核'
    case 1:
      return '已通过'
    case 2:
      return '已拒绝'
    default:
      return '未知'
  }
}

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return '-'
  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}

// 获取所有商家数据
const fetchSellerList = async () => {
  loading.value = true
  try {
    const response = await getAllSellers()
    console.log('获取到的商家列表数据:', response)

    if (response.code === 1 && response.data) {
      // 处理返回的数据，确保所有必要字段都存在
      sellerList.value = response.data.map((seller: any) => {
        // 确保所有必要字段都有值，防止页面渲染错误
        return {
          id: seller.id,
          accountName: seller.accountName || '未知用户',
          gender: seller.gender || '未知',
          phone: seller.phone || '未提供',
          email: seller.email || '未提供',
          accountStatus: seller.accountStatus ?? 0, // 默认为待审核
          photoUrl: seller.photoUrl || '',
          contactPerson: seller.contactPerson || seller.accountName || '未知',
          contactPhone: seller.contactPhone || seller.phone || '未提供',
          createTime: seller.createTime || '',
          // 为开关控件添加状态值
          isActive: seller.accountStatus === 1,
        }
      })
      totalItems.value = response.data.length
      console.log('处理后的商家列表:', sellerList.value)
    } else {
      ElMessage.error(response.msg || '获取商家列表失败')
    }
  } catch (error) {
    console.error('获取商家列表出错:', error)
    ElMessage.error('获取商家列表失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 查看商家详情
const viewDetail = async (sellerId: number) => {
  router.push(`/main/seller/detail/${sellerId}`)
}

// 审核通过
const handleApprove = (seller: any) => {
  approveForm.sellerId = seller.id
  approveForm.remark = ''
  approveDialogVisible.value = true
}

// 快速审核通过函数
const handleQuickApprove = async (seller: any) => {
  try {
    loading.value = true
    const sellerId = Number(seller.id)
    if (isNaN(sellerId)) {
      throw new Error('无效的商家ID')
    }

    await ElMessageBox.confirm('确定要通过该商家的审核吗？', '审核确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'success',
    })

    await updateSellerStatus(sellerId, 1, '审核通过')
    ElMessage.success('商家审核已通过')
    refreshData()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('审核失败:', error)
      ElMessage.error(error.message || '审核操作失败')
    }
  } finally {
    loading.value = false
  }
}

// 确认通过
const confirmApprove = async () => {
  try {
    loading.value = true
    const response = await updateSellerStatus(approveForm.sellerId, 1, approveForm.remark)
    if (response.code === 1) {
      ElMessage.success('审核通过成功')
      approveDialogVisible.value = false
      refreshData()
      if (detailDialogVisible.value) {
        detailDialogVisible.value = false
      }
    } else {
      ElMessage.error(response.msg || '操作失败')
    }
  } catch (error) {
    console.error('审核通过出错:', error)
    ElMessage.error('操作失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 拒绝审核函数
const handleReject = async (seller: any) => {
  try {
    loading.value = true
    const sellerId = Number(seller.id)
    if (isNaN(sellerId)) {
      throw new Error('无效的商家ID')
    }

    await ElMessageBox.confirm('确定要拒绝该商家的审核申请吗？', '拒绝确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    await deleteSellerById(sellerId)
    ElMessage.success('已拒绝商家审核并删除其记录')
    refreshData()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('拒绝操作失败:', error)
      ElMessage.error(error.message || '拒绝操作失败')
    }
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1 // 重置到第一页
}

// 刷新数据
const refreshData = () => {
  fetchSellerList()
}

// 分页处理
const handleSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}

// 多选处理
const handleSelectionChange = (selection: Seller[]) => {
  multipleSelection.value = selection
}

// 批量通过
const batchApprove = async () => {
  const pendingSellers = multipleSelection.value.filter((seller) => seller.accountStatus === 0)
  if (pendingSellers.length === 0) {
    ElMessage.warning('没有可批量审核的待审核商家')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要批量通过${pendingSellers.length}个商家的审核申请吗？`,
      '批量审核确认',
      {
        confirmButtonText: '确定通过',
        cancelButtonText: '取消',
        type: 'success',
      },
    )

    loading.value = true

    // 依次处理每个商家
    for (const seller of pendingSellers) {
      await updateSellerStatus(seller.id, 1, '批量审核通过')
    }

    ElMessage.success(`已批量通过${pendingSellers.length}个商家的审核`)
    refreshData()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('批量审核失败:', error)
      ElMessage.error(error.message || '批量审核操作失败')
    }
  } finally {
    loading.value = false
  }
}

// 批量拒绝
const batchReject = async () => {
  const pendingSellers = multipleSelection.value.filter((seller) => seller.accountStatus === 0)
  if (pendingSellers.length === 0) {
    ElMessage.warning('没有可批量拒绝的待审核商家')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要批量拒绝${pendingSellers.length}个商家的审核申请吗？`,
      '批量拒绝确认',
      {
        confirmButtonText: '确定拒绝',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    loading.value = true

    // 依次处理每个商家
    for (const seller of pendingSellers) {
      await deleteSellerById(seller.id)
    }

    ElMessage.success(`已批量拒绝${pendingSellers.length}个商家的审核申请`)
    refreshData()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('批量拒绝失败:', error)
      ElMessage.error(error.message || '批量拒绝操作失败')
    }
  } finally {
    loading.value = false
  }
}

// 初始化
onMounted(() => {
  fetchSellerList()
})
</script>

<style scoped lang="scss">
.page-container {
  padding: 16px;

  .page-card {
    :deep(.el-card__header) {
      padding: 16px 20px;
    }

    :deep(.el-card__body) {
      padding: 20px;
    }
  }

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .page-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
    }

    .page-actions {
      display: flex;
      align-items: center;
      gap: 12px;

      .search-input {
        width: 240px;
      }
    }
  }

  .shop-info {
    display: flex;
    align-items: center;
    gap: 8px;

    .shop-avatar {
      flex-shrink: 0;
    }
  }

  .batch-actions {
    margin-bottom: 20px;

    .el-divider {
      margin-top: 0;
      margin-bottom: 16px;
    }
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
