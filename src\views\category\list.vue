<template>
  <div class="category-container" v-translate-when:300="!loading && categoryList.length > 0">
    <div class="header">
      <h2>分类管理</h2>
      <el-button type="primary" @click="openCategoryDialog(null)">
        <el-icon><Plus /></el-icon>新增分类
      </el-button>
    </div>

    <el-card class="category-card">
      <el-table
        v-loading="loading"
        :data="categoryList"
        row-key="id"
        border
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
        <el-table-column prop="name" label="分类名称" min-width="200">
          <template #default="{ row }">
            <span>{{ row.name }}</span>
            <el-tag v-if="row.level === 0" size="small" type="success" class="ml-2">一级</el-tag>
            <el-tag v-if="row.level === 1" size="small" type="warning" class="ml-2">二级</el-tag>
            <el-tag v-if="row.level === 2" size="small" type="info" class="ml-2">三级</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="sort" label="排序" width="100" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default>
            <el-tag type="success">启用</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <el-button
              v-if="row.level < 3"
              type="primary"
              size="small"
              @click="openCategoryDialog(row)"
            >
              添加子分类
            </el-button>
            <el-button type="danger" size="small" @click="handleDeleteCategory(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 新增/编辑分类对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="500px" destroy-on-close>
      <el-form ref="formRef" :model="categoryForm" :rules="rules" label-width="90px" status-icon>
        <el-form-item label="父级分类" v-if="categoryForm.parentId !== null">
          <el-input v-model="parentCategoryName" disabled />
        </el-form-item>
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="categoryForm.name" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="排序" prop="sortOrder">
          <el-input-number v-model="categoryForm.sortOrder" :min="0" :max="1000" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitting"> 确定 </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { getCategoryTree, createCategory, deleteCategory } from '@/api/category'

// 定义分类接口返回的数据类型
interface CategoryItem {
  id: number
  name: string
  parentId: number | null
  level: number
  sort: number
  children?: CategoryItem[]
}

// 创建分类DTO
interface CategoryCreateDTO {
  name: string
  parentId: number | null
  sortOrder: number
}

// 数据列表
const loading = ref(false)
const categoryList = ref<CategoryItem[]>([])

// 对话框相关
const dialogVisible = ref(false)
const dialogTitle = ref('新增分类')
const submitting = ref(false)
const formRef = ref<FormInstance>()

// 表单相关
const categoryForm = reactive({
  name: '',
  parentId: null as null | number,
  sortOrder: 0,
})

// 父分类名称
const parentCategoryName = ref('')
const selectedParentCategory = ref<CategoryItem | null>(null)

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符之间', trigger: 'blur' },
  ],
  sortOrder: [{ required: true, message: '请输入排序值', trigger: 'blur' }],
}

// 打开分类对话框
const openCategoryDialog = (row: CategoryItem | null) => {
  resetForm()

  if (row) {
    // 添加子分类
    categoryForm.parentId = row.id
    selectedParentCategory.value = row
    parentCategoryName.value = row.name
    dialogTitle.value = `添加 ${row.name} 的子分类`
  } else {
    // 添加一级分类
    categoryForm.parentId = null
    selectedParentCategory.value = null
    parentCategoryName.value = ''
    dialogTitle.value = '新增一级分类'
  }

  dialogVisible.value = true
}

// 重置表单
const resetForm = () => {
  categoryForm.name = ''
  categoryForm.parentId = null
  categoryForm.sortOrder = 0
  parentCategoryName.value = ''
  selectedParentCategory.value = null
  formRef.value?.resetFields()
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        submitting.value = true

        // 封装请求数据
        const data: CategoryCreateDTO = {
          name: categoryForm.name,
          parentId: categoryForm.parentId,
          sortOrder: categoryForm.sortOrder,
        }

        const res = await createCategory(data)

        if (res.code === 1) {
          ElMessage.success('分类创建成功')
          dialogVisible.value = false
          // 重新加载分类列表
          await fetchCategoryTree()
        } else {
          ElMessage.error(res.msg || '分类创建失败')
        }
      } catch (error) {
        console.error('提交分类数据失败:', error)
        ElMessage.error('分类创建失败，请稍后重试')
      } finally {
        submitting.value = false
      }
    }
  })
}

// 获取分类树数据
const fetchCategoryTree = async () => {
  try {
    loading.value = true
    const res = await getCategoryTree()

    if (res.code === 1) {
      categoryList.value = res.data || []
    } else {
      ElMessage.error(res.msg || '获取分类列表失败')
    }
  } catch (error) {
    console.error('获取分类列表失败:', error)
    ElMessage.error('获取分类列表失败')

    // 模拟数据，实际项目中应该删除
    categoryList.value = [
      {
        id: 1,
        name: '电子产品',
        parentId: null,
        level: 1,
        sort: 1,
        children: [
          {
            id: 3,
            name: '手机',
            parentId: 1,
            level: 2,
            sort: 1,
            children: [
              {
                id: 8,
                name: '智能手机',
                parentId: 3,
                level: 3,
                sort: 1,
              },
              {
                id: 9,
                name: '功能机',
                parentId: 3,
                level: 3,
                sort: 2,
              },
            ],
          },
          {
            id: 4,
            name: '电脑',
            parentId: 1,
            level: 2,
            sort: 2,
          },
        ],
      },
      {
        id: 2,
        name: '服装',
        parentId: null,
        level: 1,
        sort: 2,
        children: [
          {
            id: 5,
            name: '男装',
            parentId: 2,
            level: 2,
            sort: 1,
          },
          {
            id: 6,
            name: '女装',
            parentId: 2,
            level: 2,
            sort: 2,
          },
          {
            id: 7,
            name: '童装',
            parentId: 2,
            level: 2,
            sort: 3,
          },
        ],
      },
    ]
  } finally {
    loading.value = false
  }
}

// 处理删除分类
const handleDeleteCategory = (row: CategoryItem) => {
  // 检查是否有子分类
  const hasChildren = row.children && row.children.length > 0

  let confirmMessage = `确定要删除分类"${row.name}"吗？`
  if (hasChildren) {
    confirmMessage = `确定要删除分类"${row.name}"及其所有子分类吗？此操作将级联删除所有子分类！`
  }

  ElMessageBox.confirm(confirmMessage, '删除提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        loading.value = true
        const res = await deleteCategory(row.id)

        if (res.code === 1) {
          ElMessage.success('删除成功')
          // 重新加载分类树
          await fetchCategoryTree()
        } else {
          ElMessage.error(res.msg || '删除失败')
        }
      } catch (error) {
        console.error('删除分类失败:', error)
        ElMessage.error('删除分类失败，请稍后重试')
      } finally {
        loading.value = false
      }
    })
    .catch(() => {
      // 用户取消删除，不做任何操作
    })
}

onMounted(async () => {
  await fetchCategoryTree()
})
</script>

<style scoped lang="scss">
.category-container {
  padding: 20px;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h2 {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: #303133;
    }
  }

  .category-card {
    margin-bottom: 20px;
  }

  .ml-2 {
    margin-left: 8px;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
}
</style>
