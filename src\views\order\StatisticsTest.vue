<template>
  <div class="statistics-test">
    <h2>订单统计功能测试</h2>
    
    <!-- 测试图标 -->
    <div class="icon-test">
      <h3>图标测试</h3>
      <el-icon><ShoppingCart /></el-icon>
      <el-icon><Money /></el-icon>
      <el-icon><DataAnalysis /></el-icon>
      <el-icon><TrendCharts /></el-icon>
      <el-icon><Refresh /></el-icon>
    </div>
    
    <!-- 测试API调用 -->
    <div class="api-test">
      <h3>API测试</h3>
      <el-button @click="testBasicStats" :loading="loading">测试基础统计</el-button>
      <el-button @click="testEnhancedStats" :loading="loading">测试增强统计</el-button>
      <el-button @click="testRealtimeStats" :loading="loading">测试实时统计</el-button>
      
      <div v-if="testResult" class="test-result">
        <h4>测试结果：</h4>
        <pre>{{ JSON.stringify(testResult, null, 2) }}</pre>
      </div>
    </div>
    
    <!-- 测试图表 -->
    <div class="chart-test">
      <h3>图表测试</h3>
      <div ref="testChartRef" style="height: 300px; width: 100%;"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  ShoppingCart,
  Money,
  DataAnalysis,
  TrendCharts,
  Refresh
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import {
  getOrderStatisticsData,
  getEnhancedStatistics,
  getRealtimeStatistics
} from '@/api/order'

const loading = ref(false)
const testResult = ref<any>(null)
const testChartRef = ref<HTMLDivElement>()

// 测试基础统计
const testBasicStats = async () => {
  loading.value = true
  try {
    const response = await getOrderStatisticsData()
    testResult.value = response
    ElMessage.success('基础统计API调用成功')
  } catch (error) {
    console.error('基础统计API调用失败:', error)
    ElMessage.error('基础统计API调用失败')
    testResult.value = { error: error.message }
  } finally {
    loading.value = false
  }
}

// 测试增强统计
const testEnhancedStats = async () => {
  loading.value = true
  try {
    const endDate = new Date().toISOString().split('T')[0]
    const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    
    const response = await getEnhancedStatistics(startDate, endDate)
    testResult.value = response
    ElMessage.success('增强统计API调用成功')
  } catch (error) {
    console.error('增强统计API调用失败:', error)
    ElMessage.error('增强统计API调用失败')
    testResult.value = { error: error.message }
  } finally {
    loading.value = false
  }
}

// 测试实时统计
const testRealtimeStats = async () => {
  loading.value = true
  try {
    const response = await getRealtimeStatistics()
    testResult.value = response
    ElMessage.success('实时统计API调用成功')
  } catch (error) {
    console.error('实时统计API调用失败:', error)
    ElMessage.error('实时统计API调用失败')
    testResult.value = { error: error.message }
  } finally {
    loading.value = false
  }
}

// 初始化测试图表
const initTestChart = () => {
  if (!testChartRef.value) return
  
  const chart = echarts.init(testChartRef.value)
  
  const option = {
    title: {
      text: '测试图表'
    },
    tooltip: {},
    xAxis: {
      data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
    },
    yAxis: {},
    series: [{
      name: '销量',
      type: 'bar',
      data: [120, 200, 150, 80, 70, 110, 130]
    }]
  }
  
  chart.setOption(option)
}

onMounted(() => {
  nextTick(() => {
    initTestChart()
  })
})
</script>

<style scoped lang="scss">
.statistics-test {
  padding: 20px;
  
  h2 {
    color: #303133;
    margin-bottom: 20px;
  }
  
  h3 {
    color: #606266;
    margin: 20px 0 10px 0;
  }
  
  .icon-test {
    margin-bottom: 30px;
    
    .el-icon {
      font-size: 24px;
      margin-right: 10px;
      color: #409eff;
    }
  }
  
  .api-test {
    margin-bottom: 30px;
    
    .el-button {
      margin-right: 10px;
      margin-bottom: 10px;
    }
    
    .test-result {
      margin-top: 20px;
      padding: 15px;
      background-color: #f5f7fa;
      border-radius: 4px;
      
      h4 {
        margin: 0 0 10px 0;
        color: #303133;
      }
      
      pre {
        margin: 0;
        font-size: 12px;
        color: #606266;
        white-space: pre-wrap;
        word-break: break-all;
      }
    }
  }
  
  .chart-test {
    margin-bottom: 30px;
  }
}
</style>
