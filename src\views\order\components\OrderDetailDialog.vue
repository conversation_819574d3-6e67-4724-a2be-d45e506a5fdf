<template>
  <el-dialog
    v-model="visible"
    title="订单详情"
    width="1000px"
    :before-close="handleClose"
    class="order-detail-dialog"
  >
    <div v-loading="loading" class="order-detail-content">
      <div v-if="orderDetail" class="order-info">
        <!-- 基本信息 -->
        <div class="info-section">
          <h3>基本信息</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">订单号：</span>
                <span class="value">{{ orderDetail.number }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">买家ID：</span>
                <span class="value">{{ orderDetail.buyerId }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">订单状态：</span>
                <el-tag :type="getStatusType(orderDetail.status)">
                  {{ getStatusText(orderDetail.status) }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">订单金额：</span>
                <span class="value amount">¥{{ formatAmount(orderDetail.amount) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">支付方式：</span>
                <span class="value">{{ getPayMethodText(orderDetail.payMethod) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">订单来源：</span>
                <span class="value">{{ orderDetail.source || '-' }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 时间信息 -->
        <div class="info-section">
          <h3>时间信息</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">下单时间：</span>
                <span class="value">{{ formatDateTime(orderDetail.orderTime) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">支付时间：</span>
                <span class="value">{{ formatDateTime(orderDetail.payTime) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">发货时间：</span>
                <span class="value">{{ formatDateTime(orderDetail.shipTime) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">完成时间：</span>
                <span class="value">{{ formatDateTime(orderDetail.completeTime) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">取消时间：</span>
                <span class="value">{{ formatDateTime(orderDetail.cancelTime) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">支付交易号：</span>
                <span class="value">{{ orderDetail.paymentTransactionId || '-' }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 物流信息 -->
        <div v-if="orderDetail.status >= 5" class="info-section">
          <h3>物流信息</h3>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <span class="label">物流公司：</span>
                <span class="value">{{ orderDetail.logisticsCompany || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <span class="label">快递单号：</span>
                <span class="value">{{ orderDetail.trackingNumber || '-' }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 金额明细 -->
        <div class="info-section">
          <h3>金额明细</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">商品金额：</span>
                <span class="value">¥{{ formatAmount(orderDetail.amount - (orderDetail.shippingFee || 0) + (orderDetail.discountAmount || 0)) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">运费：</span>
                <span class="value">¥{{ formatAmount(orderDetail.shippingFee) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">优惠金额：</span>
                <span class="value discount">-¥{{ formatAmount(orderDetail.discountAmount) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">税费：</span>
                <span class="value">¥{{ formatAmount(orderDetail.taxAmount) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">实付金额：</span>
                <span class="value amount">¥{{ formatAmount(orderDetail.amount) }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 备注信息 -->
        <div v-if="orderDetail.orderRemark || orderDetail.cancelReason || orderDetail.rejectionReason" class="info-section">
          <h3>备注信息</h3>
          <div v-if="orderDetail.orderRemark" class="info-item full-width">
            <span class="label">订单备注：</span>
            <span class="value">{{ orderDetail.orderRemark }}</span>
          </div>
          <div v-if="orderDetail.cancelReason" class="info-item full-width">
            <span class="label">取消原因：</span>
            <span class="value">{{ orderDetail.cancelReason }}</span>
          </div>
          <div v-if="orderDetail.rejectionReason" class="info-item full-width">
            <span class="label">拒绝原因：</span>
            <span class="value">{{ orderDetail.rejectionReason }}</span>
          </div>
        </div>

        <!-- 商品信息 -->
        <div class="info-section">
          <h3>商品信息</h3>
          <el-table :data="orderDetail.orderDetails" stripe>
            <el-table-column label="商品图片" width="80">
              <template #default="{ row }">
                <el-image
                  :src="row.productImage"
                  :preview-src-list="[row.productImage]"
                  fit="cover"
                  style="width: 50px; height: 50px; border-radius: 4px;"
                  lazy
                />
              </template>
            </el-table-column>
            <el-table-column prop="productName" label="商品名称" show-overflow-tooltip />
            <el-table-column prop="productSpec" label="规格" width="120" show-overflow-tooltip />
            <el-table-column prop="unitPrice" label="单价" width="100">
              <template #default="{ row }">
                ¥{{ formatAmount(row.unitPrice) }}
              </template>
            </el-table-column>
            <el-table-column prop="quantity" label="数量" width="80" />
            <el-table-column prop="discount" label="折扣" width="100">
              <template #default="{ row }">
                <span v-if="row.discount > 0" class="discount">-¥{{ formatAmount(row.discount) }}</span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column prop="subtotal" label="小计" width="100">
              <template #default="{ row }">
                <span class="amount">¥{{ formatAmount(row.subtotal) }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 操作日志 -->
        <div class="info-section">
          <h3>操作日志</h3>
          <div v-loading="logsLoading" class="logs-container">
            <el-timeline v-if="orderLogs.length > 0">
              <el-timeline-item
                v-for="log in orderLogs"
                :key="log.id"
                :timestamp="formatDateTime(log.createTime)"
                placement="top"
              >
                <div class="log-item">
                  <div class="log-operation">
                    <span class="operation-name">{{ getOperationText(log.operation) }}</span>
                    <span v-if="log.oldStatus && log.newStatus" class="status-change">
                      {{ getStatusText(log.oldStatus) }} → {{ getStatusText(log.newStatus) }}
                    </span>
                  </div>
                  <div class="log-operator">操作人：{{ log.operatorName }}</div>
                  <div v-if="log.remark" class="log-remark">备注：{{ log.remark }}</div>
                </div>
              </el-timeline-item>
            </el-timeline>
            <el-empty v-else description="暂无操作日志" />
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          v-if="orderDetail && orderDetail.status === 2"
          type="primary"
          @click="handleShip"
        >
          发货
        </el-button>
        <el-button
          v-if="orderDetail && orderDetail.status === 4"
          type="success"
          @click="handleComplete"
        >
          完成订单
        </el-button>
        <el-button
          v-if="orderDetail && [1, 2, 3].includes(orderDetail.status)"
          type="danger"
          @click="handleCancel"
        >
          取消订单
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 发货对话框 -->
  <ShipOrderDialog
    v-model="shipDialogVisible"
    :order-info="orderDetail"
    @success="handleShipSuccess"
  />
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getOrderById, completeOrder, getOrderLogs } from '@/api/order'
import type { OrderVO, OrderLogVO } from '@/types/order'
import ShipOrderDialog from './ShipOrderDialog.vue'

interface Props {
  modelValue: boolean
  orderId: number | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'refresh'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = ref(false)
const loading = ref(false)
const logsLoading = ref(false)
const shipDialogVisible = ref(false)
const orderDetail = ref<OrderVO | null>(null)
const orderLogs = ref<OrderLogVO[]>([])

// 监听显示状态
watch(
  () => props.modelValue,
  (newVal) => {
    visible.value = newVal
    if (newVal && props.orderId) {
      fetchOrderDetail()
    }
  }
)

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 获取订单详情
const fetchOrderDetail = async () => {
  if (!props.orderId) return

  loading.value = true
  try {
    const response = await getOrderById(props.orderId)
    if (response.code === 1) {
      orderDetail.value = response.data
      // 获取订单日志
      fetchOrderLogs()
    } else {
      ElMessage.error(response.msg || '获取订单详情失败')
    }
  } catch (error) {
    console.error('获取订单详情失败:', error)
    ElMessage.error('获取订单详情失败')
  } finally {
    loading.value = false
  }
}

// 获取订单操作日志
const fetchOrderLogs = async () => {
  if (!props.orderId) return

  logsLoading.value = true
  try {
    const response = await getOrderLogs(props.orderId)
    if (response.code === 1) {
      orderLogs.value = response.data || []
    } else {
      console.error('获取订单日志失败:', response.msg)
    }
  } catch (error) {
    console.error('获取订单日志失败:', error)
  } finally {
    logsLoading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  orderDetail.value = null
  orderLogs.value = []
}

// 发货
const handleShip = () => {
  if (!orderDetail.value) return
  shipDialogVisible.value = true
}

// 发货成功回调
const handleShipSuccess = () => {
  emit('refresh')
  fetchOrderDetail()
}

// 完成订单
const handleComplete = async () => {
  if (!orderDetail.value) return
  
  try {
    await ElMessageBox.confirm('确认完成该订单？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await completeOrder(orderDetail.value.id)
    if (response.code === 1) {
      ElMessage.success('订单完成')
      emit('refresh')
      handleClose()
    } else {
      ElMessage.error(response.msg || '操作失败')
    }
  } catch (error) {
    console.error('完成订单失败:', error)
  }
}

// 取消订单
const handleCancel = () => {
  // 这里可以打开取消订单对话框或直接处理
  ElMessage.info('请使用取消订单功能')
}

// 获取状态类型 - 根据新的状态码更新
const getStatusType = (status: number) => {
  const statusMap: Record<number, string> = {
    1: 'warning',   // 待付款
    2: 'info',      // 已付款
    3: 'warning',   // 待审核
    4: 'primary',   // 处理中
    5: 'success',   // 已发货
    6: 'success',   // 已送达
    7: 'success',   // 已完成
    8: 'danger',    // 已取消
    9: 'info',      // 已退款
    10: 'danger'    // 已拒绝
  }
  return statusMap[status] || 'info'
}

// 获取状态文本 - 根据新的状态码更新
const getStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    1: '待付款',
    2: '已付款',
    3: '待审核',
    4: '处理中',
    5: '已发货',
    6: '已送达',
    7: '已完成',
    8: '已取消',
    9: '已退款',
    10: '已拒绝'
  }
  return statusMap[status] || '未知'
}

// 获取支付方式文本 - 根据新API文档更新
const getPayMethodText = (payMethod: number) => {
  const payMethodMap: Record<number, string> = {
    1: '微信支付',
    2: '支付宝',
    3: '银行卡'
  }
  return payMethodMap[payMethod] || '未知'
}

// 获取操作文本
const getOperationText = (operation: string) => {
  const operationMap: Record<string, string> = {
    'create': '创建订单',
    'pay': '完成支付',
    'review': '审核订单',
    'ship': '发货',
    'deliver': '送达',
    'complete': '完成订单',
    'cancel': '取消订单',
    'refund': '退款',
    'reject': '拒绝订单'
  }
  return operationMap[operation] || operation
}

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 格式化金额
const formatAmount = (amount: number) => {
  if (!amount) return '0.00'
  return amount.toFixed(2)
}
</script>

<style scoped lang="scss">
.order-detail-dialog {
  .el-dialog__body {
    max-height: 70vh;
    overflow-y: auto;
  }
}

.order-detail-content {
  .order-info {
    .info-section {
      margin-bottom: 24px;

      h3 {
        margin: 0 0 16px 0;
        color: #303133;
        font-size: 16px;
        font-weight: 600;
        border-bottom: 1px solid #e4e7ed;
        padding-bottom: 8px;
      }

      .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        &.full-width {
          flex-direction: column;
          align-items: flex-start;

          .label {
            margin-bottom: 4px;
          }
        }

        .label {
          color: #606266;
          font-size: 14px;
          min-width: 100px;
          flex-shrink: 0;
        }

        .value {
          color: #303133;
          font-size: 14px;
          word-break: break-all;

          &.amount {
            color: #f56c6c;
            font-weight: 600;
          }

          &.discount {
            color: #67c23a;
            font-weight: 600;
          }
        }
      }

      .logs-container {
        min-height: 200px;

        .log-item {
          .log-operation {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 4px;

            .operation-name {
              font-weight: 600;
              color: #303133;
            }

            .status-change {
              font-size: 12px;
              color: #909399;
              background: #f5f7fa;
              padding: 2px 6px;
              border-radius: 4px;
            }
          }

          .log-operator {
            font-size: 12px;
            color: #909399;
            margin-bottom: 2px;
          }

          .log-remark {
            font-size: 12px;
            color: #606266;
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
            border-left: 3px solid #e4e7ed;
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

// 响应式设计
@media (max-width: 768px) {
  .order-detail-dialog {
    .el-dialog {
      width: 95% !important;
      margin: 5vh auto !important;
    }

    .el-dialog__body {
      max-height: 80vh;
    }
  }

  .order-detail-content {
    .order-info {
      .info-section {
        .info-item {
          flex-direction: column;
          align-items: flex-start;

          .label {
            margin-bottom: 4px;
            min-width: auto;
          }
        }
      }
    }
  }
}
</style>

<script lang="ts">
export default {
  name: 'OrderDetailDialog'
}
</script>
