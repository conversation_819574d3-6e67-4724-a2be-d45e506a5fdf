<template>
  <div class="admin-account-container">
    <div class="page-header">
      <h2>平台管理员账号管理</h2>
      <p>在这里管理平台的管理员账号</p>
    </div>

    <el-card class="tips-card" shadow="hover">
      <div class="tips-container">
        <div class="tips-header">
          <el-icon><InfoFilled /></el-icon>
          <span>功能说明</span>
        </div>
        <div class="tips-content">
          <p>1. 平台高级管理员可以设置低级管理员账号</p>
          <p>2. 管理员账号在后台直接添加设置，无需申请流程</p>
          <p>3. 可以为管理员分配不同的权限</p>
        </div>
      </div>
    </el-card>

    <el-card class="action-card" shadow="hover">
      <div class="action-container">
        <div class="left">
          <el-button type="primary" @click="showAddAdminDialog">
            <el-icon><Plus /></el-icon>添加管理员账号
          </el-button>
        </div>
        <div class="right">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索管理员名称或邮箱"
            clearable
            @clear="handleSearch"
            class="search-input"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
            <template #append>
              <el-button @click="handleSearch">搜索</el-button>
            </template>
          </el-input>
        </div>
      </div>
    </el-card>

    <el-card class="table-card" shadow="hover">
      <div class="table-header">
        <div class="left">
          <h3>管理员账号列表</h3>
          <el-tag type="info">共 {{ totalCount }} 条记录</el-tag>
          <div class="bulkOperations">
            <el-button type="danger" @click="deleteInBulk">批量删除</el-button>
            <el-button type="warning" @click="batchUpdateDialogVisible = true"
              >批量更新状态</el-button
            >
          </div>
        </div>

      </div>

      <el-table
        @selection-change="handleSelectionChange"
        v-loading="loading"
        :data="adminList"
        border
        style="width: 100%"
      >
        <el-table-column type="selection" label="选中" width="40" />
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="accountName" label="管理员名称" min-width="90" />
        <el-table-column prop="email" label="邮箱" min-width="150" />
        <el-table-column prop="phone" label="手机号" min-width="120" />
        <el-table-column prop="createTime" label="创建时间" min-width="150">
          <template #default="scope">
            {{ formatDate(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="    scope.row.accountStatus === 1 ? 'success' : 
                                    scope.row.accountStatus === 2 ? 'warning' : 'danger'">
              {{     scope.row.accountStatus === 1 ? '启用' : 
                      scope.row.accountStatus === 2 ? '锁定' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="400" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="handleEditPermissions(scope.row)"
              :icon="Setting"
            >
              权限设置
            </el-button>
            <el-button type="warning" size="small" @click="handleEditAdmin(scope.row)" :icon="Edit">
              编辑
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDeleteAdmin(scope.row)"
              :icon="Delete"
            >
              删除
            </el-button>
            <el-button
              type="success"
              size="small"
              @click="handleResetPasswordDialog(scope.row)"
              :icon="Refresh"
            >
              更新密码
            </el-button>
            <!-- <el-button type="info" size="small" @click="viewTheLogs(scope.row)" :icon="Document">
              查看日志
            </el-button> -->
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加/编辑管理员对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑管理员账号' : '添加管理员账号'"
      width="500px"
      destroy-on-close
    >
      <el-form
        ref="formRef"
        :model="adminForm"
        :rules="formRules"
        label-width="100px"
        style="max-width: 460px"
      >
        <el-form-item label="管理员名称" prop="accountName">
          <el-input v-model="adminForm.accountName" placeholder="请输入管理员名称" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="adminForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="adminForm.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="!isEdit">
          <el-input
            v-model="adminForm.password"
            type="password"
            placeholder="请设置登录密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group @change="test" v-model="adminForm.status">
              <el-radio value="1">启用</el-radio>
              <el-radio value="0">禁用</el-radio>
              <el-radio value="2">锁定</el-radio>
            </el-radio-group>
        </el-form-item>
        <el-form-item label="新密码" prop="password">
          <el-input v-model="adminForm.password" type="password" placeholder="请输入新密码" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="adminForm.remark"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitAdminForm">确认</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 批量更新管理员状态对话框 -->
    <el-dialog
      v-model="batchUpdateDialogVisible"
      title="批量更新管理员状态"
      width="500"
      destroy-on-close
    >
      <span>请选择要设置的状态:</span>
      <el-radio-group v-model="status">
        <el-radio label="1">启用</el-radio>
        <el-radio label="0">禁用</el-radio>
        <el-radio label="2">锁定</el-radio>
      </el-radio-group>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="batchUpdateDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="updateStatusInBulk"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 权限设置对话框 -->
    <el-dialog
      v-model="permissionsDialogVisible"
      title="设置管理员权限"
      width="600px"
      destroy-on-close
    >
      <div v-loading="permissionsLoading">
        <p class="permission-dialog-tip">
          为管理员 <strong>{{ currentAdmin?.accountName }}</strong> 设置权限
        </p>
        <el-divider />
        <el-checkbox-group v-model="selectedPermissions">
          <div class="permissions-container">
            <div class="permission-section" v-for="(group, index) in permissionGroups" :key="index">
              <h4>{{ group.title }}</h4>
              <div class="permission-list">
                <el-checkbox
                  v-for="permission in group.permissions"
                  :key="permission.permissionCode"
                  :label="permission.permissionCode"
                >
                  {{ permission.description }}
                </el-checkbox>
              </div>
            </div>
          </div>
        </el-checkbox-group>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="permissionsDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="savePermissions">保存权限设置</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 更新管理员密码对话框 -->
    <el-dialog
      v-model="passwordDialogVisible"
      :before-close="handelBeforeClose"
      title="更新管理员密码"
      destroy-on-close
    >
      <el-form
        ref="formRef"
        :model="adminForm"
        :rules="formRules"
        label-width="100px"
        style="max-width: 460px"
      >
        <el-form-item label="新密码" prop="password">
          <el-input v-model="adminForm.password" type="password" placeholder="请输入新密码" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handelBeforeClose">取消</el-button>
          <el-button type="primary" @click="handleResetPassword">确认</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 管理员日志对话框 -->
    <el-dialog v-model="adminLogDialogVisible" title="管理员日志" width="1200">
      <el-table :data="adminLogs" style="width: 100%">
        <el-table-column prop="id" label="id" width="60"></el-table-column>
        <el-table-column prop="adminId" label="管理员id" width="90"></el-table-column>
        <el-table-column prop="adminName" label="管理员名称" width="120"></el-table-column>
        <el-table-column prop="actionType" label="操作类型" width="120"></el-table-column>
        <el-table-column prop="actionDesc" label="操作描述" width="180"></el-table-column>
        <el-table-column prop="ipAddress" label="id地址" width="120"></el-table-column>
        <el-table-column prop="userAgent" label="用户代理" width="300"></el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="190"></el-table-column>
      </el-table>
      <div style="margin-top: 10px; margin-right: 10px">
        <el-pagination
          v-model:current-page="currentLogPage"
          v-model:page-size="pageLogSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalLogCount"
          @size-change="handleLogSizeChange"
          @current-change="handleLogCurrentChange"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {
  batchDeleteAdminAccounts,
  batchUpdateAdminStatus,
  createAdminAccount,
  deleteAdminAccount,
  getAdminAccountById,
  getAdminOperationLogs,
  getAllAdminAccounts,
  resetAdminPassword,
  updateAdminAccount,
  updateAdminPermissions,
  type AdminAccount,
} from '@/api/adminAccount'
import { getAllPermissionList, type PermissionInfo } from '@/api/permission'
import { formatDate as formatDateUtil } from '@/utils/format'
import {
  Delete,
  Document,
  Edit,
  InfoFilled,
  Plus,
  Refresh,
  Search,
  Setting,
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { computed, onMounted, reactive, ref } from 'vue'

// 数据列表相关
const loading = ref(false)
const adminList = ref<AdminAccount[]>([])
const totalCount = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const searchKeyword = ref('')
const selectedAdminList = ref<AdminAccount[]>([])
const currentSearchStatus = ref('-1')
const totalLogCount = ref(0)
const currentLogPage = ref(1)
const pageLogSize = ref(10)
// 添加/编辑表单相关
const dialogVisible = ref(false)
const isEdit = ref(false)
const formRef = ref<FormInstance>()
const adminForm = reactive<AdminAccount>({
  accountName: '',
  email: '',
  phone: '',
  password: '',
  status: 1,
  permissions: [],
  remark: '',
})
// 指定排序方式
const sortField = ref('id')
const sortFieldOptions = ref([
  { label: 'ID', value: 'id' },
  { label: '用户名', value: 'accountName' },
  { label: '创建时间', value: 'createTime' },
  { label: '最后登录时间', value: 'lastLoginTime' },
])
const sortOrder = ref('desc')
const sortOrderOptions = ref([
  { label: '倒序', value: 'desc' },
  { label: '正序', value: 'asc' },
])
// 搜索时指定搜索的状态
const selectAdminStaus = ref(-1)
// const selectAdminStausOptions = ref([
//   { label: '全部', value: -1 },
//   { label: '正常', value: 1 },
//   { label: '禁用', value: 0 },
//   { label: '锁定', value: 2 },
// ])
interface OperationLog {
  id: number // 日志唯一ID
  adminId: number // 操作管理员ID
  adminName: string // 操作管理员名称
  actionType: string // 操作类型，如 login、update、delete 等
  actionDesc: string // 操作描述，如 "登录系统"、"更新商品信息" 等
  ipAddress: string // IP地址
  userAgent: string // 用户代理
  createTime: string // 创建时间
}
// 管理员日志
const adminLogs = ref<OperationLog[]>([])
// 测试数据
adminLogs.value = [
  {
    id: 1001,
    adminId: 1,
    adminName: '系统管理员',
    actionType: 'login',
    actionDesc: '登录系统',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    createTime: '2023-05-01T08:30:00',
  },
  {
    id: 1002,
    adminId: 1,
    adminName: '系统管理员',
    actionType: 'edit_admin',
    actionDesc: '编辑管理员 "商品管理员"',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    createTime: '2023-05-01T09:15:00',
  },
]
const test=(data:any)=>{
  console.log(data);
  
}
// 状态单选框组绑定值
const status = ref(1)
// 测试数据
// 管理员账号列表测试数据
const adminAccountListTestData = {
  list: [
    {
      id: 1,
      accountName: '系统管理员',
      email: '<EMAIL>',
      phone: '***********',
      status: 1,
      createTime: '2023-01-01T08:00:00',
      updateTime: '2023-01-01T08:00:00',
      lastLoginTime: '2023-05-10T15:30:45',
      loginCount: 128,
      permissions: ['dashboard', 'ProductList', 'ProductAdd'],
      remark: '超级管理员账号',
    },
    {
      id: 2,
      accountName: '商品管理员',
      email: '<EMAIL>',
      phone: '***********',
      status: 1,
      createTime: '2023-06-01T10:30:00',
      updateTime: '2023-06-01T10:30:00',
      lastLoginTime: null,
      loginCount: 0,
      permissions: ['ProductList', 'ProductAdd', 'ProductEdit'],
      remark: '负责商品管理的管理员账号',
    },
    {
      id: 3,
      accountName: '用户管理员',
      email: '<EMAIL>',
      phone: '***********',
      status: 0,
      createTime: '2023-06-02T09:15:00',
      updateTime: '2023-06-02T09:15:00',
      lastLoginTime: null,
      loginCount: 0,
      permissions: ['UserList', 'UserDetail'],
      remark: '负责用户管理的管理员账号',
    },
  ],
  total: 3,
  page: 1,
  pageSize: 10,
  totalPages: 1,
}

// 表单验证规则
const formRules = reactive<FormRules>({
  accountName: [
    { required: true, message: '请输入管理员名称', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' },
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' },
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少为6位', trigger: 'blur' },
  ],
})

// 批量更新状态对话框
const batchUpdateDialogVisible = ref(false)
// 更新管理员密码对话框
const passwordDialogVisible = ref(false)
// 管理员日志对话框
const adminLogDialogVisible = ref(false)
// 权限设置相关
const permissionsDialogVisible = ref(false)
const permissionsLoading = ref(false)
const currentAdmin = ref<AdminAccount | null>(null)
const selectedPermissions = ref<string[]>([])
const allPermissions = ref<PermissionInfo[]>([])

// 权限分组
const permissionGroups = computed(() => {
  const permissionsArray = [
    { module: 'dashboard', description: '控制台访问权限' },
    { module: 'welcome', description: '欢迎页面访问权限' },
    { module: 'Product', description: '商品管理模块访问权限' },
    { module: 'ProductList', description: '商品列表访问权限' },
    { module: 'ProductAdd', description: '添加商品功能权限' },
    { module: 'ProductEdit', description: '编辑商品功能权限' },
    { module: 'ProductAudit', description: '商品审核功能权限' },
    { module: 'UserManagement', description: '用户管理模块访问权限' },
    { module: 'UserList', description: '用户列表访问权限' },
    { module: 'UserDetail', description: '用户详情查看权限' },
    { module: 'Commission', description: '佣金管理模块访问权限' },
    { module: 'LeaderList', description: '团长管理权限' },
    { module: 'AddLeader', description: '添加团长权限' },
    { module: 'CommissionSettings', description: '佣金设置权限' },
    { module: 'InvitationCodes', description: '邀请码管理权限' },
    { module: 'CommissionStatistics', description: '佣金统计权限' },
    { module: 'Permission', description: '权限管理模块访问权限' },
    { module: 'SellerPermission', description: '商家权限管理权限' },
    { module: 'RuleManagement', description: '权限规则管理权限' },
    { module: 'StaffAccount', description: '子账号管理权限' },
    { module: 'ManageStaff', description: '店铺子账号管理权限' },
    { module: 'AdminAccount', description: '平台管理员账号管理权限' },
    { module: 'AdminLog', description: '管理员日志查看权限' },
    { module: 'SystemSettings', description: '系统设置权限' }
  ];

  const groups = [
    {
      title: '基础功能',
      permissions: allPermissions.value.filter(
        (p) => p.permissionCode.includes('dashboard') || p.permissionCode === 'welcome',
      ),
    },
    {
      title: '商品管理',
      permissions: allPermissions.value.filter((p) => p.permissionCode.includes('Product')),
    },
    {
      title: '分类管理',
      permissions: allPermissions.value.filter(
        (p) => p.permissionCode.includes('Category'),
      ),
    },
    {
      title: '商家管理',
      permissions: allPermissions.value.filter(
        (p) =>
          p.permissionCode.includes('Seller') && !p.permissionCode.includes('Permission'),
      ),
    },
    {
      title: '用户管理',
      permissions: allPermissions.value.filter(
        (p) => p.permissionCode.includes('User'),
      ),
    },
    {
      title: '权限管理',
      permissions: allPermissions.value.filter(
        (p) =>
          p.permissionCode.includes('Permission') || p.permissionCode.includes('Rule'),
      ),
    },
    {
      title: '佣金管理',
      permissions: allPermissions.value.filter(
        (p) =>
          !p.permissionCode.includes('Product') &&
          !p.permissionCode.includes('Category') &&
          !p.permissionCode.includes('Seller') &&
          !p.permissionCode.includes('User') &&
          !p.permissionCode.includes('Permission') &&
          !p.permissionCode.includes('Rule') &&
          !p.permissionCode.includes('Commission') &&
          !p.permissionCode.includes('Leader') &&
          !p.permissionCode.includes('Invitation') &&
          !p.permissionCode.includes('dashboard') &&
          p.permissionCode !== 'welcome',
      ),
    },
  ];

  // 过滤掉没有权限的分组
  return groups.filter((group) => group.permissions.length > 0);
});

// 格式化日期
const formatDate = (date: string | undefined) => {
  if (!date) return '--'
  return formatDateUtil(date)
}

// 表格选择触发函数
const handleSelectionChange = (selection: AdminAccount[]) => {
  selectedAdminList.value = selection
  console.log('当前选中了', selectedAdminList.value)
}

// 加载管理员账号列表
const loadAdminList = async (keyword?: string) => {
  loading.value = true
  try {
    // 调用真实API获取管理员账号列表
    const res = await getAllAdminAccounts({
      page: currentPage.value,
      pageSize: pageLogSize.value,
      keyword: searchKeyword.value.trim(),
      status: selectAdminStaus.value !== -1 ? selectAdminStaus.value : null,
      sortField: sortField.value,
      sortOrder: sortOrder.value,
    })
    if (res.code === 1) {
      console.log(keyword);
      
      if(keyword){
        adminList.value = res.data.list.filter((item:any)=>item.accountName.includes(keyword))
      }else{ 
        adminList.value = res.data.list || []
      }
      totalCount.value = res.data.list.length || 0
    } else {
      ElMessage.error(res.msg || '获取管理员账号列表失败')
    }
  } catch (error) {
    // 测试数据
    adminList.value = adminAccountListTestData.list
    console.error('获取管理员账号列表失败:', error)
    ElMessage.error('获取管理员账号列表失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 加载权限列表
const loadPermissionList = async () => {
  permissionsLoading.value = true
  try {
    const res = await getAllPermissionList()
    if (res.code === 1) {
      allPermissions.value = res.data || []
    } else {
      ElMessage.error(res.msg || '获取权限列表失败')
    }
  } catch (error) {
    console.error('获取权限列表失败:', error)
    ElMessage.error('获取权限列表失败，请稍后重试')
  } finally {
    permissionsLoading.value = false
  }
}

// 分页相关
const handleSizeChange = (val: number) => {
  pageSize.value = val
  loadAdminList()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  loadAdminList()
}
const handleLogSizeChange = (val: number) => {
  pageLogSize.value = val
  loadAdminList()
}

const handleLogCurrentChange = (val: number) => {
  currentLogPage.value = val
  loadAdminList()
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  loadAdminList(searchKeyword.value)
}

// 显示添加对话框
const showAddAdminDialog = () => {
  isEdit.value = false
  // 重置表单
  Object.assign(adminForm, {
    accountName: '',
    email: '',
    phone: '',
    password: '',
    status: 1,
  })
  dialogVisible.value = true
}

// 编辑管理员
const handleEditAdmin = (row: AdminAccount) => {
  isEdit.value = true
  Object.assign(adminForm, { ...row })
  // 编辑时不需要修改密码，清空密码字段
  adminForm.password = ''
  dialogVisible.value = true
}

// 删除管理员
const handleDeleteAdmin = (row: AdminAccount) => {
  ElMessageBox.confirm(`确认删除管理员 "${row.accountName}" 吗？`, '删除确认', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        // 调用真实API删除管理员账号
        const res = await deleteAdminAccount(row.id as number)
        if (res.code === 1) {
          ElMessage.success(res.msg || '删除成功')
          // 重新加载列表
          loadAdminList()
        } else {
          ElMessage.error(res.msg || '删除失败')
        }
      } catch (error) {
        console.error('删除管理员账号失败:', error)
        ElMessage.error('删除管理员账号失败，请稍后重试')
      }
    })
    .catch(() => {
      // 用户取消删除，无需操作
    })
}
// 批量删除管理员
const deleteInBulk = async () => {
  try {
    // 从选中的管理员列表中获取id
    const res = await batchDeleteAdminAccounts({
      ids:selectedAdminList.value.map((item) => item.id as number)
    })
    if (res.code === 1) {
      ElMessage.success(res.msg || '批量删除成功')
      loadAdminList()
    } else {
      ElMessage.error(res.msg || '批量删除失败')
    }
  } catch (error) {
    ElMessage.error('批量删除失败，请稍后重试')
  }
}
// 更新管理员密码对话框
const handleResetPasswordDialog = async (data: AdminAccount) => {
  passwordDialogVisible.value = true
  currentAdmin.value = data
}
// 关闭密码修改对话框
const handelBeforeClose = (done: () => void) => {
  adminForm.password = ''
  passwordDialogVisible.value = false
  done()
}
// 更新管理员密码
const handleResetPassword = async () => {
  if (!adminForm.password) {
    ElMessage.error('密码不能为空')
    return
  }
  try {
    // 调用真实API更新管理员密码
    const res = await resetAdminPassword(
      currentAdmin.value?.id as number,
      adminForm.password.toString().trim(),
    )
    if (res.code === 1) {
      ElMessage.success(res.msg || '密码更新成功')
      passwordDialogVisible.value = false
    } else {
      ElMessage.error(res.msg || '密码更新失败')
    }
  } catch (error) {}
  currentAdmin.value = {
    accountName: '',
    email: '',
    phone: '',
    password: '',
    status: 1,
  }
}
// 批量更新管理员状态
const updateStatusInBulk = async () => {
  try {
    // 从选中的管理员列表中获取id
    const res = await batchUpdateAdminStatus(
      {
        ids:selectedAdminList.value.map((item) => item.id as number),
        status:status.value
    }
    )
    if (res.code === 1) {
      ElMessage.success(res.msg || '批量更新成功')
      batchUpdateDialogVisible.value=false
      loadAdminList()
    } else {
      ElMessage.error(res.msg || '批量更新失败')
    }
  } catch (error) {
    ElMessage.error('批量更新失败，请稍后重试')
  }
}
// 提交管理员表单
const submitAdminForm = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (!valid) return

    try {
      let res
      if (isEdit.value) {
        // 更新管理员 - 调用真实API
        console.log('adminform',adminForm);
        res = await updateAdminAccount(adminForm.id as number, adminForm)
      } else {
        // 创建管理员 - 调用真实API
        res = await createAdminAccount(adminForm)
      }

      if (res.code === 1) {
        ElMessage.success(res.msg || (isEdit.value ? '更新成功' : '添加成功'))
        dialogVisible.value = false
        // 重新加载列表
        loadAdminList()
      } else {
        ElMessage.error(res.msg || '保存失败')
      }
    } catch (error) {
      console.error('保存管理员账号失败:', error)
      ElMessage.error('保存管理员账号失败，请稍后重试')
    }
  })
}
// 查看管理员日志
const viewTheLogs = async (data: AdminAccount) => {
  adminLogDialogVisible.value = true
  try {
    const res = await getAdminOperationLogs(data.id as number, {
      page: currentLogPage.value,
      pageSize: pageLogSize.value,
    })
    if (res.code == 1) {
      adminLogs.value = res.data.list || []
      totalLogCount.value = res.data.total || []
    } else {
      ElMessage.error(res.msg || '获取管理员日志失败')
    }
  } catch (error) {
    ElMessage.error('获取管理员日志失败')
  }
}

// 编辑权限
const handleEditPermissions = async (row: AdminAccount) => {
  currentAdmin.value = row
  permissionsLoading.value = true
  permissionsDialogVisible.value = true
  try {
    // 加载当前管理员的权限 - 调用真实API
    const res = await getAdminAccountById(row.id as number)
    if (res.code === 1) {
      selectedPermissions.value = res.data?.permissions || []
    } else {
      ElMessage.error(res.msg || '获取管理员权限失败')
    }
  } catch (error) {
    console.error('获取管理员权限失败:', error)
    ElMessage.error('获取管理员权限失败，请稍后重试')
  } finally {
    permissionsLoading.value = false
  }
}

// 保存权限设置
const savePermissions = async () => {
  if (!currentAdmin.value || !currentAdmin.value.id) return

  try {
    // 调用真实API保存权限
    const res = await updateAdminPermissions(currentAdmin.value.id, {
      permissions: selectedPermissions.value,
    })
    if (res.code === 1) {
      ElMessage.success(res.msg || '权限设置保存成功')
      permissionsDialogVisible.value = false
      // 重新加载列表，获取最新数据
      loadAdminList()
    } else {
      ElMessage.error(res.msg || '权限设置保存失败')
    }
  } catch (error) {
    console.error('保存权限设置失败:', error)
    ElMessage.error('保存权限设置失败，请稍后重试')
  }
}

onMounted(() => {
  loadAdminList()
  loadPermissionList()
})
</script>

<style scoped lang="scss">
.admin-account-container {
  padding: 20px;

  .page-header {
    margin-bottom: 20px;

    h2 {
      margin: 0 0 8px 0;
      font-size: 24px;
      color: #303133;
    }

    p {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }

  .tips-card {
    margin-bottom: 20px;

    .tips-container {
      .tips-header {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        color: var(--el-color-primary);
        font-weight: bold;

        .el-icon {
          margin-right: 8px;
        }
      }

      .tips-content {
        color: #606266;
        line-height: 1.8;

        p {
          margin: 5px 0;
        }
      }
    }
  }

  .action-card {
    margin-bottom: 20px;

    .action-container {
      display: flex;
      justify-content: space-between;
      .status-group {
        margin-right: 2rem;
      }
      .search-input {
        width: 320px;
      }
    }
  }

  .table-card {
    .table-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 15px;
      .left {
        display: flex;
        align-items: center;
        gap: 1rem;
        .bulkOperations {
          margin-right: 20px;
        }

        h3 {
          margin: 0 10px 0 0;
          font-size: 16px;
        }
      }

    }

    .pagination-container {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
}

.permission-dialog-tip {
  margin-bottom: 15px;
  font-size: 14px;
  color: #606266;
}

.permissions-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;

  .permission-section {
    flex: 1 1 calc(50% - 20px);
    min-width: 250px;

    h4 {
      margin: 0 0 10px 0;
      font-size: 16px;
      color: var(--el-color-primary);
      border-bottom: 1px solid #ebeef5;
      padding-bottom: 5px;
    }

    .permission-list {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }
  }
}
</style>