# Sharewharf 商城 - 卖家管理系统

## 项目简介

Sharewharf商城卖家管理系统是一个基于Vue 3 + TypeScript + Element Plus开发的B端管理系统，提供卖家注册、登录、商品管理、订单管理等功能。

## 技术栈

- 前端框架：Vue 3
- 开发语言：TypeScript
- UI组件库：Element Plus
- 路由管理：Vue Router
- 状态管理：Pinia
- HTTP客户端：Axios
- 构建工具：Vite

## 主要功能

- 用户认证：登录、注册、登出
- 卖家注册：分步表单（基本信息、商户信息）
- 商品管理：上传、编辑、删除商品
- 订单管理：查看订单、处理订单、发货等
- 统计数据：销售数据、访问量等统计

## 环境配置

- 前端应用运行在 88 端口
- 后端API服务运行在 8080 端口
- 开发环境、生产环境可通过环境变量文件配置

## 开发说明

### 安装依赖

```bash
npm install
```

### 开发模式启动

```bash
npm run dev
```

应用将在 http://localhost:88 启动

### 打包构建

```bash
npm run build
```

### 类型检查

```bash
npm run type-check
```

### 代码格式化

```bash
npm run format
```

## 项目结构

```
src/
├── api/              # API接口定义
├── assets/           # 静态资源
├── components/       # 公共组件
├── router/           # 路由配置
├── stores/           # Pinia状态管理
├── types/            # TypeScript类型定义
├── utils/            # 工具函数
└── views/            # 页面视图
    ├── login.vue     # 登录页
    ├── declaration.vue # 声明页
    ├── register1.vue # 注册步骤一
    ├── register2.vue # 注册步骤二
    └── seller.vue    # 卖家主页
```

## 测试账号

在开发环境中，可以使用以下测试账号访问系统：

- 账号：admin
- 密码：123456

## 注意事项

1. 确保后端API服务已启动，否则登录和注册功能将无法正常工作。
2. 开发环境下，部分功能有模拟数据，方便前端开发测试。
3. 生产环境部署前需要修改 `.env.production` 中的配置，指向实际的API服务地址。

# 基于用户权限的动态菜单实现

## 功能概述

本功能实现了基于用户权限的动态菜单渲染。当用户登录后，系统会根据后端返回的权限数组（路由name）动态显示对应的菜单项，只有用户拥有对应权限的菜单才会显示。

## 实现方式

### 1. 用户Store权限管理

在 `src/stores/user.ts` 中，我们添加了权限相关功能：

- 添加了 `permissions` 字段用于存储用户权限列表
- 实现了 `setPermissions` 方法，用于设置用户权限
- 实现了 `hasPermission` 方法，用于检查用户是否拥有指定权限

```typescript
// 权限相关功能
const permissions = ref<string[]>(userInfo.value?.permissions || [])

// 设置用户权限
const setPermissions = (perms: string[]) => {
  permissions.value = perms
  if (userInfo.value) {
    userInfo.value.permissions = perms
    localStorage.setItem(USER_INFO_KEY, JSON.stringify(userInfo.value))
  }
}

// 检查用户是否有指定权限
const hasPermission = (permission: string): boolean => {
  return permissions.value.includes(permission)
}
```

### 2. 登录处理中保存权限信息

在 `src/views/login.vue` 中，处理登录响应时保存权限信息：

```typescript
// 设置用户信息
userStore.setUserInfo({
  id: result.data.id,
  accountName: result.data.accountName,
  role: result.data.userRole || '普通用户',
  permissions: result.data.permissions || [], // 保存用户权限
})

// 如果有权限数据，单独设置到store中
if (result.data.permissions) {
  userStore.setPermissions(result.data.permissions)
}
```

### 3. 动态渲染菜单

在 `src/views/main.vue` 中，使用 `v-if` 和 `hasPermission` 方法控制菜单项的显示：

```vue
<el-menu-item v-if="hasPermission('dashboard')" index="dashboard">
  <el-icon><Odometer /></el-icon>
  <template #title>控制台</template>
</el-menu-item>

<el-sub-menu
  v-if="hasPermission('Product') || hasPermission('ProductList') || hasPermission('ProductAdd')"
  index="products"
>
  <template #title>
    <el-icon><Goods /></el-icon>
    <span>商品管理</span>
  </template>
  <el-menu-item v-if="hasPermission('ProductList')" index="product-list">商品列表</el-menu-item>
  <el-menu-item v-if="hasPermission('ProductAdd')" index="product-add">添加商品</el-menu-item>
</el-sub-menu>
```

## 使用方式

当后端返回的登录数据中包含 `permissions` 字段（如 `["dashboard", "ProductList", "UserManagement"]`）时，系统会自动根据这些权限显示对应的菜单项，隐藏用户没有权限的菜单项。

## 注意事项

1. 权限值必须与路由名称一致
2. 对于有子菜单的父级菜单，当任何子菜单可见时，父菜单也应该可见
3. 用户权限数据会被保存在本地存储中，便于刷新页面后保持权限状态
