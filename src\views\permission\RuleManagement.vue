<template>
  <div class="rule-management-container">
    <div class="page-header">
      <h2>权限规则管理</h2>
      <p>查看系统中所有的权限规则</p>
    </div>

    <el-card class="tips-card" shadow="hover">
      <div class="tips-container">
        <div class="tips-header">
          <el-icon><InfoFilled /></el-icon>
          <span>权限说明</span>
        </div>
        <div class="tips-content">
          <p>1. 权限代码: 唯一标识符，用于系统内部标识权限，如 <code>ProductAdd</code></p>
          <p>2. 描述: 权限的具体功能说明</p>
          <p>3. 在"商家权限管理"中可以为具体商家分配权限</p>
        </div>
      </div>
    </el-card>

    <el-card class="action-card" shadow="hover">
      <div class="action-container">
        <div class="left">
          <el-button type="primary" @click="navigateToSellerPermissions">
            <el-icon><User /></el-icon>商家权限管理
          </el-button>
        </div>
      </div>
    </el-card>

    <el-card class="table-card" shadow="hover">
      <div class="table-header">
        <div class="left">
          <h3>权限列表</h3>
          <el-tag type="info">共 {{ permissionList.length }} 条记录</el-tag>
        </div>
      </div>

      <el-table v-loading="loading" :data="permissionList" border style="width: 100%">
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="permissionCode" label="权限代码" width="180" />
        <el-table-column prop="description" label="描述" min-width="300" />
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { User, InfoFilled } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { getAllPermissionList, type PermissionInfo } from '@/api/permission'

// 路由
const router = useRouter()

// 表格数据
const loading = ref(false)
const permissionList = ref<PermissionInfo[]>([])

// 加载权限列表
const loadPermissionList = async () => {
  loading.value = true
  try {
    const res = await getAllPermissionList()
    if (res.code === 1) {
      permissionList.value = res.data || []
    } else {
      ElMessage.error(res.msg || '获取权限列表失败')
      // 模拟数据（仅开发测试使用）
      loadMockData()
    }
  } catch (error) {
    console.error('获取权限列表失败:', error)
    ElMessage.error('获取权限列表失败，请稍后重试')
    // 模拟数据（仅开发测试使用）
    loadMockData()
  } finally {
    loading.value = false
  }
}

// 加载模拟数据
const loadMockData = () => {
  permissionList.value = [
    { permissionCode: 'dashboard', description: '控制台访问权限' },
    { permissionCode: 'Product', description: '商品管理模块访问权限' },
    { permissionCode: 'ProductList', description: '商品列表访问权限' },
    { permissionCode: 'ProductAdd', description: '添加商品功能权限' },
    { permissionCode: 'ProductEdit', description: '编辑商品功能权限' },
    { permissionCode: 'ProductAudit', description: '商品审核功能权限' },
    { permissionCode: 'UserManagement', description: '用户管理模块访问权限' },
    { permissionCode: 'UserList', description: '用户列表访问权限' },
    { permissionCode: 'UserDetail', description: '用户详情查看权限' },
    { permissionCode: 'Commission', description: '佣金管理模块访问权限' },
    { permissionCode: 'Permission', description: '权限管理模块访问权限' },
    { permissionCode: 'SellerPermission', description: '商家权限管理权限' },
    { permissionCode: 'RuleManagement', description: '权限规则管理权限' },
  ]
}

// 导航到商家权限页面
const navigateToSellerPermissions = () => {
  router.push('/main/permission/seller')
}

onMounted(() => {
  loadPermissionList()
})
</script>

<style scoped lang="scss">
.rule-management-container {
  padding: 20px;

  .page-header {
    margin-bottom: 20px;

    h2 {
      margin: 0 0 8px 0;
      font-size: 24px;
      color: #303133;
    }

    p {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }

  .tips-card {
    margin-bottom: 20px;

    .tips-container {
      .tips-header {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        color: var(--el-color-primary);
        font-weight: bold;

        .el-icon {
          margin-right: 8px;
        }
      }

      .tips-content {
        color: #606266;
        line-height: 1.8;

        p {
          margin: 5px 0;
        }

        code {
          background-color: #f2f6fc;
          padding: 2px 4px;
          border-radius: 4px;
          color: var(--el-color-danger);
        }
      }
    }
  }

  .action-card {
    margin-bottom: 20px;

    .action-container {
      display: flex;
      justify-content: space-between;
    }
  }

  .table-card {
    .table-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 15px;

      .left {
        display: flex;
        align-items: center;

        h3 {
          margin: 0 10px 0 0;
          font-size: 16px;
        }
      }
    }
  }
}
</style>
