<template>
  <div class="help-docs">
    <div class="content-header">
      <div class="header-main">
        <el-icon class="header-icon"><Document /></el-icon>
        <div class="header-text">
          <h2>{{ currentSection.title }}</h2>
          <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ path: '/main' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item>帮助中心</el-breadcrumb-item>
            <el-breadcrumb-item>{{ currentSection.title }}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
      </div>
    </div>

    <div class="content-body" v-if="currentSection">
      <div class="doc-wrapper">
        <component :is="currentSection.content" />
      </div>
    </div>

    <div v-else class="empty-content">
      <el-empty description="暂无内容">
        <template #description>
          <div class="empty-text">
            <p class="empty-main">暂无相关文档内容</p>
            <p class="empty-sub">请从左侧菜单选择要查看的内容</p>
          </div>
        </template>
      </el-empty>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, h } from 'vue'
import { useRoute } from 'vue-router'
import {
  Goods,
  List,
  Promotion,
  DataLine,
  ShoppingCart,
  Wallet,
  Box,
  Check,
  Message,
  Phone,
  Document,
} from '@element-plus/icons-vue'

// 创建一个简化的文档组件定义
const createDocComponent = (title: string, content: string) => {
  return () =>
    h('div', { class: 'doc-section' }, [h('h3', title), h('div', { innerHTML: content })])
}

// 系统概述
const overviewContent = `
  <div class="doc-content">
    <p class="doc-intro">欢迎使用我们的商城管理系统。本系统提供了完整的电商运营解决方案，包括商品管理、订单处理、营销活动等功能。通过本系统，您可以轻松管理您的在线商店。</p>
    
    <div class="feature-section">
      <h4>核心功能</h4>
      <div class="feature-grid">
        <div class="feature-item">
          <el-icon><Goods /></el-icon>
          <h5>商品管理</h5>
          <p>支持商品的添加、编辑、下架等操作，轻松管理商品信息</p>
        </div>
        <div class="feature-item">
          <el-icon><List /></el-icon>
          <h5>订单管理</h5>
          <p>提供订单处理、发货、退款等功能，高效处理订单流程</p>
        </div>
        <div class="feature-item">
          <el-icon><Promotion /></el-icon>
          <h5>营销工具</h5>
          <p>支持优惠券、促销活动等营销功能，提升销售业绩</p>
        </div>
        <div class="feature-item">
          <el-icon><DataLine /></el-icon>
          <h5>数据统计</h5>
          <p>提供销售数据、用户分析等统计功能，助力决策</p>
        </div>
      </div>
    </div>

    <div class="quick-start">
      <h4>快速开始</h4>
      <ol>
        <li>完成账号注册和设置</li>
        <li>添加商品并设置分类</li>
        <li>配置物流和支付信息</li>
        <li>开始接收订单</li>
      </ol>
    </div>
  </div>
`
const Overview = createDocComponent('系统概述', overviewContent)

// 账号设置
const accountSetupContent = `
  <div class="doc-content">
    <p class="doc-intro">本指南将帮助您完成账号的初始设置，确保您能够顺利使用系统的所有功能。</p>
    
    <div class="setup-section">
      <h4>基本信息设置</h4>
      <div class="setup-steps">
        <div class="step">
          <div class="step-number">1</div>
          <div class="step-content">
            <h5>个人资料设置</h5>
            <ul>
              <li>完善店铺名称和简介</li>
              <li>上传店铺logo</li>
              <li>设置联系方式</li>
            </ul>
          </div>
        </div>
        
        <div class="step">
          <div class="step-number">2</div>
          <div class="step-content">
            <h5>安全设置</h5>
            <ul>
              <li>修改登录密码</li>
              <li>设置安全问题</li>
              <li>开启双因素认证</li>
            </ul>
          </div>
        </div>
        
        <div class="step">
          <div class="step-number">3</div>
          <div class="step-content">
            <h5>支付设置</h5>
            <ul>
              <li>绑定收款账户</li>
              <li>设置结算周期</li>
              <li>配置交易限额</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <div class="tips-section">
      <h4>注意事项</h4>
      <ul>
        <li>请确保填写的信息真实有效</li>
        <li>定期更新密码以确保账号安全</li>
        <li>及时处理账户异常提醒</li>
      </ul>
    </div>
  </div>
`
const AccountSetup = createDocComponent('账号设置指南', accountSetupContent)

// 添加商品
const addProductContent = `
  <div class="doc-content">
    <p class="doc-intro">本教程将指导您如何正确添加商品信息，确保商品能够顺利上架销售。</p>
    
    <div class="guide-section">
      <h4>操作步骤</h4>
      <div class="step-timeline">
        <div class="timeline-item">
          <div class="timeline-dot"></div>
          <div class="timeline-content">
            <h5>基本信息</h5>
            <ul>
              <li>填写商品名称和简介</li>
              <li>选择商品分类</li>
              <li>设置商品价格</li>
              <li>输入库存数量</li>
            </ul>
          </div>
        </div>
        
        <div class="timeline-item">
          <div class="timeline-dot"></div>
          <div class="timeline-content">
            <h5>图片上传</h5>
            <ul>
              <li>上传商品主图(800x800px)</li>
              <li>添加商品相册(最多5张)</li>
              <li>设置视频(可选)</li>
            </ul>
          </div>
        </div>
        
        <div class="timeline-item">
          <div class="timeline-dot"></div>
          <div class="timeline-content">
            <h5>销售属性</h5>
            <ul>
              <li>设置商品规格</li>
              <li>配置SKU信息</li>
              <li>设置库存预警值</li>
            </ul>
          </div>
        </div>
        
        <div class="timeline-item">
          <div class="timeline-dot"></div>
          <div class="timeline-content">
            <h5>物流信息</h5>
            <ul>
              <li>设置商品重量</li>
              <li>选择运费模板</li>
              <li>设置发货地</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <div class="tips-section warning">
      <h4>注意事项</h4>
      <ul>
        <li>商品主图必须清晰，不能含有水印</li>
        <li>商品描述要详细准确，避免误导</li>
        <li>价格和库存信息要及时更新</li>
        <li>确保商品信息符合平台规范</li>
      </ul>
    </div>
  </div>
`
const AddProduct = createDocComponent('添加商品教程', addProductContent)

// 编辑商品
const editProductContent = `
  <div class="doc-content">
    <p class="doc-intro">本指南将帮助您了解如何编辑和管理已上架的商品信息。</p>
    
    <div class="guide-section">
      <h4>编辑功能</h4>
      <div class="feature-list">
        <div class="feature-card">
          <h5>基本编辑</h5>
          <ul>
            <li>修改商品标题</li>
            <li>更新商品描述</li>
            <li>调整商品价格</li>
            <li>更新库存数量</li>
          </ul>
        </div>
        
        <div class="feature-card">
          <h5>图片管理</h5>
          <ul>
            <li>更换主图</li>
            <li>编辑相册图片</li>
            <li>调整图片顺序</li>
          </ul>
        </div>
        
        <div class="feature-card">
          <h5>规格调整</h5>
          <ul>
            <li>修改规格属性</li>
            <li>调整规格价格</li>
            <li>更新规格库存</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
`
const EditProduct = createDocComponent('编辑商品', editProductContent)

// 商品分类
const productCategoryContent = `
  <div class="doc-content">
    <p class="doc-intro">了解如何管理商品分类，建立清晰的商品层级结构。</p>
    
    <div class="category-section">
      <h4>分类管理</h4>
      <div class="category-tree">
        <div class="tree-node">
          <h5>一级分类</h5>
          <p>创建主要商品类别，如"电子产品"、"服装"等</p>
        </div>
        <div class="tree-node">
          <h5>二级分类</h5>
          <p>细分一级分类，如"手机"、"电脑"等</p>
        </div>
        <div class="tree-node">
          <h5>三级分类</h5>
          <p>最细粒度分类，如"智能手机"、"游戏本"等</p>
        </div>
      </div>
    </div>

    <div class="tips-section">
      <h4>分类建议</h4>
      <ul>
        <li>保持分类层级清晰，便于用户查找</li>
        <li>定期整理和优化分类结构</li>
        <li>适时添加新的分类以适应市场变化</li>
      </ul>
    </div>
  </div>
`
const ProductCategory = createDocComponent('商品分类', productCategoryContent)

// 订单处理
const orderProcessContent = `
  <div class="doc-content">
    <p class="doc-intro">学习如何高效处理订单，提供优质的客户服务。</p>
    
    <div class="process-section">
      <h4>订单状态流程</h4>
      <div class="process-flow">
        <div class="flow-item">
          <div class="flow-icon">
            <el-icon><ShoppingCart /></el-icon>
          </div>
          <div class="flow-text">待付款</div>
        </div>
        <div class="flow-arrow">→</div>
        <div class="flow-item">
          <div class="flow-icon">
            <el-icon><Wallet /></el-icon>
          </div>
          <div class="flow-text">已付款</div>
        </div>
        <div class="flow-arrow">→</div>
        <div class="flow-item">
          <div class="flow-icon">
            <el-icon><Box /></el-icon>
          </div>
          <div class="flow-text">已发货</div>
        </div>
        <div class="flow-arrow">→</div>
        <div class="flow-item">
          <div class="flow-icon">
            <el-icon><Check /></el-icon>
          </div>
          <div class="flow-text">已完成</div>
        </div>
      </div>
    </div>

    <div class="tips-section">
      <h4>处理技巧</h4>
      <ul>
        <li>及时确认新订单</li>
        <li>按订单支付时间顺序处理</li>
        <li>保持与买家的沟通</li>
        <li>做好订单备注和跟踪</li>
      </ul>
    </div>
  </div>
`
const OrderProcess = createDocComponent('订单处理', orderProcessContent)

// 发货管理
const shippingContent = `
  <div class="doc-content">
    <p class="doc-intro">了解发货流程和物流管理的最佳实践。</p>
    
    <div class="shipping-section">
      <h4>发货流程</h4>
      <div class="shipping-steps">
        <div class="step">
          <h5>1. 确认订单</h5>
          <ul>
            <li>核对收货信息</li>
            <li>确认商品库存</li>
            <li>检查支付状态</li>
          </ul>
        </div>
        <div class="step">
          <h5>2. 打包商品</h5>
          <ul>
            <li>准备包装材料</li>
            <li>检查商品完整性</li>
            <li>添加发票等单据</li>
          </ul>
        </div>
        <div class="step">
          <h5>3. 物流发货</h5>
          <ul>
            <li>选择快递公司</li>
            <li>打印快递单</li>
            <li>交付快递</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
`
const Shipping = createDocComponent('发货管理', shippingContent)

// 退货处理
const returnsContent = `
  <div class="doc-content">
    <p class="doc-intro">学习如何处理退货退款请求，提供良好的售后服务。</p>
    
    <div class="returns-section">
      <h4>退货流程</h4>
      <div class="process-cards">
        <div class="process-card">
          <h5>1. 接收退货申请</h5>
          <ul>
            <li>查看退货原因</li>
            <li>核实商品情况</li>
            <li>与买家沟通</li>
          </ul>
        </div>
        <div class="process-card">
          <h5>2. 确认退货</h5>
          <ul>
            <li>提供退货地址</li>
            <li>确认物流信息</li>
            <li>跟踪退货包裹</li>
          </ul>
        </div>
        <div class="process-card">
          <h5>3. 处理退款</h5>
          <ul>
            <li>检查退回商品</li>
            <li>确认退款金额</li>
            <li>执行退款操作</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
`
const Returns = createDocComponent('退货处理', returnsContent)

// 促销活动
const promotionsContent = `
  <div class="doc-content">
    <p class="doc-intro">了解如何创建和管理促销活动，提升销售业绩。</p>
    
    <div class="promotion-section">
      <h4>活动类型</h4>
      <div class="promotion-types">
        <div class="promotion-card">
          <h5>限时折扣</h5>
          <p>在特定时间段内对商品进行折扣销售</p>
          <ul>
            <li>设置折扣力度</li>
            <li>选择活动时间</li>
            <li>指定参与商品</li>
          </ul>
        </div>
        
        <div class="promotion-card">
          <h5>满减优惠</h5>
          <p>购买金额达到条件时享受减免</p>
          <ul>
            <li>设置满减门槛</li>
            <li>配置优惠金额</li>
            <li>设置活动规则</li>
          </ul>
        </div>
        
        <div class="promotion-card">
          <h5>优惠券</h5>
          <p>发放优惠券吸引客户购买</p>
          <ul>
            <li>设置优惠券类型</li>
            <li>配置使用条件</li>
            <li>管理发放数量</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
`
const Promotions = createDocComponent('促销活动', promotionsContent)

// 优惠券
const couponsContent = `
  <div class="doc-content">
    <p class="doc-intro">学习如何创建和管理优惠券，提高用户购买转化率。</p>
    
    <div class="coupon-section">
      <h4>优惠券管理</h4>
      <div class="coupon-features">
        <div class="feature-box">
          <h5>创建优惠券</h5>
          <ul>
            <li>设置优惠金额</li>
            <li>配置使用门槛</li>
            <li>设置有效期</li>
            <li>限制发放数量</li>
          </ul>
        </div>
        
        <div class="feature-box">
          <h5>发放方式</h5>
          <ul>
            <li>主动领取</li>
            <li>系统发放</li>
            <li>活动奖励</li>
            <li>注册赠送</li>
          </ul>
        </div>
        
        <div class="feature-box">
          <h5>使用限制</h5>
          <ul>
            <li>商品类别限制</li>
            <li>用户等级限制</li>
            <li>使用时间限制</li>
            <li>叠加规则设置</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
`
const Coupons = createDocComponent('优惠券管理', couponsContent)

// 常见问题
const faqContent = `
  <div class="doc-content">
    <p class="doc-intro">解答商家在使用过程中常见的问题。</p>
    
    <div class="faq-section">
      <div class="faq-item">
        <h4>Q: 如何修改商品信息？</h4>
        <p>A: 在商品列表中找到需要修改的商品，点击"编辑"按钮，即可修改商品信息。修改完成后点击"保存"即可。</p>
      </div>
      
      <div class="faq-item">
        <h4>Q: 如何处理退款申请？</h4>
        <p>A: 在订单管理中查看退款申请，核实退款原因后，可以选择同意或拒绝退款。同意退款后，系统会自动处理退款流程。</p>
      </div>
      
      <div class="faq-item">
        <h4>Q: 如何设置运费模板？</h4>
        <p>A: 在设置中心找到"运费模板"，点击"新建模板"，设置配送区域和运费规则，保存后即可在发布商品时使用该模板。</p>
      </div>
      
      <div class="faq-item">
        <h4>Q: 如何查看销售数据？</h4>
        <p>A: 在数据统计模块中可以查看各项销售数据，包括销售额、订单量、退款率等指标，支持按时间筛选查看。</p>
      </div>
    </div>
  </div>
`
const FAQ = createDocComponent('常见问题', faqContent)

// 联系客服
const contactContent = `
  <div class="doc-content">
    <p class="doc-intro">如果您在使用过程中遇到问题，可以通过以下方式联系我们的客服团队。</p>
    
    <div class="contact-section">
      <div class="contact-cards">
        <div class="contact-card">
          <el-icon><Message /></el-icon>
          <h5>在线客服</h5>
          <p>工作时间：周一至周日 9:00-22:00</p>
          <el-button type="primary" size="small">立即咨询</el-button>
        </div>
        
        <div class="contact-card">
          <el-icon><Phone /></el-icon>
          <h5>电话支持</h5>
          <p>************</p>
          <p>工作时间：周一至周五 9:00-18:00</p>
        </div>
        
        <div class="contact-card">
          <el-icon><Message /></el-icon>
          <h5>邮件支持</h5>
          <p><EMAIL></p>
          <p>24小时内回复</p>
        </div>
      </div>
    </div>
  </div>
`
const Contact = createDocComponent('联系客服', contactContent)

// 内容映射表
const contentMap = {
  overview: {
    title: '系统概述',
    content: Overview,
  },
  'account-setup': {
    title: '账号设置',
    content: AccountSetup,
  },
  'add-product': {
    title: '添加商品',
    content: AddProduct,
  },
  'edit-product': {
    title: '编辑商品',
    content: EditProduct,
  },
  'product-category': {
    title: '商品分类',
    content: ProductCategory,
  },
  'order-process': {
    title: '订单处理',
    content: OrderProcess,
  },
  shipping: {
    title: '发货管理',
    content: Shipping,
  },
  returns: {
    title: '退货处理',
    content: Returns,
  },
  promotions: {
    title: '促销活动',
    content: Promotions,
  },
  coupons: {
    title: '优惠券管理',
    content: Coupons,
  },
  faq: {
    title: '常见问题',
    content: FAQ,
  },
  contact: {
    title: '联系客服',
    content: Contact,
  },
}

const route = useRoute()
const currentSection = computed(() => {
  const section = route.query.section as string
  return contentMap[section] || contentMap.overview
})

// 监听路由变化
watch(
  () => route.query.section,
  (newSection) => {
    if (newSection && !contentMap[newSection as string]) {
      console.warn(`Section "${newSection}" not found`)
    }
  },
)
</script>

<style scoped lang="scss">
.help-docs {
  padding: 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: calc(100vh - 48px);
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);

  .content-header {
    margin-bottom: 30px;
    padding: 24px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.03);
    backdrop-filter: blur(10px);

    .header-main {
      display: flex;
      align-items: center;
      gap: 16px;

      .header-icon {
        font-size: 32px;
        color: #409eff;
        background: rgba(64, 158, 255, 0.1);
        padding: 12px;
        border-radius: 12px;
      }

      .header-text {
        flex: 1;

        h2 {
          margin: 0 0 8px 0;
          font-size: 24px;
          font-weight: 600;
          background: linear-gradient(120deg, #3a7bd5, #2c5499);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }
  }

  .content-body {
    .doc-wrapper {
      background: rgba(255, 255, 255, 0.9);
      border-radius: 12px;
      padding: 32px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.03);
      backdrop-filter: blur(10px);

      :deep(.doc-section) {
        max-width: 900px;
        margin: 0 auto;

        h3 {
          font-size: 22px;
          font-weight: 600;
          color: #2c3e50;
          margin-bottom: 20px;
          padding-bottom: 12px;
          border-bottom: 2px solid #eef2f7;
        }

        .doc-content {
          color: #475569;
          line-height: 1.8;

          .doc-intro {
            font-size: 16px;
            color: #64748b;
            padding: 20px;
            background: #f8fafc;
            border-radius: 8px;
            border-left: 4px solid #3a7bd5;
            margin-bottom: 30px;
          }

          h4 {
            font-size: 18px;
            color: #334155;
            margin: 30px 0 16px;
            font-weight: 600;
          }

          p {
            margin-bottom: 16px;
            font-size: 15px;
          }

          ul,
          ol {
            padding-left: 24px;
            margin-bottom: 20px;

            li {
              margin-bottom: 12px;
              position: relative;

              &::before {
                content: '';
                position: absolute;
                left: -18px;
                top: 8px;
                width: 6px;
                height: 6px;
                background: #3a7bd5;
                border-radius: 50%;
              }
            }
          }

          .feature-grid,
          .step-timeline,
          .process-flow {
            display: grid;
            gap: 20px;
            margin: 24px 0;

            .feature-item,
            .timeline-item,
            .flow-item {
              background: #fff;
              padding: 20px;
              border-radius: 8px;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
              transition: transform 0.3s ease;

              &:hover {
                transform: translateY(-4px);
              }

              h5 {
                font-size: 16px;
                color: #2c3e50;
                margin: 0 0 12px;
                font-weight: 600;
              }
            }
          }

          .feature-grid {
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          }
        }
      }
    }
  }

  .empty-content {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.03);

    .empty-text {
      text-align: center;

      .empty-main {
        font-size: 16px;
        color: #64748b;
        margin: 0 0 8px;
      }

      .empty-sub {
        font-size: 14px;
        color: #94a3b8;
        margin: 0;
      }
    }
  }
}

// 响应式调整
@media screen and (max-width: 768px) {
  .help-docs {
    padding: 16px;

    .content-header {
      padding: 16px;
      margin-bottom: 20px;

      .header-main {
        .header-icon {
          font-size: 24px;
          padding: 8px;
        }

        .header-text h2 {
          font-size: 20px;
        }
      }
    }

    .content-body .doc-wrapper {
      padding: 20px;

      :deep(.doc-section) {
        .doc-content {
          .feature-grid {
            grid-template-columns: 1fr;
          }
        }
      }
    }
  }
}
</style>
