import type { App as VueApp } from 'vue'
import ImageUpload from './ImageUpload/index.vue'

// 组件列表
const components = [ImageUpload]

// 注册所有组件
export function registerComponents(app: VueApp) {
  components.forEach((component) => {
    app.component(component.name || component.__name || 'Component', component)
  })
}

// 默认导出组件列表
export default {
  install(app: VueApp) {
    registerComponents(app)
  },
}
