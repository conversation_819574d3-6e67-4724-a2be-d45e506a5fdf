<template>
  <div class="Staff-account-container">
    <div class="page-header">
      <h2>店铺子账号管理</h2>
      <p>在这里管理您的店员账号</p>
    </div>

    <el-card class="tips-card" shadow="hover">
      <div class="tips-container">
        <div class="tips-header">
          <el-icon><InfoFilled /></el-icon>
          <span>功能说明</span>
        </div>
        <div class="tips-content">
          <p>1. 商家可以手动设置店员账号</p>
          <p>2. 店员账号在后台直接添加设置，无需申请流程</p>
          <p>3. 可以为店员分配不同的权限</p>
        </div>
      </div>
    </el-card>

    <el-card class="action-card" shadow="hover">
      <div class="action-container">
        <div class="left">
          <el-button type="primary" @click="showAddStaffDialog">
            <el-icon><Plus /></el-icon>添加店员账号
          </el-button>
        </div>
        <div class="right">
            <el-radio-group class="radio-group" v-model="staffForm.accountStatus">
              <el-radio value="1">启用</el-radio>
              <el-radio value="0">禁用</el-radio>
              <el-radio value="2">锁定</el-radio>
            </el-radio-group>
          <el-input
            v-model="searchKeyword"
            placeholder="搜索店员名称或邮箱"
            clearable
            @clear="handleSearch"
            class="search-input"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
            <template #append>
              <el-button @click="handleSearch">搜索</el-button>
            </template>
          </el-input>
        </div>
      </div>
    </el-card>

    <el-card class="table-card" shadow="hover">
      <div class="table-header">
        <div class="left">
          <h3>店员账号列表</h3>
          <el-tag type="info">共 {{ totalCount }} 条记录</el-tag>
          <div class="bulkOperations">
            <el-button type="danger" @click="deleteInBulk">批量删除</el-button>
            <el-button type="warning" @click="batchUpdateDialogVisible = true"
              >批量更新状态</el-button
            >
          </div>
        </div>
      </div>

      <el-table
        @selection-change="handleSelectionChange"
        v-loading="loading"
        :data="StaffList"
        border
        style="width: 100%"
      >
        <el-table-column type="selection" label="选中" width="40" />
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="accountName" label="店员账号名称" min-width="110" />
        <el-table-column prop="email" label="邮箱" min-width="150" />
        <el-table-column prop="phone" label="手机号" min-width="120" />
        <el-table-column prop="createTime" label="创建时间" min-width="150">
          <template #default="scope">
            {{ formatDate(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag
              :type="
                scope.row.accountStatus === 1
                  ? 'success'
                  : scope.row.accountStatus === 2
                    ? 'warning'
                    : 'danger'
              "
            >
              {{
                scope.row.accountStatus === 1
                  ? '启用'
                  : scope.row.accountStatus === 2
                    ? '锁定'
                    : '禁用'
              }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="400" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="handleEditPermissions(scope.row)"
              :icon="Setting"
            >
              权限设置
            </el-button>
            <el-button type="warning" size="small" @click="handleEditStaff(scope.row)" :icon="Edit">
              编辑
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDeleteStaff(scope.row)"
              :icon="Delete"
            >
              删除
            </el-button>
            <el-button
              type="success"
              size="small"
              @click="handleResetPasswordDialog(scope.row)"
              :icon="Refresh"
            >
              更新密码
            </el-button>
            <!-- <el-button type="info" size="small" @click="viewTheLogs(scope.row)" :icon="Document">
              查看日志
            </el-button> -->
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加/编辑店员对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑店员账号' : '添加店员账号'"
      width="500px"
      destroy-on-close
    >
      <el-form
        ref="formRef"
        :model="staffForm"
        :rules="formRules"
        label-width="100px"
        style="max-width: 460px"
      >
        <el-form-item label="店员名称" prop="accountName">
          <el-input v-model="staffForm.accountName" placeholder="请输入店员名称" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="staffForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="staffForm.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="!isEdit">
          <el-input
            v-model="staffForm.password"
            type="password"
            placeholder="请设置登录密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="staffForm.accountStatus">
            <el-radio value="1">启用</el-radio>
            <el-radio value="0">禁用</el-radio>
            <el-radio value="2">锁定</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="operate==='add'" label="权限">
          <el-button @click="addPermission">{{
            operate === 'add' ? '点击添加权限' : '点击编辑权限'
          }}</el-button>
        </el-form-item>
        <el-form-item v-if="operate === 'edit'" label="新密码" prop="password">
          <el-input v-model="staffForm.password" type="password" placeholder="请输入新密码" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="staffForm.remark"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitstaffForm">确认</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 批量更新店员状态对话框 -->
    <el-dialog
      v-model="batchUpdateDialogVisible"
      title="批量更新店员状态"
      width="500"
      destroy-on-close
    >
      <span>请选择要设置的状态:</span>
      <el-radio-group v-model="status">
        <el-radio label="1">启用</el-radio>
        <el-radio label="0">禁用</el-radio>
        <el-radio label="2">锁定</el-radio>
      </el-radio-group>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="batchUpdateDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="updateStatusInBulk"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 权限设置对话框 -->
    <el-dialog
      v-model="permissionsDialogVisible"
      title="设置店员权限"
      width="800px"
      destroy-on-close
    >
      <div v-loading="permissionsLoading">
        <p class="permission-dialog-tip">
          为店员 <strong>{{ currentStaff?.accountName }}</strong> 设置权限
        </p>
        <el-divider />

        <div class="permission-layout">
          <!-- 左侧权限大类 -->
          <div class="permission-categories">
            <div
              v-for="category in permissionCategories"
              :key="category.code"
              class="category-item"
              :class="{ active: activeCategory === category.code }"
              @click="activeCategory = category.code"
            >
              {{ category.title }}
              <el-tag v-if="category.children.length > 0" size="small">
                {{ category.children.length }}
              </el-tag>
            </div>
          </div>

          <!-- 右侧子权限 -->
          <div class="permission-subitems">
            <template v-if="activeCategory">
              <div
                v-for="category in permissionCategories.filter((c) => c.code === activeCategory)"
                :key="category.code"
              >
                <h4>{{ category.title }}</h4>
                <el-checkbox-group v-model="selectedPermissions">
                  <div class="subitem-list">
                    <!-- 主权限项 -->
                    <el-checkbox
                      :label="category.code"
                      @change="handleParentPermissionChange(category)"
                    >
                      {{ getPermissionDescription(category.code) }}
                    </el-checkbox>

                    <!-- 子权限项 -->
                    <el-checkbox
                      v-for="child in category.children"
                      :key="child.code"
                      :label="child.code"
                      class="subitem"
                      @change="handleChildPermissionChange(category.code, child.code)"
                    >
                      {{ child.description }}
                    </el-checkbox>
                  </div>
                </el-checkbox-group>
              </div>
            </template>
            <div v-else class="empty-tip">请从左侧选择权限分类</div>
          </div>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="permissionsDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="savePermissions">保存权限设置</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 更新店员密码对话框 -->
    <el-dialog
      v-model="passwordDialogVisible"
      :before-close="handelBeforeClose"
      title="更新店员密码"
      destroy-on-close
    >
      <el-form
        ref="formRef"
        :model="staffForm"
        :rules="formRules"
        label-width="100px"
        style="max-width: 460px"
      >
        <el-form-item label="新密码" prop="password">
          <el-input v-model="staffForm.password" type="password" placeholder="请输入新密码" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handelBeforeClose">取消</el-button>
          <el-button type="primary" @click="handleResetPassword">确认</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 店员日志对话框 -->
    <el-dialog v-model="StaffLogDialogVisible" title="店员日志" width="1200">
      <el-table :data="StaffLogs" style="width: 100%">
        <el-table-column prop="id" label="id" width="60"></el-table-column>
        <el-table-column prop="StaffId" label="店员id" width="90"></el-table-column>
        <el-table-column prop="StaffName" label="店员名称" width="120"></el-table-column>
        <el-table-column prop="actionType" label="操作类型" width="120"></el-table-column>
        <el-table-column prop="actionDesc" label="操作描述" width="180"></el-table-column>
        <el-table-column prop="ipAddress" label="id地址" width="120"></el-table-column>
        <el-table-column prop="userAgent" label="用户代理" width="300"></el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="190"></el-table-column>
      </el-table>
      <div style="margin-top: 10px; margin-right: 10px">
        <el-pagination
          v-model:current-page="currentLogPage"
          v-model:page-size="pageLogSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalLogCount"
          @size-change="handleLogSizeChange"
          @current-change="handleLogCurrentChange"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {
  batchDeleteStaffAccounts,
  batchUpdateStaffStatus,
  createStaffAccount,
  deleteStaffAccount,
  getAllStaffAccounts,
  getStaffAccountById,
  resetStaffPassword,
  updateStaffAccount,
  updateStaffPermissions,
  type StaffAccount,
} from '@/api/staffAccount'
import { formatDate as formatDateUtil } from '@/utils/format'
import { Delete, Edit, InfoFilled, Plus, Refresh, Search, Setting } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { onMounted, reactive, ref } from 'vue'

const userInfo = JSON.parse(localStorage.getItem('user_info') || '{}')
// 数据列表相关

const operate = ref('')
const loading = ref(false)
const StaffList = ref<StaffAccount[]>([])
const totalCount = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const searchKeyword = ref('')
const selectedStaffList = ref<StaffAccount[]>([])
const totalLogCount = ref(0)
const currentLogPage = ref(1)
const pageLogSize = ref(10)
// 添加/编辑表单相关
const dialogVisible = ref(false)
const isEdit = ref(false)
const formRef = ref<FormInstance>()
const staffForm = reactive<StaffAccount>({
  accountName: '',
  email: '',
  phone: '',
  password: '',
  accountStatus: null,
  permissions: [],
  remark: '',
  createdBy: userInfo.id,
})
// 指定排序方式
// 搜索时指定搜索的状态
// const selectStaffStausOptions = ref([
//   { label: '全部', value: -1 },
//   { label: '正常', value: 1 },
//   { label: '禁用', value: 0 },
//   { label: '锁定', value: 2 },
// ])
interface OperationLog {
  id: number // 日志唯一ID
  StaffId: number // 操作店员ID
  StaffName: string // 操作店员名称
  actionType: string // 操作类型，如 login、update、delete 等
  actionDesc: string // 操作描述，如 "登录系统"、"更新商品信息" 等
  ipAddress: string // IP地址
  userAgent: string // 用户代理
  createTime: string // 创建时间
}
// 店员日志
const StaffLogs = ref<OperationLog[]>([])

// 状态单选框组绑定值
const status = ref(1)

// 表单验证规则
const formRules = reactive<FormRules>({
  accountName: [
    { required: true, message: '请输入店员名称', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' },
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' },
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少为6位', trigger: 'blur' },
  ],
})

// 批量更新状态对话框
const batchUpdateDialogVisible = ref(false)
// 更新店员密码对话框
const passwordDialogVisible = ref(false)
// 店员日志对话框
const StaffLogDialogVisible = ref(false)
// 权限设置相关
const permissionsDialogVisible = ref(false)
const permissionsLoading = ref(false)
const currentStaff = ref<StaffAccount | null>(null)
const selectedPermissions = ref<string[]>([])
const allPermissions = ref([])

// 添加activeCategory响应式变量
const activeCategory = ref('')

// 处理子权限变化
const handleChildPermissionChange = (parentCode: string, childCode: string) => {
  console.log('当前权限列表：', selectedPermissions.value)

  // 如果选中了子权限，确保父权限也被选中
  if (
    selectedPermissions.value.includes(childCode) &&
    !selectedPermissions.value.includes(parentCode)
  ) {
    selectedPermissions.value.push(parentCode)
  }

  // 如果取消了所有子权限，取消父权限
  const parentCategory = permissionCategories.value.find((c) => c.code === parentCode)
  if (parentCategory) {
    const hasSelectedChildren = parentCategory.children.some((child) =>
      selectedPermissions.value.includes(child.code),
    )
    if (!hasSelectedChildren && selectedPermissions.value.includes(parentCode)) {
      selectedPermissions.value = selectedPermissions.value.filter((p) => p !== parentCode)
    }
  }
}

// 处理父权限变化
const handleParentPermissionChange = (category: any) => {
  console.log('当前权限列表：', selectedPermissions.value)

  if (selectedPermissions.value.includes(category.code)) {
    // 选中父权限时，选中所有子权限
    category.children.forEach((child) => {
      if (!selectedPermissions.value.includes(child.code)) {
        selectedPermissions.value.push(child.code)
      }
    })
  } else {
    // 取消父权限时，取消所有子权限
    selectedPermissions.value = selectedPermissions.value.filter(
      (p) => !category.children.some((child) => child.code === p),
    )
  }
}
// 添加获取权限描述的方法
const getPermissionDescription = (code) => {
  const permission = permissionsArray.find((p) => p.module === code)
  return permission ? permission.description : code
}

// 修改handleEditPermissions方法
const handleEditPermissions = async (row: StaffAccount) => {
  
  try {
    currentStaff.value = row
    permissionsLoading.value = true
    permissionsDialogVisible.value = true
    
    console.log('当前选中的店员：',currentStaff.value);
    // 默认选中第一个分类
    if (permissionCategories.value.length > 0) {
      activeCategory.value = permissionCategories.value[0].code
    }

    // 加载当前店员的权限
    const res = await getStaffAccountById(row.id as number, { createdBy: userInfo.id })
    if (res.code === 1) {
      selectedPermissions.value = res.data?.permissions || []
      staffForm.accountStatus=null
      
    } else {
      ElMessage.error(res.msg || '获取店员权限失败')
    }
  } catch (error) {
    console.error('获取店员权限失败:', error)
    ElMessage.error('获取店员权限失败，请稍后重试')
  } finally {
    permissionsLoading.value = false
  }
}
const permissionCategories = ref([
  {
    title: '欢迎页面',
    code: 'welcome',
    children: [],
  },
  {
    title: '控制台',
    code: 'dashboard',
    children: [
      { code: 'hot-products', description: '高销售商品分析' },
      { code: 'merchant-sales', description: '商家营业额分析' },
      { code: 'refund-analysis', description: '退货情况分析' },
    ],
  },
  {
    title: '商品管理',
    code: 'Product',
    children: [
      { code: 'ProductAdd', description: '添加商品' },
      { code: 'ProductEdit', description: '编辑商品' },
      { code: 'ProductList', description: '商品列表' },
    ],
  },
  {
    title: '站内信',
    code: 'Message',
    children: [{ code: 'MessageList', description: '消息管理' }],
  },
  {
    title: '店铺子账号管理',
    code: 'ManageStaff',
    children: [{ code: 'StaffAccount', description: '子账号管理' }],
  },
  {
    title: '帮助文档',
    code: 'help-docs',
    children: [],
  },
])

const permissionsArray = [
  { module: 'dashboard', description: '仪表盘' },
  { module: 'declaration', description: '声明页面' },
  { module: 'help-docs', description: '帮助文档' },
  { module: 'hot-products', description: '高销售商品分析' },
  { module: 'login', description: '登录页面' },
  { module: 'main', description: '主框架' },
  { module: 'ManageStaff', description: '店铺子账号管理' },
  { module: 'merchant-sales', description: '商家营业额分析' },
  { module: 'Message', description: '站内信' },
  { module: 'MessageList', description: '消息管理' },
  { module: 'Product', description: '商品管理' },
  { module: 'ProductAdd', description: '添加商品' },
  { module: 'ProductEdit', description: '编辑商品' },
  { module: 'ProductList', description: '商品列表' },
  { module: 'refund-analysis', description: '退货情况分析' },
  { module: 'register-step1', description: '注册第一步' },
  { module: 'register-step2', description: '注册第二步' },
  { module: 'StaffAccount', description: '子账号管理' },
  { module: 'welcome', description: '欢迎页面' },
]
const addPermission = () => {
  permissionsDialogVisible.value = true
  // operate.value = 'add'
}
// 格式化日期
const formatDate = (date: string | undefined) => {
  if (!date) return '--'
  return formatDateUtil(date)
}

// 表格选择触发函数
const handleSelectionChange = (selection: StaffAccount[]) => {
  selectedStaffList.value = selection
  console.log('当前选中了', selectedStaffList.value)
}

// 加载店员账号列表
const loadStaffList = async (keyword?: string) => {
  loading.value = true
  // try {
  // 调用真实API获取店员账号列表
  const res = await getAllStaffAccounts({
    accountStatus:staffForm.accountStatus!==-1?staffForm.accountStatus:null,
    createdBy: userInfo.id,
  })
  if (res.code === 1) {
    console.log(keyword)

    if (keyword) {
      StaffList.value = res.data.filter((item: any) => item.accountName.includes(keyword))
      staffForm.accountStatus=null
      console.log('当前表单',staffForm);
      
    } else {
            staffForm.accountStatus=null
      console.log('当前表单',staffForm);
      StaffList.value = res.data || []
    }
    totalCount.value = res.data.length || 0
    loading.value = false
  } else {
    ElMessage.error(res.msg || '获取店员账号列表失败')
    loading.value = false
  }
  // } catch (error) {
  //   console.error('获取店员账号列表失败:', error)
  //   ElMessage.error('获取店员账号列表失败，请稍后重试')
  // } finally {
  //   loading.value = false
  // }
}

// 加载权限列表

// 分页相关
const handleSizeChange = (val: number) => {
  pageSize.value = val
  loadStaffList()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  loadStaffList()
}
const handleLogSizeChange = (val: number) => {
  pageLogSize.value = val
  loadStaffList()
}

const handleLogCurrentChange = (val: number) => {
  currentLogPage.value = val
  loadStaffList()
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  loadStaffList(searchKeyword.value)
  
}

// 显示添加对话框
const showAddStaffDialog = () => {
  operate.value = 'add'
  isEdit.value = false
  // 重置表单
  Object.assign(staffForm, {
    accountName: '',
    email: '',
    phone: '',
    password: '',
    status: 1,
  })
  dialogVisible.value = true
}

// 编辑店员
const handleEditStaff = (row: StaffAccount) => {
  console.log(row)
  console.log('当前表单：',staffForm);
  
  operate.value = 'edit'
  isEdit.value = true
  Object.assign(staffForm, { ...row })
  // 编辑时不需要修改密码，清空密码字段
  staffForm.password = ''
  dialogVisible.value = true
}

// 删除店员
const handleDeleteStaff = (row: StaffAccount) => {
  ElMessageBox.confirm(`确认删除店员 "${row.accountName}" 吗？`, '删除确认', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        // 调用真实API删除店员账号
        const res = await deleteStaffAccount(row.id as number, { createdBy: userInfo.id })
        if (res.code === 1) {
          ElMessage.success(res.msg || '删除成功')
          // 重新加载列表
          loadStaffList()
        } else {
          ElMessage.error(res.msg || '删除失败')
        }
      } catch (error) {
        console.error('删除店员账号失败:', error)
        ElMessage.error('删除店员账号失败，请稍后重试')
      }
    })
    .catch(() => {
      // 用户取消删除，无需操作
    })
}
// 批量删除店员
const deleteInBulk = async () => {
  try {
    // 从选中的店员列表中获取id
    const res = await batchDeleteStaffAccounts(
      selectedStaffList.value.map((item) => item.id as number),
      userInfo.id,
    )
    if (res.code === 1) {
      ElMessage.success(res.msg || '批量删除成功')
      loadStaffList()
    } else {
      ElMessage.error(res.msg || '批量删除失败')
    }
  } catch (error) {
    ElMessage.error('批量删除失败，请稍后重试')
  }
}
// 更新店员密码对话框
const handleResetPasswordDialog = async (data: StaffAccount) => {
  passwordDialogVisible.value = true
  currentStaff.value = data
}
// 关闭密码修改对话框
const handelBeforeClose = (done: () => void) => {
  staffForm.password = ''
  passwordDialogVisible.value = false
  done()
}
// 更新店员密码
const handleResetPassword = async () => {
  if (!staffForm.password) {
    ElMessage.error('密码不能为空')
    return
  }
  try {
    // 调用真实API更新店员密码
    const res = await resetStaffPassword(currentStaff.value?.id as number,{
      password:staffForm.password.toString().trim(),
      createdBy:userInfo.id
    })
    if (res.code === 1) {
      ElMessage.success(res.msg || '密码更新成功')
      passwordDialogVisible.value = false
    } else {
      ElMessage.error(res.msg || '密码更新失败')
    }
  } catch (error) {}
  currentStaff.value = {
    accountName: '',
    email: '',
    phone: '',
    password: '',
    accountStatus: null,
  }
}
// 批量更新店员状态
const updateStatusInBulk = async () => {
  try {
    // 从选中的店员列表中获取id
    const res = await batchUpdateStaffStatus(userInfo.id,{
      ids:selectedStaffList.value.map((item) => item.id as number),
      status:Number(status.value),
      
    })
    if (res.code === 1) {
      ElMessage.success(res.msg || '批量更新成功')
      batchUpdateDialogVisible.value = false
      loadStaffList()
    } else {
      ElMessage.error(res.msg || '批量更新失败')
    }
  } catch (error) {
    ElMessage.error('批量更新失败，请稍后重试')
  }
}
// 提交店员表单
const submitstaffForm = async () => {
  console.log(operate.value)

  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (!valid) return
    try {
      let res
      if (operate.value === 'edit') {
        console.log('编辑');
        
        // 更新店员 - 调用真实API
        res = await updateStaffAccount(staffForm.id as number, staffForm)
      } else {
        console.log('添加');
        staffForm.permissions = selectedPermissions.value
        console.log('staffForm', staffForm)
        // 创建店员 - 调用真实API
        res = await createStaffAccount(staffForm)
      }

      if (res.code === 1) {
        ElMessage.success(res.msg || (isEdit.value ? '更新成功' : '添加成功'))
        dialogVisible.value = false

        // 重新加载列表
        staffForm.accountStatus=-1
        selectedPermissions.value = []
        operate.value = ''
        loadStaffList()
      } else {
        ElMessage.error(res.msg || '保存失败')
      }
    } catch (error) {
      console.error('保存店员账号失败:', error)
      ElMessage.error('保存店员账号失败，请稍后重试')
    }
  })
}

// 保存权限设置
const savePermissions = async () => {
  console.log('当前修改的店员：',currentStaff.value);
  
  if ((operate.value = 'add')) {
    permissionsDialogVisible.value = false
  }
  if (!currentStaff.value || !currentStaff.value.id) return

  try {
    // 确保只传递权限代码
    const permissionCodes = selectedPermissions.value
    // 调用真实API保存权限
    const res = await updateStaffPermissions(currentStaff.value.id,userInfo.id,permissionCodes)
    if (res.code === 1) {
      ElMessage.success(res.msg || '权限设置保存成功')
      permissionsDialogVisible.value = false
      // 重新加载列表，获取最新数据
      loadStaffList()
    } else {
      ElMessage.error(res.msg || '权限设置保存失败')
    }
  } catch (error) {
    console.error('保存权限设置失败:', error)
    ElMessage.error('保存权限设置失败，请稍后重试')
  }
}

onMounted(() => {
  loadStaffList()
  allPermissions.value = permissionsArray
  console.log('该商家全部权限allPermissions：', allPermissions.value)
})
</script>

<style scoped lang="scss">
.Staff-account-container {
  padding: 20px;

  .page-header {
    margin-bottom: 20px;

    h2 {
      margin: 0 0 8px 0;
      font-size: 24px;
      color: #303133;
    }

    p {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }

  .tips-card {
    margin-bottom: 20px;

    .tips-container {
      .tips-header {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        color: var(--el-color-primary);
        font-weight: bold;

        .el-icon {
          margin-right: 8px;
        }
      }

      .tips-content {
        color: #606266;
        line-height: 1.8;

        p {
          margin: 5px 0;
        }
      }
    }
  }

  .action-card {
    margin-bottom: 20px;

    .action-container {
    .radio-group {
      margin-right: 20px;
    }
      display: flex;
      justify-content: space-between;
      .status-group {
        margin-right: 2rem;
      }
      .search-input {
        width: 320px;
      }
    }
  }

  .table-card {
    .table-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 15px;
      .left {
        display: flex;
        align-items: center;
        gap: 1rem;
        .bulkOperations {
          margin-right: 20px;
        }

        h3 {
          margin: 0 10px 0 0;
          font-size: 16px;
        }
      }
    }

    .pagination-container {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
}

.permission-dialog-tip {
  margin-bottom: 15px;
  font-size: 14px;
  color: #606266;
}

.permissions-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;

  .permission-section {
    flex: 1 1 calc(50% - 20px);
    min-width: 250px;

    h4 {
      margin: 0 0 10px 0;
      font-size: 16px;
      color: var(--el-color-primary);
      border-bottom: 1px solid #ebeef5;
      padding-bottom: 5px;
    }

    .permission-list {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }
  }
}
.permission-layout {
  display: flex;
  height: 400px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.permission-categories {
  width: 200px;
  border-right: 1px solid #ebeef5;
  overflow-y: auto;

  .category-item {
    padding: 12px 16px;
    cursor: pointer;
    border-bottom: 1px solid #f5f7fa;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &:hover {
      background-color: #f5f7fa;
    }

    &.active {
      background-color: #ecf5ff;
      color: var(--el-color-primary);
    }

    .el-tag {
      margin-left: 8px;
    }
  }
}

.permission-subitems {
  flex: 1;
  padding: 16px;
  overflow-y: auto;

  h4 {
    margin: 0 0 16px 0;
    color: var(--el-text-color-primary);
  }

  .subitem-list {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .subitem {
      margin-left: 24px;
    }
  }
}

.empty-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--el-text-color-secondary);
}
</style>
