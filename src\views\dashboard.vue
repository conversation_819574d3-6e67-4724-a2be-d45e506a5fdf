<template>
  <div class="dashboard-container">
    <!-- 顶部提示栏 -->
    <div class="dashboard-header">
      <div class="header-left">
        <h1 class="dashboard-title">数据控制台</h1>
        <div class="demo-badge">
          <el-tag type="warning" effect="dark" size="large" class="demo-tag">
            <el-icon><Warning /></el-icon>
            示例数据展示
          </el-tag>
        </div>
      </div>
      <div class="header-right">
        <el-alert
          title="注意：页面展示的所有数据均为模拟数据，仅作为界面展示使用"
          type="warning"
          :closable="false"
          show-icon
        />
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="stat-cards">
      <el-card class="stat-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>总订单数</span>
            <el-tag type="success" effect="plain" size="small">本月</el-tag>
          </div>
        </template>
        <div class="card-content">
          <div class="card-value">1,358</div>
          <div class="card-trend">
            <span class="trend-value up">
              <el-icon><ArrowUp /></el-icon>12.5%
            </span>
            <span class="trend-text">较上月</span>
          </div>
        </div>
        <div class="mini-chart">
          <div class="line-chart">
            <div
              class="line-point"
              v-for="height in [30, 45, 35, 50, 40, 60]"
              :key="height"
              :style="{ bottom: height + '%' }"
            ></div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>总销售额</span>
            <el-tag type="success" effect="plain" size="small">本月</el-tag>
          </div>
        </template>
        <div class="card-content">
          <div class="card-value">¥89,560</div>
          <div class="card-trend">
            <span class="trend-value up">
              <el-icon><ArrowUp /></el-icon>8.4%
            </span>
            <span class="trend-text">较上月</span>
          </div>
        </div>
        <div class="mini-chart">
          <div class="line-chart">
            <div
              class="line-point"
              v-for="height in [40, 55, 45, 60, 50, 70]"
              :key="height"
              :style="{ bottom: height + '%' }"
            ></div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>新增用户</span>
            <el-tag type="success" effect="plain" size="small">本月</el-tag>
          </div>
        </template>
        <div class="card-content">
          <div class="card-value">258</div>
          <div class="card-trend">
            <span class="trend-value up">
              <el-icon><ArrowUp /></el-icon>10.7%
            </span>
            <span class="trend-text">较上月</span>
          </div>
        </div>
        <div class="mini-chart">
          <div class="line-chart">
            <div
              class="line-point"
              v-for="height in [35, 50, 40, 55, 45, 65]"
              :key="height"
              :style="{ bottom: height + '%' }"
            ></div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>平均订单金额</span>
            <el-tag type="danger" effect="plain" size="small">本月</el-tag>
          </div>
        </template>
        <div class="card-content">
          <div class="card-value">¥223.5</div>
          <div class="card-trend">
            <span class="trend-value down">
              <el-icon><ArrowDown /></el-icon>3.8%
            </span>
            <span class="trend-text">较上月</span>
          </div>
        </div>
        <div class="mini-chart">
          <div class="line-chart">
            <div
              class="line-point"
              v-for="height in [50, 45, 40, 35, 30, 25]"
              :key="height"
              :style="{ bottom: height + '%' }"
            ></div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 功能导航区 -->
    <div class="feature-nav">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card shadow="hover" class="feature-card" @click="navigateTo('/merchant-sales')">
            <div class="feature-icon">
              <el-icon><Money /></el-icon>
            </div>
            <div class="feature-info">
              <h3>商家营业额分析</h3>
              <p>查看各商家营业情况，销售排行，每日营业额统计</p>
            </div>
            <el-button type="primary" class="feature-btn">查看详情</el-button>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="hover" class="feature-card" @click="navigateTo('/refund-analysis')">
            <div class="feature-icon">
              <el-icon><RefreshLeft /></el-icon>
            </div>
            <div class="feature-info">
              <h3>退货情况统计</h3>
              <p>查看各商家退货率，退货原因分析，退货趋势</p>
            </div>
            <el-button type="primary" class="feature-btn">查看详情</el-button>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="hover" class="feature-card" @click="navigateTo('/hot-products')">
            <div class="feature-icon">
              <el-icon><GoodsFilled /></el-icon>
            </div>
            <div class="feature-info">
              <h3>高销商品分析</h3>
              <p>查看热销商品排行，商品销售趋势，类别销售分布</p>
            </div>
            <el-button type="primary" class="feature-btn">查看详情</el-button>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <div class="chart-section">
      <el-card class="chart-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>销售趋势</span>
            <div class="card-actions">
              <el-radio-group v-model="chartTimeRange" size="small">
                <el-radio-button label="week">本周</el-radio-button>
                <el-radio-button label="month">本月</el-radio-button>
                <el-radio-button label="year">全年</el-radio-button>
              </el-radio-group>
            </div>
          </div>
        </template>
        <div class="chart-container">
          <div class="trend-chart">
            <div class="trend-line">
              <svg class="line-path" viewBox="0 0 300 100" preserveAspectRatio="none">
                <path
                  d="M0,50 C60,40 120,60 180,30 S240,20 300,40"
                  fill="none"
                  stroke="url(#gradient)"
                  stroke-width="2"
                />
              </svg>
              <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" style="stop-color: #3a7bd5" />
                <stop offset="100%" style="stop-color: #64b5f6" />
              </linearGradient>
            </div>
            <div class="trend-points">
              <div
                class="point"
                v-for="(value, index) in trendPoints"
                :key="index"
                :style="{ left: index * 20 + '%', bottom: value + '%' }"
              >
                <div class="point-tooltip">{{ value }}%</div>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 商家营业额TOP10 -->
    <div class="merchant-top">
      <el-card class="merchant-top-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>商家营业额TOP10</span>
            <el-button type="primary" text @click="navigateTo('/merchant-sales')"
              >查看全部</el-button
            >
          </div>
        </template>
        <div class="merchant-top-list">
          <el-table :data="topMerchants" style="width: 100%" size="large">
            <el-table-column type="index" label="排名" width="60" />
            <el-table-column prop="name" label="商家名称" />
            <el-table-column prop="sales" label="本月营业额" sortable>
              <template #default="scope">
                <span class="sales-value">{{ scope.row.sales }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="growth" label="环比增长" width="120">
              <template #default="scope">
                <span :class="scope.row.isUp ? 'trend-up' : 'trend-down'">
                  <el-icon v-if="scope.row.isUp"><ArrowUp /></el-icon>
                  <el-icon v-else><ArrowDown /></el-icon>
                  {{ scope.row.growth }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template #default="scope">
                <el-button type="primary" link @click="viewMerchantDetail(scope.row.id)">
                  详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <el-row :gutter="24">
        <el-col :span="12">
          <el-card class="table-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <span>最近订单</span>
                <el-button type="primary" text>查看全部</el-button>
              </div>
            </template>
            <el-table :data="recentOrders" style="width: 100%" size="large">
              <el-table-column prop="orderNo" label="订单号" width="120" />
              <el-table-column prop="customer" label="客户" />
              <el-table-column prop="amount" label="金额" width="100" />
              <el-table-column prop="status" label="状态" width="100">
                <template #default="scope">
                  <el-tag :type="getStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="table-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <span>热销商品</span>
                <el-button type="primary" text @click="navigateTo('/hot-products')"
                  >查看全部</el-button
                >
              </div>
            </template>
            <el-table :data="topProducts" style="width: 100%" size="large">
              <el-table-column prop="name" label="商品名称" />
              <el-table-column prop="sales" label="销量" width="90" sortable />
              <el-table-column prop="stock" label="库存" width="90">
                <template #default="scope">
                  <el-tag :type="getStockType(scope.row.stock)" size="small">
                    {{ scope.row.stock }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 退货情况概览 -->
    <div class="refund-overview">
      <el-card class="refund-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>退货情况概览</span>
            <el-button type="primary" text @click="navigateTo('/refund-analysis')"
              >查看详情</el-button
            >
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="refund-stat">
              <div class="refund-title">本月退货率</div>
              <div class="refund-value">
                <span class="percentage">4.8%</span>
                <span class="comparison down">
                  <el-icon><ArrowDown /></el-icon>0.5%
                </span>
              </div>
              <el-progress :percentage="4.8" :stroke-width="8" :show-text="false" />
            </div>
          </el-col>
          <el-col :span="8">
            <div class="refund-stat">
              <div class="refund-title">退货金额</div>
              <div class="refund-value">
                <span class="amount">¥4,359</span>
                <span class="comparison down">
                  <el-icon><ArrowDown /></el-icon>12.3%
                </span>
              </div>
              <el-progress
                :percentage="12.3"
                :stroke-width="8"
                :show-text="false"
                status="success"
              />
            </div>
          </el-col>
          <el-col :span="8">
            <div class="refund-stat">
              <div class="refund-title">退货订单数</div>
              <div class="refund-value">
                <span class="count">65</span>
                <span class="comparison down">
                  <el-icon><ArrowDown /></el-icon>8.6%
                </span>
              </div>
              <el-progress
                :percentage="8.6"
                :stroke-width="8"
                :show-text="false"
                status="success"
              />
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  ArrowUp,
  ArrowDown,
  Money,
  RefreshLeft,
  GoodsFilled,
  Warning,
} from '@element-plus/icons-vue'

const router = useRouter()
const chartTimeRange = ref('month')

// 趋势图数据点
const trendPoints = [30, 45, 35, 60, 40, 55]

// 最近订单数据 - 示例数据
const recentOrders = ref([
  {
    orderNo: 'ORD20230001',
    customer: '张三',
    amount: '¥458.00',
    status: '已完成',
  },
  {
    orderNo: 'ORD20230002',
    customer: '李四',
    amount: '¥1,253.50',
    status: '待发货',
  },
  {
    orderNo: 'ORD20230003',
    customer: '王五',
    amount: '¥328.00',
    status: '已发货',
  },
])

// 热销商品数据 - 示例数据
const topProducts = ref([
  { name: '无线蓝牙耳机', sales: 256, stock: 58 },
  { name: '智能手表', sales: 187, stock: 32 },
  { name: '便携式蓝牙音箱', sales: 145, stock: 15 },
])

// 商家TOP10数据 - 示例数据
const topMerchants = ref([
  { id: 1, name: '电子科技专营店', sales: '¥128,560', growth: '15.8%', isUp: true },
  { id: 2, name: '优品家居旗舰店', sales: '¥98,320', growth: '12.3%', isUp: true },
  { id: 3, name: '时尚服饰品牌店', sales: '¥89,750', growth: '9.5%', isUp: true },
  { id: 4, name: '全球美妆专卖', sales: '¥78,450', growth: '8.2%', isUp: true },
  { id: 5, name: '健康食品直营店', sales: '¥65,280', growth: '6.7%', isUp: true },
  { id: 6, name: '办公用品商城', sales: '¥58,920', growth: '5.3%', isUp: true },
  { id: 7, name: '运动户外专营', sales: '¥52,680', growth: '4.8%', isUp: true },
  { id: 8, name: '母婴用品旗舰店', sales: '¥48,350', growth: '3.9%', isUp: true },
  { id: 9, name: '家电数码专卖店', sales: '¥45,780', growth: '3.2%', isUp: true },
  { id: 10, name: '图书文具商城', sales: '¥42,150', growth: '-2.5%', isUp: false },
])

// 获取订单状态的标签类型
const getStatusType = (status: string) => {
  switch (status) {
    case '已完成':
      return 'success'
    case '已发货':
      return 'primary'
    case '待发货':
      return 'warning'
    case '待付款':
      return 'info'
    default:
      return ''
  }
}

// 获取库存状态的标签类型
const getStockType = (stock: number) => {
  if (stock === 0) {
    return 'danger'
  } else if (stock < 20) {
    return 'warning'
  } else {
    return 'success'
  }
}

// 导航到指定路径
const navigateTo = (path: string) => {
  // 将外部路径转换为内部路由路径
  if (path.startsWith('/')) {
    // 首先检查是否是分析页面路径
    if (['/merchant-sales', '/refund-analysis', '/hot-products'].includes(path)) {
      // 将外部路径转换为main子路由路径
      const mainPath = '/main' + path
      router.push(mainPath)
      return
    }
    // 其他路径处理
    router.push(path)
  } else {
    router.push(path)
  }
}

// 查看商家详情
const viewMerchantDetail = (merchantId: number) => {
  router.push(`/merchant-detail/${merchantId}`)
}
</script>

<style scoped lang="scss">
.dashboard-container {
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #eef2f5 100%);
  border-radius: 12px;
  min-height: calc(100vh - 40px);

  .dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    gap: 20px;

    .header-left {
      display: flex;
      align-items: center;
      gap: 16px;

      .dashboard-title {
        font-size: 24px;
        font-weight: 600;
        margin: 0;
        background: linear-gradient(120deg, #3a7bd5, #2c5499);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .demo-tag {
        .el-tag {
          display: flex;
          align-items: center;
          gap: 6px;
          padding: 8px 12px;
          font-weight: 500;

          .el-icon {
            font-size: 16px;
          }
        }
      }
    }

    .header-right {
      flex: 1;
      max-width: 500px;
    }
  }

  .stat-cards {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 24px;
    margin-bottom: 24px;

    .stat-card {
      position: relative;
      transition: all 0.3s ease;
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(10px);

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);

        .mini-chart .line-chart .line-point {
          transform: scale(1.2);
        }
      }

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;
        font-weight: 500;
        color: #5e6d82;
      }

      .card-content {
        position: relative;
        z-index: 1;
      }

      .card-value {
        font-size: 32px;
        font-weight: 600;
        margin-bottom: 8px;
        color: #2c3e50;
      }

      .card-trend {
        display: flex;
        align-items: center;
        font-size: 13px;

        .trend-value {
          display: flex;
          align-items: center;
          margin-right: 8px;
          font-weight: 500;

          &.up {
            color: #67c23a;
          }

          &.down {
            color: #f56c6c;
          }

          .el-icon {
            margin-right: 2px;
          }
        }

        .trend-text {
          color: #909399;
        }
      }

      .mini-chart {
        position: absolute;
        right: 20px;
        bottom: 20px;
        width: 80px;
        height: 40px;

        .line-chart {
          position: relative;
          width: 100%;
          height: 100%;

          .line-point {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #3a7bd5;
            border-radius: 50%;
            transition: all 0.3s ease;

            &::before {
              content: '';
              position: absolute;
              left: 2px;
              bottom: 2px;
              width: calc(100% + 12px);
              height: 1px;
              background: rgba(58, 123, 213, 0.2);
            }

            &:not(:last-child)::after {
              content: '';
              position: absolute;
              right: -14px;
              top: 50%;
              width: 14px;
              height: 1px;
              background: #3a7bd5;
              opacity: 0.3;
            }
          }
        }
      }
    }
  }

  .feature-nav {
    margin-bottom: 24px;

    .feature-card {
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;
      cursor: pointer;

      &:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
      }

      .feature-icon {
        font-size: 24px;
        margin-bottom: 16px;
      }

      .feature-info {
        text-align: center;

        h3 {
          font-size: 18px;
          font-weight: 600;
          margin-bottom: 8px;
        }

        p {
          font-size: 14px;
          color: #909399;
        }
      }

      .feature-btn {
        display: block;
        margin: 0 auto;
        margin-top: 16px;
      }
    }
  }

  .chart-section {
    margin-bottom: 24px;

    .chart-card {
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
      }

      .chart-container {
        height: 300px;
        padding: 20px;

        .trend-chart {
          position: relative;
          width: 100%;
          height: 100%;

          .trend-line {
            position: absolute;
            width: 100%;
            height: 100%;

            .line-path {
              width: 100%;
              height: 100%;
            }
          }

          .trend-points {
            position: absolute;
            width: 100%;
            height: 100%;

            .point {
              position: absolute;
              width: 8px;
              height: 8px;
              background: #3a7bd5;
              border-radius: 50%;
              transform: translate(-50%, 50%);
              cursor: pointer;
              transition: all 0.3s ease;

              &:hover {
                transform: translate(-50%, 50%) scale(1.5);

                .point-tooltip {
                  opacity: 1;
                  transform: translateY(-10px);
                }
              }

              .point-tooltip {
                position: absolute;
                bottom: 100%;
                left: 50%;
                transform: translateX(-50%);
                padding: 4px 8px;
                background: #2c3e50;
                color: white;
                border-radius: 4px;
                font-size: 12px;
                opacity: 0;
                transition: all 0.3s ease;
              }
            }
          }
        }
      }
    }
  }

  .merchant-top {
    margin-bottom: 24px;

    .merchant-top-card {
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
      }

      .merchant-top-list {
        padding: 20px;
      }
    }
  }

  .table-section {
    .table-card {
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
      }

      :deep(.el-table) {
        background: transparent;

        th {
          background-color: rgba(245, 247, 250, 0.8) !important;
          font-weight: 600;
          color: #2c3e50;
        }

        td {
          background-color: transparent;
        }

        tr:hover > td {
          background-color: rgba(64, 158, 255, 0.1) !important;
        }
      }
    }
  }

  .refund-overview {
    margin-bottom: 24px;

    .refund-card {
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
      }

      .refund-stat {
        padding: 20px;

        .refund-title {
          font-size: 18px;
          font-weight: 600;
          margin-bottom: 8px;
        }

        .refund-value {
          display: flex;
          align-items: center;
          margin-bottom: 16px;

          .percentage {
            font-size: 14px;
            margin-right: 8px;
          }

          .comparison {
            font-size: 14px;
            color: #909399;
          }
        }

        .el-progress {
          margin-top: 16px;
        }
      }
    }
  }
}

@media screen and (max-width: 1200px) {
  .dashboard-container {
    .stat-cards {
      grid-template-columns: repeat(2, 1fr);
    }
  }
}

@media screen and (max-width: 768px) {
  .dashboard-container {
    padding: 16px;

    .dashboard-header {
      flex-direction: column;
      align-items: stretch;

      .header-right {
        max-width: none;
      }
    }

    .stat-cards {
      grid-template-columns: 1fr;
    }

    .table-section {
      .el-row {
        margin: 0 !important;
      }

      .el-col {
        padding: 0 !important;
        margin-bottom: 16px;
      }
    }
  }
}
</style>
