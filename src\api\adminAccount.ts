import request from '@/utils/request'

// 管理员账号接口
export interface AdminAccount {
  id?: number
  accountName: string
  email: string
  phone: string
  password?: string
  status?: number
  createTime?: string
  updateTime?: string
  lastLoginTime?: string | null
  permissions?: string[]
  remark?: string
}


// 获取所有管理员账号
export function getAllAdminAccounts(data: any) {
  return request({
    url: '/admin/accounts',
    method: 'get',
    data
  })
}

// 获取单个管理员账号
export function getAdminAccountById(id: number) {
  return request({
    url: `/admin/accounts/${id}`,
    method: 'get',
  })
}

// 创建管理员账号
export function createAdminAccount(data: AdminAccount) {
  return request({
    url: '/admin/accounts',
    method: 'post',
    data,
  })
}

// 更新管理员账号
export function updateAdminAccount(id: number, data: Partial<AdminAccount>) {
  return request({
    url: `/admin/accounts/${id}`,
    method: 'put',
    data,
  })
}

// 删除管理员账号
export function deleteAdminAccount(id: number) {
  return request({
    url: `/admin/accounts/${id}`,
    method: 'delete',
  })
}

// 更新管理员权限
export function updateAdminPermissions(id: number, data: any) {
  return request({
    url: `/admin/accounts/${id}/permissions`,
    method: 'put',
    data,
  })
}

// 批量删除管理员账号
export function batchDeleteAdminAccounts(data:any) {
  return request({
    url: '/admin/accounts/batch-delete',
    method: 'post',
    data,
  })
}

// 批量更新管理员状态
export function batchUpdateAdminStatus(data:any) {
  return request({
    url: '/admin/accounts/batch-update-status',
    method: 'post',
    data,
  })
}

// 重置管理员密码
export function resetAdminPassword(id: number, password: string) {
  return request({
    url: `/admin/accounts/${id}/reset-password`,
    method: 'post',
    params: { password },
  })
}

// 获取管理员操作日志
export function getAdminOperationLogs(id: number, params?: {
  page?: number;
  pageSize?: number;
  startTime?: string;
  endTime?: string;
  actionType?: string;
}) {
  return request({
    url: `/admin/accounts/${id}/logs`,
    method: 'get',
    params,
  })
}