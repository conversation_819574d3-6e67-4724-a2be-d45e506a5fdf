<template>
  <div class="message-container">
    <el-row :gutter="20">
      <!-- 左侧消息分类菜单 -->
      <el-col :span="5">
        <el-menu
          :default-active="activeCategory"
          class="message-menu"
          @select="handleCategoryChange"
        >
          <el-menu-item index="all">
            <el-icon><Message /></el-icon>
            <span>全部消息</span>
            <el-badge :value="totalCount" class="menu-badge" />
          </el-menu-item>
          <el-menu-item index="unread">
            <el-icon><Bell /></el-icon>
            <span>未读消息</span>
            <el-badge 
              :value="unreadCount" 
              :max="99" 
              class="menu-badge" 
              v-if="unreadCount > 0"
            />
          </el-menu-item>
          <el-menu-item index="system">
            <el-icon><Monitor /></el-icon>
            <span>系统公告</span>
            <el-badge :value="categoryCount.system" class="menu-badge" />
          </el-menu-item>
          <el-menu-item index="system">
            <el-icon><Monitor /></el-icon>
            <span>发布公告</span>
            <el-badge :value="categoryCount.system" class="menu-badge" />
          </el-menu-item>
        </el-menu>
      </el-col>

      <!-- 右侧消息列表 -->
      <el-col :span="19">
        <div class="message-content">
          <!-- 操作工具栏 -->
          <div class="message-toolbar">
            <el-button-group>
              <el-button 
                size="small" 
                :disabled="selectedMessages.length === 0"
                @click="batchMarkAsRead"
              >
                <el-icon><Reading /></el-icon>
                标为已读
              </el-button>
              <el-button 
                size="small" 
                :disabled="selectedMessages.length === 0"
                @click="batchDelete"
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </el-button-group>
            
            <el-input
              v-model="searchQuery"
              placeholder="搜索消息内容"
              clearable
              size="small"
              style="width: 200px; margin-left: 10px;"
              @clear="handleSearchClear"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>

          <!-- 消息列表 -->
          <el-table
            :data="filteredMessages"
            style="width: 100%"
            @selection-change="handleSelectionChange"
            empty-text="暂无消息"
          >
            <el-table-column type="selection" width="40" />
            
            <el-table-column width="50">
              <template #default="{ row }">
                <el-icon 
                  v-if="!row.isRead" 
                  color="#F56C6C" 
                  :size="16"
                >
                  <CircleCloseFilled />
                </el-icon>
              </template>
            </el-table-column>
            
            <el-table-column prop="title" label="消息标题" width="180">
              <template #default="{ row }">
                <span :class="{ 'unread-title': !row.isRead }">{{ row.title }}</span>
              </template>
            </el-table-column>
            
            <el-table-column prop="content" label="消息内容" show-overflow-tooltip>
              <template #default="{ row }">
                <div class="message-preview">
                  {{ row.content }}
                </div>
              </template>
            </el-table-column>
            
            <el-table-column prop="createTime" label="时间" width="160">
              <template #default="{ row }">
                {{ formatTime(row.createTime) }}
              </template>
            </el-table-column>
          
            <el-table-column label="操作" width="120">
              <template #default="{ row }">
                <el-button 
                  link 
                  type="primary" 
                  size="small"
                  @click="showDetail(row)"
                >
                  查看
                </el-button>
                <el-button 
                  link 
                  type="danger" 
                  size="small"
                  @click="deleteMessage(row.id)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <!-- 分页控件 -->
          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :total="totalCount"
              layout="total, sizes, prev, pager, next, jumper"
              :page-sizes="[10, 20, 50]"
              background
            />
          </div>
        </div>
      </el-col>
    </el-row>
    
    <!-- 消息详情弹窗 -->
    <el-dialog 
      v-model="detailVisible" 
      :title="currentMessage.title" 
      width="60%"
    >
      <div class="message-detail">
        <div class="detail-header">
          <span class="detail-time">{{ formatTime(currentMessage.createTime) }}</span>
          <el-tag 
            size="small" 
            :type="getMessageTypeTag(currentMessage.type)"
          >
            {{ getMessageTypeLabel(currentMessage.type) }}
          </el-tag>
        </div>
        
        <div class="detail-content">
          {{ currentMessage.content }}
        </div>
        
        <div v-if="currentMessage.attachments?.length" class="detail-attachments">
          <h4>附件：</h4>
          <div class="attachment-list">
            <el-tag
              v-for="(file, index) in currentMessage.attachments"
              :key="index"
              class="attachment-tag"
              @click="downloadAttachment(file)"
            >
              <el-icon><Document /></el-icon>
              {{ file.name }}
            </el-tag>
          </div>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="detailVisible = false">关闭</el-button>
        <el-button 
          type="primary" 
          v-if="!currentMessage.isRead"
          @click="markAsRead(currentMessage.id)"
        >
          标记为已读
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { 
  Message, Bell, Collection, Monitor, Reading, 
  Delete, Search, CircleCloseFilled, Document 
} from '@element-plus/icons-vue'

// 模拟数据
const mockMessages = [
  {
    id: 1,
    type: 'product-audit',
    title: '商品审核通知',
    content: '您的商品"夏季新款连衣裙"需要补充材料：请上传清晰的质检报告',
    isRead: false,
    createTime: new Date('2023-06-15 10:30:00'),
    attachments: [
      { name: '质检报告样本.pdf', url: '/files/sample.pdf' }
    ]
  },
  {
    id: 2,
    type: 'order-notice',
    title: '订单发货提醒',
    content: '订单 #202306141234 已发货，快递单号：SF123456789',
    isRead: true,
    createTime: new Date('2023-06-14 15:20:00')
  },
  {
    id: 3,
    type: 'system',
    title: '系统维护通知',
    content: '系统将于2023-06-16 02:00至04:00进行维护升级',
    isRead: false,
    createTime: new Date('2023-06-14 09:00:00')
  }
]

// 响应式数据
const activeCategory = ref('all')
const searchQuery = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const selectedMessages = ref([])
const detailVisible = ref(false)
const currentMessage = ref({
  id: 0,
  title: '',
  content: '',
  type: '',
  isRead: false,
  createTime: '',
  attachments: []
})

// 计算属性
const unreadCount = computed(() => mockMessages.filter(m => !m.isRead).length)
const totalCount = computed(() => mockMessages.length)
const categoryCount = computed(() => ({
  product: mockMessages.filter(m => m.type === 'product-audit').length,
  order: mockMessages.filter(m => m.type === 'order-notice').length,
  system: mockMessages.filter(m => m.type === 'system').length
}))

const filteredMessages = computed(() => {
  let result = [...mockMessages]
  
  // 分类过滤
  if (activeCategory.value === 'unread') {
    result = result.filter(m => !m.isRead)
  } else if (activeCategory.value === 'product-audit') {
    result = result.filter(m => m.type === 'product-audit')
  } else if (activeCategory.value === 'order-notice') {
    result = result.filter(m => m.type === 'order-notice')
  } else if (activeCategory.value === 'system') {
    result = result.filter(m => m.type === 'system')
  }
  
  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(m => 
      m.title.toLowerCase().includes(query) || 
      m.content.toLowerCase().includes(query)
    )
  }
  
  return result
})

// 方法
function handleCategoryChange(index) {
  activeCategory.value = index
  currentPage.value = 1
}

function handleSelectionChange(selection) {
  selectedMessages.value = selection
}

function handleSearchClear() {
  searchQuery.value = ''
}

function showDetail(message) {
  currentMessage.value = { ...message }
  detailVisible.value = true
}

function markAsRead(id) {
  const message = mockMessages.find(m => m.id === id)
  if (message) message.isRead = true
  detailVisible.value = false
}

function batchMarkAsRead() {
  selectedMessages.value.forEach(m => { m.isRead = true })
  selectedMessages.value = []
}

function deleteMessage(id) {
  const index = mockMessages.findIndex(m => m.id === id)
  if (index !== -1) mockMessages.splice(index, 1)
}

function batchDelete() {
  selectedMessages.value.forEach(m => {
    const index = mockMessages.findIndex(msg => msg.id === m.id)
    if (index !== -1) mockMessages.splice(index, 1)
  })
  selectedMessages.value = []
}

function downloadAttachment(file) {
  console.log('下载附件:', file.url)
  // 实际项目中这里调用下载API
}

function formatTime(date) {
  return new Date(date).toLocaleString()
}

function getMessageTypeTag(type) {
  const map = {
    'product-audit': 'warning',
    'order-notice': 'success',
    'system': 'info'
  }
  return map[type] || ''
}

function getMessageTypeLabel(type) {
  const map = {
    'product-audit': '商品审核',
    'order-notice': '订单通知',
    'system': '系统消息'
  }
  return map[type] || type
}
</script>

<style scoped>
.message-container {
  padding: 20px;
  height: calc(100vh - 60px);
  background-color: #fff;
  box-sizing: border-box;
}

.message-menu {
  height: 100%;
  border-right: none;
}

.menu-badge {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
}

.message-content {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.message-toolbar {
  margin-bottom: 15px;
  display: flex;
  justify-content: space-between;
}

.message-preview {
  max-width: 300px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.unread-title {
  font-weight: 600;
  color: #333;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.message-detail {
  padding: 0 10px;
}

.detail-header {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-content {
  line-height: 1.6;
  margin-bottom: 20px;
}

.detail-attachments {
  margin-top: 30px;
}

.attachment-list {
  margin-top: 10px;
}

.attachment-tag {
  margin-right: 10px;
  cursor: pointer;
}

.attachment-tag:hover {
  background-color: #f0f7ff;
}
</style>