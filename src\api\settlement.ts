import request from '@/utils/request'
import type {
  SettlementQueryDTO,
  SettlementCompleteDTO,
  SettlementInfoVO,
  SettlementSummaryVO,
  PageResponse,
  ApiResponse
} from '@/types/settlement'

// ==================== 商家端接口 (/merchant/settlement) ====================

/**
 * 查询商家回款汇总信息
 */
export const getMerchantSettlementSummary = (): Promise<ApiResponse<SettlementSummaryVO>> => {
  return request.get('/merchant/settlement/summary')
}

/**
 * 商家端分页查询回款订单
 */
export const getMerchantSettlementOrders = (params: SettlementQueryDTO): Promise<ApiResponse<PageResponse<SettlementInfoVO>>> => {
  return request.get('/merchant/settlement/orders', { params })
}

/**
 * 商家端按状态查询订单
 */
export const getMerchantSettlementOrdersByStatus = (
  status: number,
  params: SettlementQueryDTO
): Promise<ApiResponse<PageResponse<SettlementInfoVO>>> => {
  return request.get(`/merchant/settlement/orders/status/${status}`, { params })
}

/**
 * 商家端按账单周期查询订单
 */
export const getMerchantSettlementOrdersByCycle = (
  billingCycle: string,
  params: SettlementQueryDTO
): Promise<ApiResponse<PageResponse<SettlementInfoVO>>> => {
  return request.get(`/merchant/settlement/orders/cycle/${billingCycle}`, { params })
}



// ==================== 平台端接口 (/admin/settlement) ====================

/**
 * 查询所有商家汇总
 */
export const getAdminSettlementSummary = (params?: SettlementQueryDTO): Promise<ApiResponse<PageResponse<SettlementSummaryVO>>> => {
  return request.get('/admin/settlement/summary', { params })
}

/**
 * 平台端分页查询所有回款订单
 */
export const getAdminSettlementOrders = (params: SettlementQueryDTO): Promise<ApiResponse<PageResponse<SettlementInfoVO>>> => {
  return request.get('/admin/settlement/orders', { params })
}

/**
 * 查询待回款订单
 */
export const getAdminPendingSettlementOrders = (params?: SettlementQueryDTO): Promise<ApiResponse<PageResponse<SettlementInfoVO>>> => {
  return request.get('/admin/settlement/pending', { params })
}

/**
 * 按商家查询回款信息
 */
export const getAdminSettlementOrdersBySeller = (
  sellerId: number,
  params: SettlementQueryDTO
): Promise<ApiResponse<PageResponse<SettlementInfoVO>>> => {
  return request.get(`/admin/settlement/seller/${sellerId}`, { params })
}

/**
 * 标记回款完成
 */
export const completeSettlement = (data: SettlementCompleteDTO): Promise<ApiResponse<void>> => {
  return request.put('/admin/settlement/complete', data)
}

/**
 * 手动更新回款状态
 */
export const updateSettlementStatus = (): Promise<ApiResponse<void>> => {
  return request.post('/admin/settlement/update-status')
}

/**
 * 为订单创建回款信息
 */
export const createSettlementForOrder = (orderId: number): Promise<ApiResponse<void>> => {
  return request.post(`/admin/settlement/create/${orderId}`)
}

/**
 * 确认已回款（平台端操作）
 */
export const confirmSettlement = (id: number, remark?: string): Promise<ApiResponse<string>> => {
  const params = remark ? { remark } : {}
  return request.put(`/admin/settlement/confirm/${id}`, null, { params })
}




